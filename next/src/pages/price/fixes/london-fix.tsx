import dayjs from 'dayjs'
import type { GetServerSideProps } from 'next'
import type { FC } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import LondonFixPageCell from '~/src/components-metals/LondonFixPageCell/LondonFixPageCell'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import Layout from '~/src/components/Layout/Layout'
import { metals } from '~/src/lib/metals-factory.lib'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import * as timestamps from '~/src/utils/timestamps'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c?.res,
    queries: [
      metals.londonFixByYear({
        variables: {
          year: dayjs.unix(timestamps.current()).year().toString(),
          currency: 'USD',
        },
        options: {
          enabled: true,
        },
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const LondonFix: FC = () => (
  <Layout title="London Fix">
    <ErrBoundary>
      <LondonFixPageCell />
    </ErrBoundary>
    <AdvertisingSlot
      id={'footer'}
      className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
    />
  </Layout>
)
export default LondonFix
