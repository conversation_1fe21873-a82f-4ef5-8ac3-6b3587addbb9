import type { User } from 'firebase/auth'
import type UserClaims from '~/src/services/coraltalk/UserClaims'
import { getUsername } from '~/src/services/firebase/service'

/**
 * Get the Coral Talk SSO token for the user
 *
 * @param user
 */
async function getToken(user: User) {
  // Prepare the user claims
  const coralUser: UserClaims = {
    id: user.uid,
    email: user.email,
    username: (await getUsername(user.email)) || user.displayName,
    badges: ['subscriber'],
  }

  // Generate the Coral Talk SSO token
  return await getFromServer(coralUser)
}

/**
 * Get the Coral Talk SSO token from the server
 *
 * @param userClaims
 */
async function getFromServer(userClaims: UserClaims) {
  try {
    // Get the token from the server
    const response = await fetch('/api/coraltalk/getToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userClaims),
    })

    // Check if the response is ok
    if (!response.ok) {
      throw new Error(`Error: ${response.status}`)
    }

    // Parse the response
    const data = await response.json()
    return data.token
  } catch (error) {
    console.error('Failed to obtain token:', error)
    return null
  }
}

export { getToken }
