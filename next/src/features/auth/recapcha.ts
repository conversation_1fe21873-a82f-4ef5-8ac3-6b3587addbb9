import { useGoogleReCaptcha } from 'react-google-recaptcha-v3'

/**
 * Verify the reCAPTCHA token with the server.
 *
 * @param token
 */
export async function verifyRecaptcha(token: string) {
  const verificationResult = await fetch('/api/recaptcha/check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ token }),
  })

  const resultData = await verificationResult.json()

  if (resultData.success) {
    return true
  }

  console.error('Recaptcha verification failed')
  return false
}

/**
 * Custom hook to verify the user with reCAPTCHA.
 */
export const useRecaptchaVerification = () => {
  // Get the executeRecaptcha function from the hook
  const { executeRecaptcha } = useGoogleReCaptcha()

  return async () => {
    // If the recaptcha function is not available, return false
    if (!executeRecaptcha) {
      return false
    }

    // Execute the recaptcha function to get the token
    const recaptchaToken = await executeRecaptcha('login')

    // If the token is not available, return false
    if (!recaptchaToken) {
      return false
    }

    // Verify the token with the server
    return await verifyRecaptcha(recaptchaToken)
  }
}
