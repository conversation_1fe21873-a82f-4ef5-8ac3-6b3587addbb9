import { handleSurveyMonkeyScript } from '~/src/features/SurveyMonkey/SurveyMonkey'

/**
 * Handle scripts in the HTML
 * Extracts scripts from the HTML and returns the new HTML
 * and the extracted scripts
 *
 * @param html
 */
const handleScripts = (html: string) => {
  let newHtml = html

  // Regex for extracting scripts
  const scriptSrcRegex = /<script\b[^>]*src="([^"]+)"[^>]*><\/script>/gi
  const inlineScriptRegex = /<script\b[^>]*>([\s\S]*?)<\/script>/gi

  // Extracted scripts
  const extractedScripts = []
  const extractedInlineScripts = []

  // Group of regexes
  const regexGroup = [
    {
      regex: scriptSrcRegex,
      isURL: true,
    },
    {
      regex: inlineScriptRegex,
      isURL: false, // Inline script
    },
  ]

  // Loop through the regex group
  for (const regex of regexGroup) {
    // Extract scripts from the HTML
    newHtml = newHtml.replace(regex.regex, (_, src) => {
      // Process Services
      const { serviceHTML, serviceScriptInline, serviceScriptURL } =
        handleServices(src, regex.isURL)

      // Add the script to the extractedScripts array
      regex.isURL
        ? serviceScriptURL
          ? extractedScripts.push(serviceScriptURL)
          : null
        : serviceScriptInline
          ? extractedInlineScripts.push(serviceScriptInline)
          : null

      return serviceHTML
    })
  }

  return {
    html: newHtml,
    scripts: extractedScripts,
    inlineScripts: extractedInlineScripts,
  }
}

/**
 * Handle the services and allowed scripts
 *
 * @param scriptElement
 * @param isURL
 */
const handleServices = (scriptElement: string, isURL = false) => {
  // Handle SurveyMonkey script
  const { serviceHTML, serviceScriptInline, serviceScriptURL } =
    handleSurveyMonkeyScript(scriptElement, isURL)

  return {
    serviceHTML,
    serviceScriptInline,
    serviceScriptURL,
  }
}

export default handleScripts
