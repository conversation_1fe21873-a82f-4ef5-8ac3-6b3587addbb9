/**
 * UserClaims is the interface for the user claims that are returned from the
 * authentication service. This is used to determine the user's role and other
 * information.
 *
 * @param id - The user's unique identifier.
 * @param email - The user's email address.
 * @param username - The user's username.
 * @param badges - The user's badges.
 * @param role - The user's role.
 * @param url - The user's URL.
 */
export default interface UserClaims {
  id: string
  email: string
  username: string
  badges?: string[]
  // This should be updated with kitco-auth roles
  role?:
    | 'SUPER_ADMIN'
    | 'AUDIO_CURATOR'
    | 'IMAGE_CURATOR'
    | 'VIDEO_CURATOR'
    | 'DRUPAL_CONTENT_EDITOR'
    | 'DRUPAL_CONTENT_REPORTER'
    | 'DRUPAL_CONTENT_CONTRIBUTOR'
    | 'USER'
  url?: string
}
