import clsx from 'clsx'
import type { GetServerSideProps, NextPage } from 'next'
import Link from 'next/link'
import Script from 'next/script'
import { type FC, Fragment, type ReactNode, useCallback } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { FeaturedMedia } from '~/src/components-news/Article/ArticleFeaturedMedia.component'
import ArticleAudioPlayer from '~/src/components-news/ArticleAudioPlayer/ArticleAudioPlayer'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardMobile } from '~/src/components-news/ArticleTeasers/TeaserCardMobile'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import AuthorDetails from '~/src/components-news/AuthorDetails/AuthorDetails'
import { NoRelatedArticlesShowLatestNews } from '~/src/components-news/RelatedNews/NoRelatedArticlesShowLatestNews'
import CommentDrawer from '~/src/components/Comment/CommentDrawer'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { AuthorImage } from '~/src/components/image-with-fallback/image-with-fallback.component'
import { NewsCategoryTitleDetailPage } from '~/src/components/news-category/news-category.component'
import NewsMeta from '~/src/components/news/meta'
import SocialsSideBar from '~/src/components/socials/SocialsSideBar'
import { SocialsKitco } from '~/src/components/socials/socials-kitco.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import { TagLink } from '~/src/components/tag-link/tag-link.component'
import type {
  ArticleTeaserFragmentFragment,
  Author,
  NewsOtwListQuery,
  OffTheWire,
  OffTheWireFragmentFragment,
  Tag,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { articleDate } from '~/src/utils/article-date.util'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import useRecordView from '~/src/utils/useRecordView'
import useScreenSize from '~/src/utils/useScreenSize'
import styles from './article-alias.module.scss'

interface ArticleProps {
  articleData: OffTheWire
}

interface AuthorProps {
  authorData: Author
  publishDate: string
  updateDate: string
}

export const getServerSideProps: GetServerSideProps = async (c) => {
  const slugs = c.query.alias as Array<string>
  const auHash = c?.query?.auHash ?? ('' as any)
  const fullSlug = `/news/off-the-wire/${slugs.join('/')}`

  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      news.nodeByUrlAlias({
        variables: { urlAlias: fullSlug, auHash },
        options: {
          enabled: true,
        },
      }),
      news.newsTrending({ variables: { limit: 10 } }),
    ],
  })

  // cache control one hour
  c.res.setHeader('Cache-Control', 's-maxage=3600, stale-while-revalidate')

  return {
    props: {
      dehydratedState,
      urlAlias: fullSlug,
      auHash,
    },
  }
}

const listAuthorStr = (data: OffTheWire) => {
  const authorData = data?.author
  const unifyAuthors: <AUTHORS>

  let listAuthorString = 'By '
  unifyAuthors?.forEach((x, idx) => {
    listAuthorString += `${x?.name}${
      idx !== unifyAuthors?.length - 1 ? ' and ' : ''
    }`
  })

  return listAuthorString
}

const ArticlePage: NextPage<{ urlAlias: string; auHash?: string }> = ({
  urlAlias,
  auHash,
}) => {
  const { data } = kitcoQuery(
    news.nodeByUrlAlias({
      variables: { urlAlias, auHash },
      options: {
        enabled: true,
      },
    }),
  )

  useRecordView(Boolean(data), data?.nodeByUrlAlias?.id)

  const articleData = data?.nodeByUrlAlias as OffTheWire
  if (!articleData)
    return (
      <LayoutNewsLanding title={`${data?.nodeByUrlAlias?.title}  | Kitco News`}>
        <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
          <h1 className="text-xl">Not Found!</h1>
        </div>
      </LayoutNewsLanding>
    )

  const { isMobile } = useScreenSize()

  const pFirst = StrippedString(articleData?.body)?.replace('&nbsp;', ' ')

  return (
    <LayoutNewsLanding title={`${data?.nodeByUrlAlias?.title} | Kitco News`}>
      <NewsMeta
        title={data?.nodeByUrlAlias?.title}
        description={pFirst}
        image={
          articleData?.teaserImage?.detail?.default?.srcset ??
          articleData?.image?.detail?.default?.srcset
        }
        authorTwitter={articleData?.author?.twitterId}
      />
      {isMobile && <ArticleMobile articleData={articleData} />}
      {!isMobile && <ArticleDesktopAndTablet articleData={articleData} />}
    </LayoutNewsLanding>
  )
}

export default ArticlePage

const ArticleDesktopAndTablet: FC<ArticleProps> = ({ articleData }) => {
  return (
    <>
      <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <div className="flex w-[190px]">
          <NewsCategoryTitleDetailPage category="news/off-the-wire" />
        </div>
        <div className="w-[calc(100%_-_190px)] pl-10">
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {articleData?.title}
          </h1>
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <LeftContent articleData={articleData} />
        <ArticleContent articleData={articleData} />
      </div>
    </>
  )
}

const LeftContent: FC<ArticleProps> = ({ articleData }) => {
  return (
    <div className="w-[190px]">
      <AuthorBlock
        authorData={articleData?.author}
        publishDate={articleData?.createdAt}
        updateDate={articleData?.updatedAt}
      />
      {!!articleData?.source && (
        <>
          <Spacer className="h-[30px]" />
          <SourceBlock
            name={articleData?.source?.name}
            description={articleData?.source?.description}
            subtitle={articleData?.source?.subtitle}
          />
        </>
      )}
      <AudioPlayer articleData={articleData} />
      <Spacer className="h-[30px]" />
      <AdvertisingSlot
        id={'left-rail-1'}
        className={'mx-auto min-h-[600px] w-[160px] no-print'}
      />
    </div>
  )
}

const SideBarArticle: FC<ArticleProps> = ({ articleData }) => {
  return (
    <>
      <div
        className="sticky top-3 m-0 hidden flex-col justify-start self-start border-0 p-0
        pr-4 align-baseline text-black md:flex md:items-center
        md:gap-16"
      >
        <aside
          className="m-0 flex w-full flex-col items-center justify-start
          gap-8 border-0 p-0 align-baseline"
        >
          <SocialsSideBar
            hidePrint={true}
            listAuthorStr={listAuthorStr(articleData)}
          >
            <CommentDrawer
              className=""
              category="off-the-wire"
              storyID={articleData.id}
              elementID="OffTheWireCommentBoxDesktop"
            />
          </SocialsSideBar>
        </aside>
      </div>
    </>
  )
}

const ArticleContent: FC<ArticleProps> = ({ articleData }) => {
  return (
    <div className="flex w-auto flex-row-reverse gap-4 md:w-[calc(100%_-_190px)] md:pl-10">
      <article>
        <div className="flex justify-items-end gap-10 lg:grid-cols-3">
          <div className="col-span-1 lg:col-span-2">
            <BulletNews summaryBullets={articleData?.summaryBullets} />
            <FeaturedMedia articleData={articleData} />
            <BodyBlock body={articleData?.body} />
            <Spacer className="h-6" />
            <AuthorDetails authorData={articleData?.author} />
            {articleData?.tags?.length ? (
              <>
                <Spacer className="h-6" />
                <Tags data={articleData?.tags} />
              </>
            ) : (
              <></>
            )}
            <Spacer className="h-6" />
            <Disclaimer />
            <Spacer className="h-6" />
            {/* <ArticleComment />
            <Spacer className="h-6" /> */}
            <div
              className="dianomi_context min-h-[702px] w-full tablet:min-h-[400px]"
              data-dianomi-context-id="246"
            />
            <Script
              src="https://www.dianomi.com/js/contextfeed.js"
              id="dianomi_context_script"
            />
            <RelatedArticles currentNodeId={articleData?.id} />
          </div>
          <aside className="hidden w-[300px] lg:col-span-1 lg:block">
            <AdvertisingSlot
              id={'right-rail-1'}
              className={'mx-auto mb-10 min-h-[250px] w-[300px] no-print'}
            />
            <div className="rounded-md border border-ktc-borders p-5">
              <TrendingNowSection />
            </div>
          </aside>
        </div>
      </article>
      <SideBarArticle articleData={articleData} />
    </div>
  )
}

const ArticleMobile: FC<ArticleProps> = ({ articleData }) => {
  return (
    <div className="mx-auto flex w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
      <div className="w-full">
        <NewsCategoryTitleDetailPage category="news/off-the-wire" />
        <article>
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {articleData?.title}
          </h1>
          <div className="grid grid-cols-1 gap-10 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <BulletNews summaryBullets={articleData?.summaryBullets} />
              <AuthorBlockMobile
                authorData={articleData?.author}
                publishDate={articleData?.createdAt}
                updateDate={articleData?.updatedAt}
              />
              {!!articleData?.source && (
                <>
                  <Spacer className="h-[30px]" />
                  <SourceBlock
                    name={articleData?.source?.name}
                    description={articleData?.source?.description}
                    subtitle={articleData?.source?.subtitle}
                  />
                </>
              )}
              <Spacer className="h-[30px]" />
              <div className="flex justify-between">
                <AudioPlayer articleData={articleData} isMobile={true} />
                <SocialsKitco
                  className="mx-3 w-full justify-evenly"
                  hidePrint={true}
                  listAuthorStr={listAuthorStr(articleData)}
                />
              </div>
              <Spacer className="h-[30px]" />
              <FeaturedMedia articleData={articleData} />
              <BodyBlock body={articleData?.body} />
              <Spacer className="h-6" />
              <AuthorDetails authorData={articleData?.author} />
              <Spacer className="h-6" />
              <Spacer className="h-6" />
              <LowerShareBlock articleData={articleData} />
              <Spacer className="h-6" />
              <Disclaimer />
              <Spacer className="h-6" />
              {/* <ArticleComment />
              <Spacer className="h-6" /> */}
              <div
                className="dianomi_context min-h-[702px] w-full tablet:min-h-[400px]"
                data-dianomi-context-id="246"
              />
              <Script
                src="https://www.dianomi.com/js/contextfeed.js"
                id="dianomi_context_script"
              />
              <RelatedArticles currentNodeId={articleData?.id} />
            </div>
          </div>
        </article>
      </div>
    </div>
  )
}

const AuthorBlock: FC<AuthorProps> = ({
  authorData,
  publishDate,
  updateDate,
}) => {
  const unifyAuthors: <AUTHORS>
  const isMultiAuthor: boolean = unifyAuthors?.length >= 2

  return (
    <>
      <div className="mt-5">
        <div className="flex w-full items-center -space-x-[0.75em] pb-2.5">
          {unifyAuthors?.map((x, idx) => (
            <AuthorImage
              src={x?.imageUrl}
              className={clsx(
                'h-20 w-20 rounded-full object-cover',
                isMultiAuthor ? 'border-2 border-white' : undefined,
              )}
              style={{
                zIndex: 100 - idx,
              }}
              urlAlias={x?.urlAlias}
              key={x?.id + idx}
            />
          ))}
        </div>
        <div>
          <h6 className="font-medium tracking-[0.0168em]">
            <span>By&nbsp;</span>
            {unifyAuthors?.map((x, idx) => (
              <Fragment key={idx}>
                <Link
                  href={x?.urlAlias}
                  className="text-sm font-bold text-[#373737]"
                  key={x?.id + idx}
                >
                  {x?.name}
                </Link>
                {idx !== unifyAuthors?.length - 1 ? (
                  <span>&nbsp;and&nbsp;</span>
                ) : null}
              </Fragment>
            ))}
          </h6>
          <Spacer className="h-[30px]" />
          <div>
            <div className="text-xs font-bold text-ktc-desc-gray">
              Published:
            </div>
            <time className="text-xs text-ktc-gray">
              {articleDate(publishDate)}
            </time>
          </div>
          <div>
            <div className="mt-2 text-xs font-bold text-ktc-desc-gray">
              Updated:
            </div>
            <time className="text-xs text-ktc-gray">
              {articleDate(updateDate)}
            </time>
          </div>
        </div>
      </div>
    </>
  )
}

const AuthorBlockMobile: FC<AuthorProps> = ({
  authorData,
  publishDate,
  updateDate,
}) => {
  const unifyAuthors: <AUTHORS>
  const isMultiAuthor: boolean = unifyAuthors?.length >= 2

  return (
    <div className="flex justify-between">
      <div className="flex w-full -space-x-[0.75em]">
        {unifyAuthors?.map((x, idx) => (
          <AuthorImage
            src={x?.imageUrl}
            className={clsx(
              'h-10 w-10 rounded-full object-cover',
              isMultiAuthor ? 'border-2 border-white' : undefined,
            )}
            style={{
              zIndex: 100 - idx,
            }}
            urlAlias={x.urlAlias}
            key={x.id + idx}
          />
        ))}
        <div className="pl-6">
          <h6 className="text-[16px] font-normal leading-[23px] tracking-[0.0168em]">
            <span>By&nbsp;</span>
            {unifyAuthors?.map((x, idx) => (
              <Fragment key={idx}>
                <Link
                  href={x.urlAlias}
                  className="font-bold text-[#373737]"
                  key={x.id + idx}
                >
                  {x?.name}
                </Link>
                {idx !== unifyAuthors?.length - 1 ? (
                  <span>&nbsp;and&nbsp;</span>
                ) : null}
              </Fragment>
            ))}
          </h6>
          <div>
            <span className="font-normal text-ktc-date-gray">
              Published&nbsp;
            </span>
            <time className="font-normal text-ktc-date-gray">
              {articleDate(publishDate)}
            </time>
          </div>
          <div>
            <span className="font-normal text-ktc-date-gray">
              Updated&nbsp;
            </span>
            <time className="font-normal text-ktc-date-gray">
              {articleDate(updateDate)}
            </time>
          </div>
        </div>
      </div>
    </div>
  )
}

const LowerShareBlock: FC<{ articleData: OffTheWire }> = ({ articleData }) => {
  return (
    <div className="flex w-full flex-col items-center gap-3">
      <h3 className="text-[16px] font-bold underline">Share</h3>
      <SocialsKitco
        className="gap-4"
        listAuthorStr={listAuthorStr(articleData)}
      />
    </div>
  )
}

const SourceBlock: FC<{
  name: string
  description: string
  subtitle: string
}> = ({ name, description, subtitle }) => (
  <div className="flex flex-col items-start border border-gray-300 bg-[#f8f8f8] p-2.5 text-base leading-5">
    <h5>
      <div className="font-bold">{name}</div>
      <div className="font-normal">{subtitle}</div>
    </h5>
    <div
      className="pt-2 text-xs font-normal text-ktc-desc-gray "
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
)

const Disclaimer = () => {
  return (
    <div className="mb-10 text-[10px] text-xs leading-[14px] text-ktc-gray">
      <span className="font-bold text-ktc-desc-gray">Disclaimer:&nbsp;</span>
      The views expressed in this article are those of the author and may not
      reflect those of Kitco Metals Inc. The author has made every effort to
      ensure accuracy of information provided; however, neither Kitco Metals
      Inc. nor the author can guarantee such accuracy. This article is strictly
      for informational purposes only. It is not a solicitation to make any
      exchange in commodities, securities or other financial instruments. Kitco
      Metals Inc. and the author of this article do not accept culpability for
      losses and/ or damages arising from the use of this publication.
    </div>
  )
}

const RelatedArticles: FC<{
  currentNodeId: number
}> = ({ currentNodeId }) => {
  const { data } = kitcoQuery(
    news.newsOTWList({
      variables: {
        limit: 50,
        offset: 0,
      },
      options: {
        enabled: true,
        select: useCallback((d: NewsOtwListQuery) => {
          const filteredArr = d?.nodeList.items?.filter(
            (obj) => Object.keys(obj).length !== 0,
          )
          return {
            ...d,
            nodeList: {
              total: d?.nodeList?.total,
              items: [...filteredArr],
            },
          }
        }, []),
      },
    }),
  )

  function filterCurrentFromData() {
    return data?.nodeList?.items
      ?.filter((x: OffTheWireFragmentFragment) => x.id !== currentNodeId)
      .slice(0, 3)
  }

  const { isMobile } = useScreenSize()
  const articles = filterCurrentFromData()

  const ShowTeaserCard = () => {
    return (
      <>
        {articles?.map((article: any) => {
          if (!isMobile) {
            return <TeaserCard key={article.id} node={article} size="sm" />
          }

          return <TeaserCardMobile key={article.id} node={article} size="sm" />
        })}
      </>
    )
  }

  const classTeaserCard = () => {
    if (isMobile) {
      return 'grid grid-row-3 gap-10'
    }

    return 'grid grid-cols-3 gap-10'
  }

  return (
    <aside>
      <h3 className="font-mulish mb-5 border-b border-ktc-borders pb-2.5 text-2xl">
        Related Articles
      </h3>
      {!articles?.length ? (
        <NoRelatedArticlesShowLatestNews
          currentNodeId={currentNodeId}
          classTeaserCard={classTeaserCard()}
        />
      ) : (
        <div className={classTeaserCard()}>
          <ShowTeaserCard />
        </div>
      )}
    </aside>
  )
}

function BodyBlock({ body }: { body: OffTheWire['body'] }): ReactNode {
  return (
    <div
      className={clsx(
        'relative text-base',
        styles.articleBodyStyles,
        styles.articleWrapper,
      )}
      dangerouslySetInnerHTML={{
        __html: body,
      }}
    />
  )
}

const TrendingNowSection: FC = () => {
  const { data } = kitcoQuery(news.newsTrending({ variables: { limit: 10 } }))

  return (
    <div className="flex flex-col">
      <h3
        className={
          'border-b border-ktc-borders pb-2.5 text-[20px] leading-[26px]'
        }
      >
        <span>Trending News</span>
      </h3>
      <div className="flex flex-grow flex-col justify-between">
        {data?.nodeListTrending
          ?.slice(0, 5)
          .map((x: ArticleTeaserFragmentFragment) => {
            return (
              <div className="mt-5 flex" key={x.id}>
                <TeaserTextOnly
                  key={x?.id}
                  node={x}
                  hideSummary={true}
                  size={'sm'}
                  lineClamp="line-clamp-2"
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}

function BulletNews({ summaryBullets }: { summaryBullets: Array<string> }) {
  if (summaryBullets?.length > 0)
    return (
      <>
        {summaryBullets?.map((x, idx) => (
          <h2
            className={clsx(
              'pb-2 pl-4 text-[16px] font-semibold',
              styles.articleBulletNews,
            )}
            key={idx}
          >
            {x}
          </h2>
        ))}
        <Spacer className="h-4" />
      </>
    )

  return null
}

const Tags: FC<{ data: Tag[] }> = ({ data }) => {
  return (
    <div className="rounded-xl border border-ktc-borders p-5">
      <h3 className="font-mulish pb-2 leading-5">
        <span>Tags:</span>
      </h3>
      <div className="flex flex-wrap gap-2">
        {data?.map((t) => (
          <TagLink key={t.id} href={t.urlAlias} name={t.name} />
        ))}
      </div>
    </div>
  )
}

const AudioPlayer: FC<{
  articleData: ArticleProps['articleData']
  isMobile?: boolean
}> = ({ articleData, isMobile = false }) => {
  if (!articleData?.audioTts?.assetUuid) return

  if (isMobile) {
    return (
      <div className="w-1/2">
        <ArticleAudioPlayer
          assetSnippetUuid={articleData?.audioTts?.assetUuid}
        />
      </div>
    )
  }

  return (
    <>
      <Spacer className="h-[30px]" />
      <ArticleAudioPlayer assetSnippetUuid={articleData?.audioTts?.assetUuid} />
    </>
  )
}
