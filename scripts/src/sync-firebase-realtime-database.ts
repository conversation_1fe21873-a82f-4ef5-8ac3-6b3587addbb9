import admin from 'firebase-admin'

/**
 * Export data from the given environment
 *
 * @param app - The Firebase app instance
 *
 * @returns A promise that resolves to the data from production
 */
async function exportData(app: admin.app.App): Promise<any> {
  const db = app.database()
  const ref = db.ref('/')
  const snapshot = await ref.once('value')
  return snapshot.val()
}

/**
 * Clear all data from the development environment
 *
 * @param app - The Firebase app instance
 *
 * @returns A promise that resolves when the data is cleared
 */
async function clearDevelopmentData(app: admin.app.App): Promise<void> {
  const db = app.database()
  const ref = db.ref('/')
  await ref.set(null)
  console.log('Cleared all data in development.')
}

/**
 * Import data into the development environment
 *
 * @param app - The Firebase app instance
 * @param data - The data to import into development
 */
async function importData(app: admin.app.App, data: any): Promise<void> {
  const db = app.database()
  const ref = db.ref('/')
  await ref.set(data)
  console.log('Imported data into development.')
}

/**
 * Main function to synchronize data from production to development
 */
export async function syncRealtimeDatabase(
  devApp: admin.app.App,
  prodApp: admin.app.App,
): Promise<void> {
  try {
    console.log('Exporting data from production...')
    const data = await exportData(prodApp)
    console.log('Data exported from production.')

    console.log('Clearing data in development...')
    await clearDevelopmentData(devApp)

    console.log('Importing data into development...')
    await importData(devApp, data)

    console.log('Synchronization process completed.')
  } catch (error: any) {
    console.error('Error in the process:', error.message)
  }
}
