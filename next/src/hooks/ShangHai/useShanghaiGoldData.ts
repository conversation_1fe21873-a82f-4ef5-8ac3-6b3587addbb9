import { useState } from 'react'
import { useCurrencyCNY } from '~/src/hooks/Currency/useCurrency'
import { metals } from '~/src/lib/metals-factory.lib'
import * as timestamps from '~/src/utils/timestamps'

/**
 * useShanghaiGoldData hook
 */
export const useShanghaiGoldData = () => {
  // Get currency atom
  const currency = useCurrencyCNY()

  // Get only the latest data
  const fetcher = metals.shanghaiFix({
    variables: {
      currency: 'USD',
      symbol: 'SHAU',
      limit: 1,
      timestamp: timestamps.current(),
    },
    options: { enabled: true },
  })

  // Initialize quoteDate
  const [quoteDate, setQuoteDate] = useState(undefined)

  /**
   * Fetcher function for currencies
   *
   * @param latestTimestamp
   */
  const fetcherCurrencies = (latestTimestamp: number) => {
    return metals.currencies({ variables: { timestamp: latestTimestamp } })
  }

  return {
    currency,
    fetcher,
    fetcherCurrencies,
    quoteDate,
    setQuoteDate,
  }
}
