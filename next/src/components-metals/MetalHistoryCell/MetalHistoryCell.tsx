import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import { useAtom } from 'jotai'
import { Suspense } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import { Query } from '~/src/components/Query/Query'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import {
  TimeSelect,
  yearReadAtom,
} from '~/src/components/year-select/year-select.component'
import type { GetKitcoFixPmv3Query } from '~/src/generated'
import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import useWeight from '~/src/hooks/Weight/useWeight'
import { metals } from '~/src/lib/metals-factory.lib'
import WeightType from '~/src/types/WeightSelect/WeightType'
import { renderFn } from '~/src/utils/SangHai/priceConversion'
import cs from '~/src/utils/cs'
import dates from '~/src/utils/dates'
import isOdd from '~/src/utils/isOdd'

export function MetalHistoryCell() {
  const currency = useCurrency()
  const [read] = useAtom(yearReadAtom)

  const fetcher = metals.getKitcoFixPMV3({
    variables: {
      currency: currency.symbol,
      year: Number.parseInt(read.all[read.selectedKey].humanReadable),
    },
    options: {},
  })

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {({ data, isFetching }) => {
            return <KitcoFixHistory isLoading={isFetching} data={data} />
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

dayjs.extend(isoWeek)
function KitcoFixHistory({
  data,
  isLoading,
}: {
  isLoading: boolean
  data: GetKitcoFixPmv3Query
}) {
  const weight = useWeight(WeightType.PreciousMetals)
  function isMonday(timestamp: number) {
    return dayjs.unix(timestamp).day() === 1
  }

  // exclude saturdays and sundays
  function isSaturdayOrSunday(timestamp: number) {
    return (
      dayjs.unix(timestamp).day() === 0 || dayjs.unix(timestamp).day() === 6
    )
  }

  return (
    <>
      <section className="pt-10">
        <div className="grid grid-cols-5 ">
          <div
            className={cs([
              'flex items-center justify-center',
              'border border-t-0 border-ktc-date-gray',
              'px-4 text-xl font-semibold',
            ])}
          >
            <TimeSelect />
          </div>
          {['Gold', 'Silver', 'Platinum', 'Palladium'].map((x, idx) => (
            <div
              key={idx}
              className={cs([
                'border border-t-0 border-ktc-date-gray',
                'flex items-center justify-center text-xl font-semibold',
              ])}
            >
              <span className="text-xs tablet:text-xl">{x}</span>
            </div>
          ))}
        </div>

        {data ? (
          <>
            <div
              className={cs([
                'grid grid-cols-5 text-lg',
                'h-[400px] overflow-y-scroll',
                !isLoading ? 'undefined' : 'opacity-50',
              ])}
            >
              <div className="col-span-2">
                {data?.gold?.results?.map((x, idx) =>
                  isSaturdayOrSunday(x.timestamp) ? null : (
                    <div
                      key={`gold:${x.timestamp}`}
                      className={cs([
                        'py-1',
                        'grid grid-cols-2 border-b border-ktc-date-gray',
                        isOdd(idx) ? 'bg-ktc-date-gray/20' : 'bg-transparent',
                        isMonday(x.timestamp) ? 'border-b-8' : undefined,
                      ])}
                    >
                      <div>
                        <time className="block text-sm w-[90px] mx-auto pt-1 text-ktc-desc-gray">
                          {dates.fmtUnix(x.timestamp, 'MMM DD, YYYY')}
                        </time>
                      </div>
                      <div>
                        <span className="block mx-auto w-[70px]">
                          {renderFn(weight, x?.bid)}
                        </span>
                      </div>
                    </div>
                  ),
                )}
              </div>

              <div>
                {data?.silver?.results?.map((x, idx) =>
                  isSaturdayOrSunday(x.timestamp) ? null : (
                    <DataCell
                      id={x.timestamp}
                      idx={idx}
                      key={`silver:${x.timestamp}`}
                      className="w-[50px]"
                    >
                      {renderFn(weight, x.bid)}
                    </DataCell>
                  ),
                )}
              </div>

              <div>
                {data?.platinum?.results?.map((x, idx) =>
                  isSaturdayOrSunday(x.timestamp) ? null : (
                    <DataCell
                      id={x.timestamp}
                      idx={idx}
                      key={`platinum:${x.timestamp}`}
                    >
                      {renderFn(weight, x.bid)}
                    </DataCell>
                  ),
                )}
              </div>

              <div>
                {data?.palladium?.results?.map((x, idx) =>
                  isSaturdayOrSunday(x.timestamp) ? null : (
                    <DataCell
                      id={x.timestamp}
                      idx={idx}
                      key={`palladium:${x.timestamp}`}
                    >
                      {renderFn(weight, x.bid)}
                    </DataCell>
                  ),
                )}
              </div>
            </div>
          </>
        ) : (
          <>
            {[1, 2, 3, 4, 5].map((_, idx) => (
              <div
                key={idx}
                className={cs([
                  'grid grid-cols-5 text-lg',
                  'my-3 justify-items-center',
                ])}
              >
                <SkeletonTable />
                <SkeletonTable />
                <SkeletonTable />
                <SkeletonTable />
                <SkeletonTable />
              </div>
            ))}
          </>
        )}
      </section>
    </>
  )
}

function DataCell(props: {
  id: number
  idx: number
  className?: string
  children: React.ReactNode
}) {
  function isMonday(timestamp: number) {
    return dayjs.unix(timestamp).day() === 1
  }

  return (
    <div
      className={cs([
        'py-1',
        'border-b border-ktc-date-gray',
        isOdd(props.idx) ? 'bg-ktc-date-gray/20' : 'bg-transparent',
        isMonday(props.id) ? 'border-b-8' : undefined,
      ])}
    >
      <data
        className={cs([
          'mx-auto block w-[70px]',
          !props?.className ? undefined : props.className,
        ])}
      >
        {props.children || '-'}
      </data>
    </div>
  )
}
