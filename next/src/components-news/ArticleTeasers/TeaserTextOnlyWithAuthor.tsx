import Image from 'next/image'
import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserCardProps } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { Spacer } from '~/src/components/spacer/spacer.component'
import cs from '~/src/utils/cs'

export const TeaserTextOnlyWithAuthor: FC<TeaserCardProps> = ({
  node,
  size,
  hideCategory,
  hideSummary,
  classTitle,
}) => {
  const sizeCSS = {
    sm: 'text-[16px] leading-[130%]',
    md: 'text-[20px] leading-[130%]',
    lg: 'text-[24px] leading-[130%]',
    xl: 'text-[24px] leading-[130%]',
    xxl: 'text-[34px] leading-[110%]',
  }

  return (
    <div className="w-full cursor-pointer">
      {!hideCategory && (
        <>
          <CategoryLink
            urlAlias={node?.category?.urlAlias}
            text={node?.category?.name}
          />
          <div className="h-2 bg-transparent" />
        </>
      )}
      <Link href={node?.urlAlias ?? '/'}>
        <h3
          className={cs([sizeCSS[size], classTitle, 'mb-2 text-kitco-black'])}
        >
          {node?.teaserHeadline ?? node?.title}
        </h3>
        {!hideSummary ? (
          <>
            <div
              className="summary"
              dangerouslySetInnerHTML={{
                __html:
                  node?.teaserSnippet ?? node?.bodyWithEmbeddedMedia?.value,
              }}
            />
            <Spacer className="h-2" />
          </>
        ) : null}
      </Link>

      <Link href={node?.author?.urlAlias ?? '/'}>
        <>
          <div id="authorContainer" className="flex md:items-center">
            <div className="mr-3">
              <Image
                src={node?.author?.imageUrl ?? '/default-avatar.svg'}
                alt={`Photo of ${node?.author?.name}`}
                width={30}
                height={30}
                className="h-[30px] w-[30px] rounded-full bg-[#f7f7f7] object-cover"
                sizes="(max-width: 767px) 35px, 70px"
              />
            </div>
            <div className="block">
              <p className="font-bold leading-5 text-ktc-category">
                {node?.author?.name}
              </p>
              <DateStamp stamp={node?.createdAt} />
            </div>
          </div>
        </>
      </Link>
    </div>
  )
}
