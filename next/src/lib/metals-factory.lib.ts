import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  AllMetalsQuoteQuery,
  AllMetalsQuoteQueryVariables,
  BaseMetalsQuery,
  BaseMetalsQueryVariables,
  CurrenciesQuery,
  CurrenciesQueryVariables,
  ExchangeRatesTableQueryQuery,
  GetKitcoFixPmv3Query,
  GetKitcoFixPmv3QueryVariables,
  GetNivoData30DaysQuery,
  GetNivoData30DaysQueryVariables,
  GetNivoDataQuery,
  GetNivoDataQueryVariables,
  GoldIndexWidgetQuery,
  GoldIndexWidgetQueryVariables,
  GoldRatiosQuery,
  GoldRatiosQueryVariables,
  LondonFixAndShanghaiFixQuery,
  LondonFixAndShanghaiFixQueryVariables,
  LondonFixByYearQuery,
  LondonFixByYearQueryVariables,
  LondonFixDynamicQuery,
  LondonFixDynamicQueryVariables,
  LondonFixQuery,
  LondonFixQueryVariables,
  MetalAndCurrenciesQuery,
  MetalAndCurrenciesQueryVariables,
  MetalHistoryQuery,
  MetalHistoryQueryVariables,
  MetalMonthAnnualQuery,
  MetalMonthAnnualQueryVariables,
  MetalPointsInTimeQuery,
  MetalPointsInTimeQueryVariables,
  MetalQuoteQuery,
  MetalQuoteQueryVariables,
  ShanghaiFixByYearQuery,
  ShanghaiFixByYearQueryVariables,
  ShanghaiFixQuery,
  ShanghaiFixQueryVariables,
  SilverPgmQuery,
  SilverPgmQueryVariables,
} from '~/src/generated'
import type QueryArgs from '~/src/types/QueryArgs'
import { graphs } from '../services/database/fetcher'
import { excludeTimestampFromCacheKey } from '../utils/exclude-timestamp-from-cache-key.util'
import { refetchInterval } from '../utils/timestamps'
import {
  forexFragment,
  londonQuoteFragment,
  metalFragment,
  metalQuoteFragment,
} from './metals-fragments.graphql'

export const metals = {
  metalQuote: (
    args: QueryArgs<MetalQuoteQueryVariables, MetalQuoteQuery>,
  ): UseQueryOptions<MetalQuoteQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalQuote', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query MetalQuote(
              $symbol: String!
              $currency: String!
              $timestamp: Int
            ) {
              GetMetalQuoteV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  allMetalsQuote: (
    args: QueryArgs<AllMetalsQuoteQueryVariables, AllMetalsQuoteQuery>,
  ): UseQueryOptions<AllMetalsQuoteQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: [args.variables.currency, 'allMetalsQuote'],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query AllMetalsQuote($currency: String!, $timestamp: Int) {
              gold: GetMetalQuoteV3(
                symbol: "AU"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              silver: GetMetalQuoteV3(
                symbol: "AG"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              platinum: GetMetalQuoteV3(
                symbol: "PT"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              palladium: GetMetalQuoteV3(
                symbol: "PD"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              rhodium: GetMetalQuoteV3(
                symbol: "RH"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  goldSilverPlatinumPalladium: (
    args: QueryArgs<GoldIndexWidgetQueryVariables, GoldIndexWidgetQuery>,
  ): UseQueryOptions<GoldIndexWidgetQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['goldSilverPlatinumPalladium', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query GoldSilverPlatinumPalladium(
              $currency: String!
              $timestamp: Int
            ) {
              gold: GetMetalQuoteV3(
                symbol: "AU"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              silver: GetMetalQuoteV3(
                symbol: "AG"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              platinum: GetMetalQuoteV3(
                symbol: "PT"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              palladium: GetMetalQuoteV3(
                symbol: "PD"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  baseMetals: (
    args: QueryArgs<BaseMetalsQueryVariables, BaseMetalsQuery>,
  ): UseQueryOptions<BaseMetalsQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['baseMetals', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query BaseMetals($timestamp: Int, $currency: String!) {
              AluminumPrice: GetMetalQuote(
                symbol: "AL"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              CopperPrice: GetMetalQuoteV3(
                symbol: "CU"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              NickelPrice: GetMetalQuoteV3(
                symbol: "NI"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              ZincPrice: GetMetalQuoteV3(
                symbol: "ZN"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              LeadPrice: GetMetalQuoteV3(
                symbol: "PB"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              Uranium: GetMetalQuoteV3(
                symbol: "UR"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  metalPointsInTime: (
    args: QueryArgs<MetalPointsInTimeQueryVariables, MetalPointsInTimeQuery>,
  ): UseQueryOptions<MetalPointsInTimeQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalPointsInTime', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query MetalPointsInTime(
              $now: Int
              $thirtyday: Int
              $oneyear: Int
              $sixmonths: Int
              $symbol: String!
            ) {
              now: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $now
                currency: "USD"
              ) {
                ...MetalFragment
              }
              thirtyday: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $thirtyday
                currency: "USD"
              ) {
                ...MetalFragment
              }
              sixmonths: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $sixmonths
                currency: "USD"
              ) {
                ...MetalFragment
              }
              oneyear: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $oneyear
                currency: "USD"
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  metalHistory: (
    args: QueryArgs<MetalHistoryQueryVariables, MetalHistoryQuery>,
  ): UseQueryOptions<MetalHistoryQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalHistory', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalQuoteFragment}
            query MetalHistory(
              $symbol: String!
              $startTime: Int!
              $endTime: Int!
              $groupBy: String
              $offset: Int
              $limit: Int
              $currency: String!
            ) {
              GetMetalHistoryV3(
                symbol: $symbol
                startTime: $startTime
                endTime: $endTime
                groupBy: $groupBy
                limit: $limit
                offset: $offset
                currency: $currency
              ) {
                currency
                symbol
                name
                results {
                  ...MetalQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  getKitcoFixPMV3: (
    args: QueryArgs<GetKitcoFixPmv3QueryVariables, GetKitcoFixPmv3Query>,
  ): UseQueryOptions<GetKitcoFixPmv3Query> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['getKitcoFixPMV3', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalQuoteFragment}
            query GetKitcoFixPMV3($currency: String!, $year: Int!) {
              gold: GetKitcoFixPreciousMetalsV3(
                symbol: "AU"
                currency: $currency
                year: $year
              ) {
                currency
                symbol
                name
                results {
                  ...MetalQuoteFragment
                }
              }
              silver: GetKitcoFixPreciousMetalsV3(
                symbol: "AG"
                currency: $currency
                year: $year
              ) {
                currency
                symbol
                name
                results {
                  ...MetalQuoteFragment
                }
              }
              platinum: GetKitcoFixPreciousMetalsV3(
                symbol: "PT"
                currency: $currency
                year: $year
              ) {
                currency
                symbol
                name
                results {
                  ...MetalQuoteFragment
                }
              }
              palladium: GetKitcoFixPreciousMetalsV3(
                symbol: "PD"
                currency: $currency
                year: $year
              ) {
                currency
                symbol
                name
                results {
                  ...MetalQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  nivoChartData: (
    args: QueryArgs<GetNivoDataQueryVariables, GetNivoDataQuery>,
  ): UseQueryOptions<GetNivoDataQuery> => {
    return {
      ...args.options,
      refetchInterval,
      retry: 3,
      retryDelay: 1000,
      queryKey: ['nivoChartData', args.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalQuoteFragment}
            query GetNivoData(
              $currency: String!
              $timestamp: Int!
              $symbol: String!
            ) {
              now: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $timestamp
                currency: $currency
              ) {
                ID
                symbol
                currency
                name
                results {
                  ...MetalQuoteFragment
                }
              }

              history: GetMetalChartDataV3(
                chartId: TODAY
                symbol: $symbol
                currency: $currency
              ) {
                symbol
                currency
                results {
                  ...MetalQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  nivoChartData30Days: (
    args: QueryArgs<GetNivoData30DaysQueryVariables, GetNivoData30DaysQuery>,
  ): UseQueryOptions<GetNivoData30DaysQuery> => {
    return {
      ...args.options,
      refetchInterval,
      retry: 3,
      retryDelay: 1000,
      queryKey: ['nivoChartData30Days', args.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalQuoteFragment}
            query GetNivoData30Days(
              $currency: String!
              $endTime: Int!
              $symbol: String!
            ) {
              now: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $endTime
                currency: $currency
              ) {
                ID
                symbol
                currency
                name
                results {
                  ...MetalQuoteFragment
                }
              }

              history: GetMetalChartDataV3(
                chartId: THIRTY_DAYS
                symbol: $symbol
                currency: $currency
              ) {
                symbol
                currency
                results {
                  ...MetalQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  metalAndCurrencies: (
    args: QueryArgs<MetalAndCurrenciesQueryVariables, MetalAndCurrenciesQuery>,
  ): UseQueryOptions<MetalAndCurrenciesQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalAndCurrencies', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${forexFragment}
            query MetalAndCurrencies($symbol: String!, $timestamp: Int) {
              metal: GetMetalQuoteV3(
                symbol: $symbol
                currency: "USD"
                timestamp: $timestamp
              ) {
                results {
                  ask
                  bid
                  change
                  changePercentage
                  high
                  low
                  mid
                  unit
                }
              }

              AUD: GetForexQuoteV3(symbol: "AUD", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              BRL: GetForexQuoteV3(symbol: "BRL", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              GBP: GetForexQuoteV3(symbol: "GBP", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              CAD: GetForexQuoteV3(symbol: "CAD", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              CNY: GetForexQuoteV3(symbol: "CNY", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              EURO: GetForexQuoteV3(symbol: "EUR", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              HKD: GetForexQuoteV3(symbol: "HKD", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              INR: GetForexQuoteV3(symbol: "INR", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              JPY: GetForexQuoteV3(symbol: "JPY", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              MXN: GetForexQuoteV3(symbol: "MXN", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              RUB: GetForexQuoteV3(symbol: "RUB", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              ZAR: GetForexQuoteV3(symbol: "ZAR", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              CHF: GetForexQuoteV3(symbol: "CHF", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  currencies: (
    args: QueryArgs<CurrenciesQueryVariables, CurrenciesQuery>,
  ): UseQueryOptions<CurrenciesQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['Currencies', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${forexFragment}
            query Currencies($timestamp: Int) {
              AUD: GetForexQuoteV3(symbol: "AUD", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              BRL: GetForexQuoteV3(symbol: "BRL", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              GBP: GetForexQuoteV3(symbol: "GBP", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              CAD: GetForexQuoteV3(symbol: "CAD", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              CNY: GetForexQuoteV3(symbol: "CNY", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              EUR: GetForexQuoteV3(symbol: "EUR", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              HKD: GetForexQuoteV3(symbol: "HKD", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              INR: GetForexQuoteV3(symbol: "INR", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              JPY: GetForexQuoteV3(symbol: "JPY", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              MXN: GetForexQuoteV3(symbol: "MXN", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              RUB: GetForexQuoteV3(symbol: "RUB", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              ZAR: GetForexQuoteV3(symbol: "ZAR", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
              CHF: GetForexQuoteV3(symbol: "CHF", timestamp: $timestamp) {
                results {
                  ...ForexFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  shanghaiFix: (
    args: QueryArgs<ShanghaiFixQueryVariables, ShanghaiFixQuery>,
  ): UseQueryOptions<ShanghaiFixQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['shanghaiFix', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query ShanghaiFix(
              $currency: String!
              $timestamp: Int!
              $symbol: String!
              $limit: Int
            ) {
              GetShanghaiFixV3(
                currency: $currency
                timestamp: $timestamp
                symbol: $symbol
                limit: $limit
              ) {
                ID
                currency
                symbol
                results {
                  ID
                  timestamp
                  am
                  pm
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  shanghaiFixByYear: (
    args: QueryArgs<ShanghaiFixByYearQueryVariables, ShanghaiFixByYearQuery>,
  ): UseQueryOptions<ShanghaiFixByYearQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['shanghaiFix', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query ShanghaiFixByYear($year: String!) {
              GetShanghaiFixByYearV3(year: $year) {
                ID
                currency
                symbol
                results {
                  ID
                  timestamp
                  am
                  pm
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  londonFix: (
    args: QueryArgs<LondonFixQueryVariables, LondonFixQuery>,
  ): UseQueryOptions<LondonFixQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['londonFix', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${londonQuoteFragment}
            query LondonFix($year: String!) {
              londonFixUSD: GetLondonFixByYearV3(currency: "USD", year: $year) {
                ID
                currency
                results {
                  ...LondonQuoteFragment
                }
              }
              londonFixEUR: GetLondonFixByYearV3(currency: "EUR", year: $year) {
                ID
                currency
                results {
                  ...LondonQuoteFragment
                }
              }
              londonFixGBP: GetLondonFixByYearV3(currency: "GBP", year: $year) {
                ID
                currency
                results {
                  ...LondonQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  londonFixDynamic: (
    args: QueryArgs<LondonFixDynamicQueryVariables, LondonFixDynamicQuery>,
  ): UseQueryOptions<LondonFixDynamicQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['londonFixDynamic', args.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${londonQuoteFragment}
            query LondonFixDynamic(
              $currency: String!
              $startTime: Int!
              $endTime: Int!
            ) {
              GetLondonFixV3(
                currency: $currency
                startTime: $startTime
                endTime: $endTime
              ) {
                ID
                currency
                results {
                  ...LondonQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  londonFixByYear: (
    args: QueryArgs<LondonFixByYearQueryVariables, LondonFixByYearQuery>,
  ): UseQueryOptions<LondonFixByYearQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['londonFixByYear', args.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${londonQuoteFragment}
            query LondonFixByYear($year: String!, $currency: String!) {
              GetLondonFixByYearV3(year: $year, currency: $currency) {
                ID
                currency
                results {
                  ...LondonQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  londonFixAndShanghaiFix: (
    args: QueryArgs<
      LondonFixAndShanghaiFixQueryVariables,
      LondonFixAndShanghaiFixQuery
    >,
  ): UseQueryOptions<LondonFixAndShanghaiFixQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['londonFixAndShanghaiFix', args.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${londonQuoteFragment}
            query LondonFixAndShanghaiFix(
              $currency: String!
              $yesterday: Int!
              $today: Int!
              $symbol: String!
            ) {
              londonFix: GetLondonFixV3(
                currency: $currency
                startTime: $yesterday
                endTime: $today
                limit: 1
              ) {
                ID
                currency
                startTime
                endTime
                results {
                  ...LondonQuoteFragment
                }
              }
              shanghaiFix: GetShanghaiFixV3(
                currency: $currency
                timestamp: $yesterday
                symbol: $symbol
              ) {
                ID
                currency
                symbol
                results {
                  ID
                  timestamp
                  am
                  pm
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  silverPGM: (
    args: QueryArgs<SilverPgmQueryVariables, SilverPgmQuery>,
  ): UseQueryOptions<SilverPgmQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['silverPGM', cacheKey],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query SilverPGM($currency: String!, $timestamp: Int) {
              silver: GetMetalQuoteV3(
                symbol: "AG"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              platinum: GetMetalQuoteV3(
                symbol: "PT"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              palladium: GetMetalQuoteV3(
                symbol: "PD"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              rhodium: GetMetalQuoteV3(
                symbol: "RH"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  metalMonthAnnual: (
    args: QueryArgs<MetalMonthAnnualQueryVariables, MetalMonthAnnualQuery>,
  ): UseQueryOptions<MetalMonthAnnualQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalMonthAnnual', args?.variables],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query MetalMonthAnnual(
              $symbol: String!
              $currency: String!
              $timestamp: Int!
            ) {
              GetHistoricalPointsV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                thirtyDay {
                  ID
                  change
                  changePercentage
                }
                sixtyDay {
                  ID
                  change
                  changePercentage
                }
                oneYear {
                  ID
                  change
                  changePercentage
                }
                fiveYear {
                  ID
                  change
                  changePercentage
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  goldRatios: (
    args: QueryArgs<GoldRatiosQueryVariables, GoldRatiosQuery>,
  ): UseQueryOptions<GoldRatiosQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args?.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['goldRatios', cacheKey ?? ''],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query GoldRatios($symbols: String!, $timestamp: Int!) {
              gold: GetMetalQuoteV3(
                symbol: "AU"
                currency: "USD"
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }
              silver: GetMetalQuoteV3(
                symbol: "AG"
                currency: "USD"
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }
              palladium: GetMetalQuoteV3(
                symbol: "PD"
                currency: "USD"
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }
              platinum: GetMetalQuoteV3(
                symbol: "PT"
                currency: "USD"
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }

              quotes: GetBarchartQuotes(
                symbols: $symbols
                timestamp: $timestamp
              ) {
                timestamp
                symbols
                results {
                  lastPrice
                  name
                  serverTimestamp
                  symbol
                }
              }

              crudeOil: GetBarchartFuturesByExchange(
                exchange: "NYMEX"
                category: "Energies"
              ) {
                timestamp
                exchange
                results {
                  name
                  lastPrice
                  netChange
                  symbol
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  exchangeRatesTable: (
    args?: QueryArgs<void, ExchangeRatesTableQueryQuery>,
  ): UseQueryOptions<ExchangeRatesTableQueryQuery> => {
    return {
      ...args?.options,
      refetchInterval,
      queryKey: ['exchangeRatesTable'],
      queryFn: async () =>
        await graphs.pricesFetch(
          gql`
            query ExchangeRatesTableQuery {
              GetLiveSpotGoldTableV3 {
                ID
                Symbol
                Table {
                  Currency
                  Rate {
                    CurrencyToUsd
                    UsdToCurrency
                    ChangePercent
                    NYTime
                  }
                  Gold {
                    Price
                    Change
                    ChangePercent
                  }
                }
              }
            }
          `,
          args?.variables,
        ),
    }
  },
}
