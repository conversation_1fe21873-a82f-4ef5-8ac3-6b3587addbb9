img {
  display: block;
  width: 100%;
}

a {
  display: block;
  color: #1d61ae;
  text-decoration: none;
}

.mining-billboard {
  display: flex;
  flex-wrap: wrap;
  font-family: 'Arial';
}

.section-one {
  display: flex;
  justify-content: center;
  flex: 1 1 100%;
  margin-bottom: 15px;
}

.section-two {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  flex: 1 1 100%;
}

.section-three {
  flex: 1 1 100%;
  color: #0a4e8d;
  margin-bottom: 15px;
}

.section-four {
  flex: 1 1 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;
  margin-bottom: 15px;
}

.line-item {
  display: block;
}

.section-three .line-item:nth-child(odd) {
  background: #f5f5f5;
}

.section-three .line-item {
  font-size: 14px;
  margin: 0;
  padding: 5px 15px;
}

.stock-section {
  flex: 1 1 100%;
  margin: 15px 0 0 0;
  text-align: center;
}

.stock-data-name {
  display: none;
}

.stock-name {
  flex: 1 1 100%;
  font-size: 15px;
  font-weight: 700;
  color: #1d61ae;
}

.stock-price {
  font-weight: bold;
  font-size: 22px;
  margin-right: 5px;
}

.stock-arrow-up {
  display: none;
}

.stock-arrow-down {
  display: none;
}

.price-container {
  flex: 1 1 50%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.hlv-container {
  flex: 1 1 50%;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
}

.hlv-container p {
  margin: 5px 10px 0 10px;
}

.stock-timestamp {
  display: none;
}

.stock-price-container {
  flex: 1 1 100%;
  display: flex;
  align-items: center;
}

.stock-change-container {
  flex: 1 1 100%;
}

@media (min-width: 700px) {
  .mining-billboard {
    display: grid;
    grid-template-columns: 300px 1fr;
    grid-template-rows: 50px 145px 55px;
    flex-wrap: nowrap;
  }

  .section-one {
    grid-column: 1;
    grid-row: 1 / span 3;
    margin: 0;
  }

  .section-two {
    grid-row: 1;
    grid-column: 2 / span 1;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: normal;
    padding: 0 15px;
  }

  .section-three {
    grid-row: 2;
    grid-column: 2 / span 1;
  }

  .section-four {
    grid-row: 3;
    grid-column: 2 / span 1;
  }

  .stock-section {
    margin: 0;
    text-align: right;
  }

  .hlv-container {
    text-align: right;
  }
}
