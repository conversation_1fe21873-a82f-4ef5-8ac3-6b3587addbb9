import Image from 'next/image'
import Link from 'next/link'
import { type FC, Fragment } from 'react'
import type { TeaserWideProps } from '~/src/components-news/ArticleTeasers/TeaserWide'
import { Spacer } from '~/src/components/spacer/spacer.component'
import cs from '~/src/utils/cs'
import { teaserTimestamp } from '~/src/utils/teaser-timestamp'

export const TeaserTextWithAuthor: FC<TeaserWideProps> = ({ node, size }) => {
  const sizeCSS = {
    sm: 'text-[20px] leading-[130%] line-clamp-3',
    md: '',
    lg: 'text-[20px] leading-[130%] line-clamp-2 !text-[#232323]',
    xl: '',
  }

  const imgContainerCSS = {
    sm: 'w-1/12 relative',
    md: '',
    lg: 'w-1/12 relative',
    xl: '',
  }

  const bodyContainerCSS = {
    sm: 'block pl-[15px] ml-[15px] w-11/12 border-l border-ktc-borders',
    md: '',
    lg: 'block pl-[15px] ml-[15px] w-11/12 border-l border-ktc-borders',
    xl: '',
  }

  const summaryCSS = {
    sm: 'text-[14px] leading-5 line-clamp-2 font-normal text-[#838383]',
    md: '',
    lg: 'text-[14px] leading-5 line-clamp-2 font-normal text-[#838383]',
    xl: '',
  }

  return (
    <div className="mb-[45px] flex w-full last:mb-0">
      <Link
        className={imgContainerCSS[size]}
        href={node?.author?.urlAlias ?? '/'}
      >
        <div id="authorContainer" className="flex flex-col items-center">
          <div>
            <Image
              src={node?.author?.imageUrl ?? '/default-avatar.svg'}
              alt={`Photo of ${node?.author?.name}`}
              width={35}
              height={35}
              className="rounded-full"
              sizes="(max-width: 767px) 35px, 70px"
            />
          </div>
          <div className="block">
            <p className="py-2.5 text-center text-sm font-normal leading-5 text-[#232323]">
              {node?.author?.name}
            </p>
          </div>
        </div>
      </Link>
      <div className={bodyContainerCSS[size]}>
        <Link href={node?.urlAlias || '/'}>
          <>
            <h2 className={cs([sizeCSS[size]])}>
              {node?.teaserHeadline ?? node?.title}
            </h2>
            <div className="h-2" />
            {(size === 'lg' || size === 'xl') && (
              <Fragment>
                <div
                  className={cs(['summary', summaryCSS[size]])}
                  dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
                />
                <Spacer className="h-2" />
              </Fragment>
            )}
            <time className={cs(['text-sm font-normal text-[#cccccc]'])}>
              {node?.createdAt && teaserTimestamp(node?.createdAt)}
            </time>
          </>
        </Link>
      </div>
    </div>
  )
}
