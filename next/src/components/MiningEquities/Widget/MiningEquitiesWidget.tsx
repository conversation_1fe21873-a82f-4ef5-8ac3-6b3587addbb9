import { useRouter } from 'next/navigation'
import { useMemo, useState } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import MiningEquitiesDataTable from '~/src/components/MiningEquities/DataTable/MiningEquitiesDataTable'
import MiningEquitiesWidgetHeader from '~/src/components/MiningEquities/Widget/MiningEquitiesWidgetHeader'
import {
  getTopMiningEquities,
  useMiningEquitiesData,
} from '~/src/hooks/MiningEquities/useMiningEquitiesData'

/**
 * This component is a wrapper for the Mining Equities Widget.
 * It renders the header and the table.
 * It also handles the filtering of the data.
 *
 * @constructor
 */
const MiningEquitiesWidget = () => {
  const router = useRouter()

  // Get the data for the table
  const data = useMiningEquitiesData()

  const [currentFilter, setCurrentFilter] = useState('ALL')

  const topMining = useMemo(
    () => getTopMiningEquities(data, currentFilter),
    [data, currentFilter],
  )

  const isLoading = !topMining

  return (
    <>
      <div className=" mb-6 md:mb-0 w-full max-w-full p-0 md:rounded-xl md:p-6 md:shadow-[0_0_40px_0_rgba(18,18,18,0.08)] lg:w-full lg:max-w-full">
        <div className="w-full max-w-full sm:max-w-[750px] md:w-[750px] md:p-0 lg:w-full lg:max-w-full">
          <ErrBoundary errorTitle="Mining Equities Header Error">
            <MiningEquitiesWidgetHeader totalCompanies={data.length} />
          </ErrBoundary>
        </div>
        <div className="mt-5 w-full max-w-full p-0 lg:w-full lg:max-w-full">
          <ErrBoundary errorTitle="Mining Equities Widget Error">
            <MiningEquitiesDataTable
              data={data}
              displayData={topMining}
              filterColumns={['Name', 'TVSymbol', 'Price', 'Change', 'Mobile']}
              isLoading={isLoading}
              onChangeFilter={setCurrentFilter}
              pagination={false}
              showSearch={false}
              sortable={false}
            />
          </ErrBoundary>
        </div>
        <div className="flex w-full max-w-full pt-6 lg:w-full lg:max-w-full items-center justify-center">
          <button
            type="button"
            className="px-4 py-3 bg-[#E5B539] hover:bg-[#5E5E5E] hover:text-white active:bg-[#060606] active:text-white text-neutral-900 w-full sm:w-auto rounded-lg justify-center items-center gap-2.5 inline-flex"
            onClick={() => {
              router.push('/mining/mining-equities')
            }}
          >
            <div className="text-sm font-bold font-['Mulish'] leading-none">
              VIEW FULL TABLE
            </div>
          </button>
        </div>
      </div>
    </>
  )
}

export default MiningEquitiesWidget
