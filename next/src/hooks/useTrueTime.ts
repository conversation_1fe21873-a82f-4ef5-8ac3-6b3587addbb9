import dayjs from 'dayjs'
import { useEffect, useState } from 'react'

export function useTrueTime(pollIntervalMs = 60000) {
  const [now, setNow] = useState(() => dayjs())

  useEffect(() => {
    let timer: NodeJS.Timeout
    async function sync() {
      try {
        const res = await fetch('https://worldtimeapi.org/api/timezone/Etc/UTC')
        const { unixtime } = await res.json()
        const trueTime = dayjs(unixtime * 1000)
        console.log(
          '[DEBUG] useTrueTime: Synced with WorldTimeAPI:',
          trueTime.format(),
        )
        setNow(trueTime)
      } catch (error) {
        console.log(
          '[DEBUG] useTrueTime: WorldTimeAPI failed, using local time:',
          error,
        )
        setNow(dayjs())
      }
      timer = setTimeout(sync, pollIntervalMs)
    }
    sync()
    return () => clearTimeout(timer)
  }, [pollIntervalMs])

  useEffect(() => {
    const tick = setInterval(
      () => setNow((prev) => prev.add(1, 'second')),
      1000,
    )
    return () => clearInterval(tick)
  }, [])

  return now
}
