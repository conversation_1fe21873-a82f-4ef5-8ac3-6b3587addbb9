import { useEffect, useState } from 'react'
import type { BarchartsFuturesByExchangeQuery } from '~/src/generated'
import { markets } from '~/src/lib/markets-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'

enum Meats {
  LiveCattle = 'Live Cattle',
  FeederCattle = 'Feeder Cattle',
  LeanHogs = 'Lean Hogs',
  PorkCutout = 'Pork Cutout',
  Milk = 'Class III Milk',
}

function dataProcessor(cme: BarchartsFuturesByExchangeQuery) {
  let meats = []

  if (cme?.GetBarchartFuturesByExchange) {
    const hashMap = {}
    const { results } = cme.GetBarchartFuturesByExchange

    for (let i = 0; i < results.length; i++) {
      if (!Object.prototype.hasOwnProperty.call(hashMap, results[i].name)) {
        hashMap[results[i].name] = {
          ...results[i],
          exchange: 'CME',
          category: 'Meats',
        }
      }
    }

    Object.keys(hashMap).map((x) => {
      switch (x) {
        case Meats.LiveCattle:
          meats = [...meats, hashMap[Meats.LiveCattle]]
          break
        case Meats.FeederCattle:
          meats = [...meats, hashMap[Meats.FeederCattle]]
          break
        case Meats.LeanHogs:
          meats = [...meats, hashMap[Meats.LeanHogs]]
          break
        case Meats.PorkCutout:
          meats = [...meats, hashMap[Meats.PorkCutout]]
          break
        case Meats.Milk:
          meats = [...meats, hashMap[Meats.Milk]]
          break
        default:
          return
      }
    })
  }

  return meats
}

const useFuturesMeats = () => {
  const { data: cme } = kitcoQuery(
    markets.futuresByExchange({
      variables: {
        category: 'Meats',
        exchange: 'CME',
      },
    }),
  )

  const [isLoading, setIsLoading] = useState(true)
  const [processedData, setProcessedData] = useState<any>([])

  useEffect(() => {
    const processData = async () => {
      if (cme) {
        const processedResult = await dataProcessor(cme)
        setProcessedData(processedResult)
        setIsLoading(false)
      }
    }

    processData()
  }, [cme])

  return [processedData, isLoading]
}

export default useFuturesMeats
