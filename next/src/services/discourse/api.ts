import { sanitizeEmail, sanitizeUsername } from '~/src/features/auth/sanitize'

/**
 * Fetch the SSO token from the server
 *
 * @param token
 * @param sso
 * @param sig
 * @returns {Promise<void>}
 */
export const fetchSSOToken = async (
  token: string,
  sso: string | string[],
  sig: string | string[],
): Promise<string | null> => {
  try {
    const response = await fetch('/api/discourse/auth', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ sso: sso, sig: sig }),
    })

    if (response.ok) {
      // Return the URL to redirect the user to Discourse
      const { url } = await response.json()

      return url
    }
  } catch (e) {
    console.error('Error fetching SSO token:', e)
  }
  return null
}

/**
 * Fetch user data from Discourse
 *
 * @param email
 */
export const getUserByEmail = async (email: string) => {
  try {
    const response = await fetch('/api/discourse/getUserByEmail', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: sanitizeEmail(email) }),
    })

    if (response.ok) {
      return await response.json()
    }
  } catch (err) {
    console.error('Error fetching user by email:', err)
  }
  return null
}

/**
 * Fetch user data from Discourse
 *
 * @param username
 */
export const getUserByUsername = async (username: string) => {
  try {
    const { original, lowercase } = sanitizeUsername(username)

    const response = await fetch('/api/discourse/getUserByUsername', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username: lowercase, displayName: original }),
    })

    if (response.ok) {
      return await response.json()
    }
  } catch (err) {
    console.error('Error fetching user by username:', err)
  }
  return null
}

/**
 * Check if a username exists in Discourse
 *
 * @param username
 */
export const checkUsernameExists = async (username: string) => {
  const user = await getUserByUsername(username)

  return !(user === null || !user.username || user.username.length <= 0)
}
