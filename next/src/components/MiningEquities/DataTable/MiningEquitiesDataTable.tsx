import type { TableOptions } from '@tanstack/react-table'
import {
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
} from '@tanstack/table-core'
import { type FC, useEffect, useMemo, useState } from 'react'
import DataTable from '~/src/components/DataTable/DataTable'
import DataTableFilter from '~/src/components/DataTable/Filter/DataTableFilter'
import DataTableSearch from '~/src/components/DataTable/Search/DataTableSearch'
import DataTableSubFilter from '~/src/components/DataTable/SubFilter/DataTableSubFilter'
import columns from '~/src/components/MiningEquities/DataTable/DataTableColumns'
import { sortData } from '~/src/hooks/MiningEquities/sortService'
import { useFilteredData } from '~/src/hooks/MiningEquities/useFilteredData'
import { useSorting } from '~/src/hooks/MiningEquities/useSorting'
import { useSubFilters } from '~/src/hooks/MiningEquities/useSubFilters'
import useUniqueCategories from '~/src/hooks/MiningEquities/useUniqueCategories'
import type MiningEquity from '~/src/types/DataTable/MiningEquity'

/**
 * MiningEquitiesDataTable component props.
 *
 * @interface MiningEquitiesDataTableProps
 *
 * @property {MiningEquity[]} data - The raw data to be used in the table (for filtering and sorting).
 * @property {MiningEquity[]} [displayData] - The data to be displayed in the table. (optional)
 * @property {string[]} [filterColumns] - The columns to be filtered.
 * @property {boolean} [isLoading] - Whether the data is loading.
 * @property {(filter: string) => void} [onChangeFilter] - The function to handle filter changes.
 * @property {boolean} [pagination] - Whether to display pagination.
 * @property {boolean} [showSearch] - Whether to display the search bar.
 * @property {boolean} [sortable] - Whether to allow sorting.
 * @property {SortingState} [sortBy] - The sorting state.
 * @extends {DataTableProps<MiningEquity>}
 */
interface MiningEquitiesDataTableProps {
  data: MiningEquity[]
  displayData?: MiningEquity[]
  filterColumns?: string[]
  isLoading?: boolean
  onChangeFilter?: (filter: string) => void
  pagination?: boolean
  showSearch?: boolean
  sortable?: boolean
  sortBy?: SortingState
}

/**
 * This component is a table that displays the Mining Equities data.
 * It allows the user to filter, sort, and paginate the data.
 *
 * @param {MiningEquitiesDataTableProps} props - The component props.
 * @returns {React.ReactElement} - The rendered component.
 */
const MiningEquitiesDataTable: FC<MiningEquitiesDataTableProps> = ({
  data,
  displayData = data,
  filterColumns = [],
  isLoading,
  onChangeFilter = () => {},
  pagination = true,
  showSearch = true,
  sortable = true,
  sortBy = [],
}: MiningEquitiesDataTableProps): React.ReactElement => {
  const [isMobile, setIsMobile] = useState(false)

  // Filter states
  const [activeFilter, setActiveFilter] = useState('ALL')
  const [activeSubFilter, setActiveSubFilter] = useState('ALL')

  // Search states
  const [searchTerm, setSearchTerm] = useState('')

  // Filter columns based on filterColumns prop
  const filteredColumns = useMemo(() => {
    // If there are no filter columns, return all columns
    if (filterColumns.length === 0) {
      return columns
    }
    return columns.filter((column) => filterColumns.includes(column.id))
  }, [filterColumns])

  // Filter the data based on the active filter
  const { filteredData, searchResults } = useFilteredData(
    displayData,
    activeFilter,
    activeSubFilter,
    searchTerm,
  )

  // Get the unique categories from the data
  const uniqueCategories = useUniqueCategories(data)

  // Get the sub filters based on the active filter
  const { subFilters } = useSubFilters(activeFilter)

  // Custom hooks
  const { internalSorting, sortColumnExists, setInternalSorting } =
    useSorting(sortBy)

  // Sort the data based on the internal sorting
  const sortedData = useMemo(() => {
    // If the sorting column doesn't exist, return the filtered data
    return sortColumnExists ? filteredData : sortData(filteredData, sortBy)
  }, [filteredData, sortBy, sortColumnExists])

  // Column pinning configuration
  const extraConfig: Partial<TableOptions<MiningEquity>> = useMemo(() => {
    const config = {
      initialState: {
        columnPinning: {
          left: ['Name'],
        },
        pagination: {
          pageSize: isMobile ? 10 : displayData.length,
        },
      },
      getSortedRowModel: getSortedRowModel(),
      onSortingChange: setInternalSorting,
      state: {
        sorting: internalSorting,
      },
      enableSorting: sortable,
    }

    // If it's not mobile, return the default config
    if (!isMobile || !pagination) {
      return config
    }

    // If it's mobile, return the config with the pagination row model
    return {
      ...config,
      getPaginationRowModel: getPaginationRowModel(),
    }
  }, [isMobile, pagination, sortable, displayData, internalSorting])

  // Detect screen size to determine if it's mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 640)
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Raise the filter change event
  useEffect(() => {
    onChangeFilter(activeFilter)
  }, [activeFilter])

  return (
    <>
      <DataTableFilter
        activeFilter={activeFilter}
        filters={uniqueCategories}
        onFilterChange={setActiveFilter}
      />
      {showSearch && (
        <DataTableSearch
          onSearchChange={setSearchTerm}
          resultText={searchResults}
          searchTerm={searchTerm}
        >
          {subFilters.length > 0 && (
            <DataTableSubFilter
              activeFilter={activeSubFilter}
              filters={subFilters}
              onFilterChange={setActiveSubFilter}
            />
          )}
        </DataTableSearch>
      )}
      <DataTable<MiningEquity>
        columns={filteredColumns}
        data={sortedData}
        extraConfig={extraConfig}
        isLoading={isLoading}
        paginationClassName="sm:hidden"
        paginationEnabled={pagination}
        scrollOnDesktop={true}
        disableDragDrop={true}
      />
    </>
  )
}

export default MiningEquitiesDataTable
