import { AdvertisingSlot } from 'react-advertising'
import BarchartsLeadersCell from '~/src/components-markets/BarchartsLeadersCell/BarchartsLeadersCell'
import useFuturesMetals from '~/src/components-markets/FuturesCell/metalsDataCell'
import MarketPageIndicesCell, {
  // indicesSymbols,
  // componentData as indicesData,
  indices,
} from '~/src/components-markets/MarketPageIndicesCell/MarketPageIndicesCell'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import FutureForexTable from '~/src/components/FutureForexTable/FutureForexTable'
import Layout from '~/src/components/Layout/Layout'
import PageLayoutTwoColumns from '~/src/components/PageLayoutTwoColumns/PageLayoutTwoColumns'
import { Barcharts } from '~/src/features/bar-charts/barcharts'
import styles from './index.module.scss'

const MarketsPage = () => {
  const [metals] = useFuturesMetals()

  // https://frontend.dev.kitco.com/markets/indices/$NASX
  // https://frontend.dev.kitco.com/markets/indices/$NASX

  return (
    <Layout title={'Stock Markets | DJIA, DOW, NASDAQ, S&P 500, NYSE'}>
      <PageLayoutTwoColumns>
        <main>
          <h1 className="mb-8 text-4xl font-bold">All Markets</h1>
          <section className={styles.block}>
            <div className={styles.chartGrid}>
              {indices.map((x, idx) => (
                <div key={idx} className="mr-2">
                  <Barcharts
                    symbol={`${x.symbol}`}
                    title={x.name}
                    href={`/markets/indices/${x.symbol}`}
                  />
                </div>
              ))}
            </div>
          </section>

          <section className={styles.block}>
            <AdvertisingSlot
              id={'banner-2'}
              className="mx-auto h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] desktop:h-[90px] desktop:w-[728px] no-print"
            />
          </section>

          <section className={styles.block}>
            <MarketPageIndicesCell />
          </section>

          <section className={styles.block}>
            <BarchartsLeadersCell leaderType="active" showMore={true} />
          </section>

          <section className={styles.block}>
            <BarchartsLeadersCell leaderType="gainers" showMore={true} />
          </section>
          <section className="grid gap-4">
            <FutureForexTable
              title="Metal Futures"
              data={metals}
              showMore={true}
            />
          </section>
        </main>
        <aside>
          <div className={styles.block}>
            <AdvertisingSlot
              id={'right-rail-1'}
              className="mx-auto hidden h-[250px] w-[300px] desktop:flex no-print"
            />
          </div>
          <div className={styles.block}>
            <LatestNewsCell />
          </div>
          <div className={styles.block}>
            <AdvertisingSlot
              id={'right-rail-2'}
              className="mx-auto hidden h-[600px] w-[300px] desktop:flex no-print"
            />
          </div>
        </aside>
      </PageLayoutTwoColumns>
    </Layout>
  )
}

export default MarketsPage
