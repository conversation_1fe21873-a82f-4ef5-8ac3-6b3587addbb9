import type CommodityData from '~/src/types/DataTable/CommodityData'

function useGoldIndexMessages(value: CommodityData[], commodity: string) {
  // if the value is empty, return empty strings
  if (!value || !commodity)
    return {
      getQuestionMessage: () => '',
      getExplanationMessage: () => '',
      commodityData: null,
    }

  // Find the commodity data
  const commodityData: CommodityData = value.find(
    (item: CommodityData) =>
      item.commodity.toLowerCase() === commodity.toLowerCase(),
  )

  // If the commodity data is not found, return empty strings
  if (!commodityData) {
    return {
      getQuestionMessage: () => '',
      getExplanationMessage: () => '',
      commodityData: null,
    }
  }

  const commodityName = commodityData.commodity

  const { totalChange, changeDueToUSD, changeDueToTrade } = commodityData

  const getQuestionMessage = () => {
    if (totalChange.changeVal > 0) {
      return `Did ${commodityName} really go up ${totalChange.change}?`
    }
    if (totalChange.changeVal < 0) {
      return `Did ${commodityName} really go down ${totalChange.change}?`
    }

    return `Did ${commodityName} really not change?`
  }

  const getExplanationMessage = () => {
    if (changeDueToUSD.changeVal > 0 && changeDueToTrade.changeVal > 0) {
      return `Yes. The weakened US Dollar was responsible for ${changeDueToUSD.change} of that increase.`
    }

    if (changeDueToUSD.changeVal < 0 && changeDueToTrade.changeVal < 0) {
      return `Yes. The stronger US Dollar was responsible for ${changeDueToUSD.change} of that drop.`
    }

    if (changeDueToUSD.changeVal > 0 && changeDueToTrade.changeVal === 0) {
      return `Yes. The weakened US Dollar was responsible for ${changeDueToUSD.change} of that increase.`
    }

    if (changeDueToUSD.changeVal < 0 && changeDueToTrade.changeVal === 0) {
      return 'In real terms ${commodityName} remained unchanged, but the stronger US Dollar caused the drop in price.'
    }

    if (changeDueToUSD.changeVal === 0 && changeDueToTrade.changeVal > 0) {
      return `Yes. The US Dollar value didn't change and had no impact on the price.`
    }

    if (changeDueToUSD.changeVal === 0 && changeDueToTrade.changeVal < 0) {
      return `Yes. The US Dollar value didn't change and had no impact on the price.`
    }

    if (changeDueToUSD.changeVal === 0 && changeDueToTrade.changeVal === 0) {
      return `Yes. There was no change in the value of ${commodityName} nor the US Dollar.`
    }

    if (changeDueToUSD.changeVal > 0 && changeDueToTrade.changeVal < 0) {
      if (totalChange.changeVal > 0) {
        return `No. It actually went down ${changeDueToTrade.change} in real terms, but US Dollar weakness makes it appear to have gone the other way.`
      }

      return `No. In real terms it actually went down ${changeDueToTrade.change}, but this change was offset by a weaker US Dollar.`
    }

    if (changeDueToUSD.changeVal < 0 && changeDueToTrade.changeVal > 0) {
      if (totalChange.changeVal > 0) {
        return `Yes. It actually went up ${changeDueToTrade.change} in real terms, but this change was offset by a stronger US Dollar.`
      }
      return `No. It actually went up ${changeDueToTrade.change} in real terms, but US Dollar strength makes it appear to have gone the other way.`
    }
    return `Yes. The weakened US Dollar was responsible for ${
      changeDueToUSD.change
    } of that ${totalChange.changeVal >= 0 ? 'increase' : 'decrease'}.`
  }

  const getWidgetMessage = () => {
    if (totalChange.changeVal > 0) {
      return {
        text: `Did ${commodityName} really go up`,
        value: `${totalChange.change} (${totalChange.percentage}%)?`,
      }
    }
    if (totalChange.changeVal < 0) {
      return {
        text: `Did ${commodityName} really go down`,
        value: `${totalChange.change} (${totalChange.percentage}%)?`,
      }
    }
    return { text: `Did ${commodityName} really not change?`, value: '' }
  }

  return {
    getQuestionMessage,
    getExplanationMessage,
    getWidgetMessage,
    commodityData,
  }
}

export default useGoldIndexMessages
