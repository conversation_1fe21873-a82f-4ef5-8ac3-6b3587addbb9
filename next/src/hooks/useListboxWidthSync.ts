import { useEffect, useRef, useState } from 'react'

export function useListboxWidthSync<T extends HTMLElement>() {
  // Store the button ref
  const buttonRef = useRef<T>(null)

  // Store the button width
  const [buttonWidth, setButtonWidth] = useState<string | undefined>(undefined)

  useEffect(() => {
    if (buttonRef.current) {
      setButtonWidth(`${buttonRef.current.offsetWidth}px`)
    }
  }, [buttonRef.current])

  useEffect(() => {
    const observer = new ResizeObserver(() => {
      if (buttonRef.current) {
        setButtonWidth(`${buttonRef.current.offsetWidth}px`)
      }
    })
    if (buttonRef.current) {
      observer.observe(buttonRef.current)
    }
    return () => {
      if (buttonRef.current) {
        observer.unobserve(buttonRef.current)
      }
    }
  }, [])

  return { buttonRef, buttonWidth }
}
