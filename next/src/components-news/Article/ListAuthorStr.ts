import type { Author, NewsArticle } from '~/src/generated'

export const listAuthorStr = (data: NewsArticle) => {
  const authorData = data?.author
  const supportingAuthors = data?.supportingAuthors ?? []
  const unifyAuthors: <AUTHORS>

  let listAuthorString = 'By '
  unifyAuthors?.forEach((x, idx) => {
    listAuthorString += `${x?.name}${
      idx !== unifyAuthors?.length - 1 ? ' and ' : ''
    }`
  })

  return listAuthorString
}
