import type React from 'react'
import 'react-tippy'

// Fixes the error: Property 'children' does not exist on type 'IntrinsicAttributes & TooltipProps'
// This is a known issue with the react-tippy library. The library is not written in TypeScript, so we need to declare the children prop ourselves.
// Todo: We should not use this library, not maintained since 4 years ago

declare module 'react-tippy' {
  export interface TooltipProps {
    children?: React.ReactNode
  }
}
