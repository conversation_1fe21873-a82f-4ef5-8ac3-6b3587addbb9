{"compilerOptions": {"noUncheckedIndexedAccess": true, "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "verbatimModuleSyntax": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}