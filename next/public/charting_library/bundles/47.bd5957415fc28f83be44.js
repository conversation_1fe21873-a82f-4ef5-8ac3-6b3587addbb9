(window.webpackJsonp = window.webpackJsonp || []).push([[47], {
  "0Ez2": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"m20 11a6 6 0 1 0-12 0c0 2.91 2.87 8.11 6 11.55 3.13-3.44 6-8.64 6-11.55zm-6 13c-3-3-7-9.13-7-13a7 7 0 1 1 14 0c0 3.87-4 10-7 13zm0-15a1 1 0 0 0 0 4 1 1 0 0 0 0-4\"/></svg>";
  },
  "0Q2B": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M21.5 23h-14v1h14zM5 7.5v14h1v-14z\"/><path d=\"M12 23c0-3.314-2.686-6-6-6h-.5v1h.5c2.761 0 5 2.239 5 5v.5h1v-.5z\"/><path d=\"M20 23c0-7.732-6.268-14-14-14h-.5v1h.5c7.18 0 13 5.82 13 13v.5h1v-.5z\"/><path d=\"M16 23c0-5.523-4.477-10-10-10h-.5v1h.5c4.971 0 9 4.029 9 9v.5h1v-.5z\"/><path d=\"M5.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "11T/": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.354 21.354l7.097-7.097-.707-.707-7.097 7.097z\"/><path d=\"M17.249 11.458l7.105-7.105-.707-.707-7.105 7.105z\"/><path d=\"M7.542 22.683l17.296-2.739-.156-.988-17.296 2.739z\" id=\"Line\"/><path d=\"M7.538 22.062l15.708-7.661-.438-.899-15.708 7.661z\"/><path d=\"M6.802 20.97l7.695-15.777-.899-.438-7.695 15.777z\"/><path d=\"M6.285 20.741l2.76-17.423-.988-.156-2.76 17.423z\"/><path d=\"M5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM15.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "2dtg": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"evenodd\"><path fill-rule=\"nonzero\" d=\"M23.002 23C23 23 23 18.003 23 18.003L15.998 18C16 18 16 22.997 16 22.997l7.002.003zM15 18.003A1 1 0 0 1 15.998 17h7.004c.551 0 .998.438.998 1.003v4.994A1 1 0 0 1 23.002 24h-7.004A.993.993 0 0 1 15 22.997v-4.994z\"/><path d=\"M19 20h1v2h-1z\"/><path fill-rule=\"nonzero\" d=\"M22 14.5a2.5 2.5 0 0 0-5 0v3h1v-3a1.5 1.5 0 0 1 3 0v.5h1v-.5z\"/><g fill-rule=\"nonzero\"><path d=\"M3 14.707A1 1 0 0 1 3.293 14L14.439 2.854a1.5 1.5 0 0 1 2.122 0l2.585 2.585a1.5 1.5 0 0 1 0 2.122L8 18.707a1 1 0 0 1-.707.293H4a1 1 0 0 1-1-1v-3.293zm1 0V18h3.293L18.439 6.854a.5.5 0 0 0 0-.708l-2.585-2.585a.5.5 0 0 0-.708 0L4 14.707z\"/><path d=\"M13.146 4.854l4 4 .708-.708-4-4zm-9 9l4 4 .708-.708-4-4z\"/><path d=\"M15.146 6.146l-9 9 .708.708 9-9z\"/></g></g></svg>";
  },
  "2fn7": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M6.5 23h12v-1h-12z\" id=\"Line\"/><path d=\"M21.596 20.715l3.091-9.66-.952-.305-3.091 9.66z\"/><path d=\"M8.413 22.664l1.95-6.094-.952-.305-1.95 6.094z\"/><path d=\"M11.602 12.695l3.085-9.641-.952-.305-3.085 9.641z\"/><path d=\"M11.783 16.167l6.817 5.454.625-.781-6.817-5.454z\"/><path d=\"M15.976 18.652l3.711-11.598-.952-.305-3.711 11.598z\"/><path d=\"M4.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM10.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM20.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "2gmH": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"m16 11a6 6 0 1 0-12 0c0 2.91 2.87 8.11 6 11.55 3.13-3.44 6-8.64 6-11.55zm-6 13c-3-3-7-9.13-7-13a7 7 0 1 1 14 0c0 3.87-4 10-7 13zm0-15a2 2 0 0 0 0 4 2 2 0 0 0 0-4m15 11c0 2.48-2.02 4-4.5 4S16 22.49 16 20h1c0 1.8 1.42 3 3.5 3s3.5-1.2 3.5-3m-6-2h5v1h-5m3-3.5v8h-1v-8m.5.5a1.5 1.5 0 0 1 0-3 1.5 1.5 0 0 1 0 3\"/></svg>";
  },
  "2lje": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 29 31\" width=\"29\" height=\"31\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M15.3 22l8.187-8.187c.394-.394.395-1.028.004-1.418l-4.243-4.243c-.394-.394-1.019-.395-1.407-.006l-11.325 11.325c-.383.383-.383 1.018.007 1.407l1.121 1.121h7.656zm-9.484-.414c-.781-.781-.779-2.049-.007-2.821l11.325-11.325c.777-.777 2.035-.78 2.821.006l4.243 4.243c.781.781.78 2.048-.004 2.832l-8.48 8.48h-8.484l-1.414-1.414z\"/><path d=\"M13.011 22.999h7.999v-1h-7.999zM13.501 11.294l6.717 6.717.707-.707-6.717-6.717z\"/></g></svg>";
  },
  "39u9": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M8 9.5c0 3.038 2.462 5.5 5.5 5.5s5.5-2.462 5.5-5.5v-.5h-1v.5c0 2.485-2.015 4.5-4.5 4.5s-4.5-2.015-4.5-4.5v-.5h-1v.5z\"/><path d=\"M0 9.5c0 7.456 6.044 13.5 13.5 13.5s13.5-6.044 13.5-13.5v-.5h-1v.5c0 6.904-5.596 12.5-12.5 12.5s-12.5-5.596-12.5-12.5v-.5h-1v.5z\"/><path d=\"M4 9.5c0 4.259 2.828 7.964 6.86 9.128l.48.139.277-.961-.48-.139c-3.607-1.041-6.137-4.356-6.137-8.167v-.5h-1v.5z\"/><path d=\"M16.141 18.628c4.032-1.165 6.859-4.869 6.859-9.128v-.5h-1v.5c0 3.811-2.53 7.125-6.136 8.167l-.48.139.278.961.48-.139z\"/><path d=\"M13.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM13.5 11c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "3s8f": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"evenodd\"><path fill-rule=\"nonzero\" d=\"M14 10a2 2 0 0 0-2 2v11H6V12c0-4.416 3.584-8 8-8s8 3.584 8 8v11h-6V12a2 2 0 0 0-2-2zm-3 2a3 3 0 0 1 6 0v10h4V12c0-3.864-3.136-7-7-7s-7 3.136-7 7v10h4V12z\"/><path d=\"M6.5 18h5v1h-5zm10 0h5v1h-5z\"/></g></svg>";
  },
  "4+EX": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M23 4v21h1v-21z\"/><path d=\"M17 4v21h1v-21z\"/><path d=\"M5 16.5v8.5h1v-8.5z\"/><path d=\"M5 4v8.5h1v-8.5z\"/><path d=\"M5.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path d=\"M11 16.5v8.5h1v-8.5z\"/><path d=\"M11 4v8.5h1v-8.5z\"/><path d=\"M11.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "43BO": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"m6 13a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2m1 0a2 2 0 0 0 1 1h12a2 2 0 0 0 1-1v-9a2 2 0 0 0-1-1H8a2 2 0 0 0-1 1m6 5a1 1 0 0 1 2 0v2a1 1 0 0 1-2 0m-3-9V8a1 1 0 0 1 8 0v3h-1V8a1 1 0 0 0-6 0v3\"/></svg>";
  },
  "43aP": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.275 21.432l12.579-12.579-.707-.707-12.579 12.579z\"/><path d=\"M6.69 13.397l7.913 7.913.707-.707-7.913-7.913zM7.149 10.558l7.058-7.058-.707-.707-7.058 7.058z\"/><path d=\"M18.149 21.558l7.058-7.058-.707-.707-7.058 7.058z\"/><path d=\"M5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 13c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM16.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "4rDL": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M12.435 6.136c-4.411.589-7.983 3.039-9.085 6.27l.946.323c.967-2.836 4.209-5.059 8.271-5.602l-.132-.991zM3.347 16.584c1.101 3.243 4.689 5.701 9.117 6.283l.13-.991c-4.079-.537-7.335-2.767-8.301-5.613l-.947.321zM16.554 22.865c4.381-.582 7.94-3 9.071-6.2l-.943-.333c-.994 2.811-4.224 5.006-8.26 5.542l.132.991zM25.646 12.394c-1.107-3.225-4.675-5.668-9.078-6.257l-.133.991c4.056.542 7.293 2.76 8.265 5.591l.946-.325z\"/><path d=\"M14.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM14.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM25.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "6oLA": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M4.034 14.18l-.07-.18.07-.18c1.535-3.975 5.645-6.82 9.966-6.82 4.32 0 8.431 2.845 9.966 6.82l.07.18-.07.18c-1.535 3.975-5.646 6.82-9.966 6.82-4.321 0-8.431-2.845-9.966-6.82zm9.966 5.82c3.84 0 7.521-2.503 8.962-6-1.441-3.497-5.122-6-8.962-6-3.841 0-7.521 2.503-8.962 6 1.441 3.497 5.121 6 8.962 6z\"/><path d=\"M11 14.001c0 1.66 1.341 2.999 3.001 2.999s2.999-1.339 2.999-2.999c0-1.66-1.339-3.001-2.999-3.001-1.66 0-3.001 1.341-3.001 3.001z\"/></g></svg>";
  },
  "7jlp": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M6 21.586l3.586-3.586h13.407c.004 0 .007-11.993.007-11.993 0-.007-17-.007-17-.007v15.586zm-1 2.414v-18.005c0-.549.451-.995.995-.995h17.01c.549 0 .995.45.995 1.007v11.986c0 .556-.45 1.007-1.007 1.007h-12.993l-5 5z\"/></svg>";
  },
  "82rr": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M7.382 16h14.483l-4.167-5 4.167-5h-15.865v12.764l1.382-2.764zm-2.382 7v-18h19l-5 6 5 6h-16l-3 6z\"/></svg>";
  },
  "8I/E": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M3.5 11h21v-1h-21z\"/><path d=\"M3.5 18h21v-1h-21z\"/><path d=\"M10 3.5v21h1v-21z\"/><path d=\"M17 3.5v21h1v-21z\"/><path d=\"M22.5 4v-1h-19.5v19.5h1v-18.5z\"/><path d=\"M24 24h-18.507v1h19.507v-19.5h-1z\"/><path d=\"M3.5 26c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 5c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "9QR3": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4 2v19.5h1v-19.5zM15.5 10h-11v1h11zM17 12.5v11h1v-11zM6.29 22.417l10.162-10.162-.707-.707-10.162 10.162z\" id=\"Line\"/><path d=\"M19.264 9.443l6.589-6.589-.707-.707-6.589 6.589z\"/><path d=\"M6.577 23.381l19.071-5.903-.296-.955-19.071 5.903z\"/><path d=\"M5.573 21.724l5.905-19.076-.955-.296-5.905 19.076z\"/><path d=\"M6.5 24h19.5v-1h-19.5z\"/><path d=\"M4.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "9wu9": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M8.5 23h11v-1h-11zM6 8.5v12h1v-12zM7.483 8.28l12.293 13.112.73-.684-12.293-13.112z\"/><path d=\"M6.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM6.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM21.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  Awez: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M6 6v6.5h1v-6.5zM7 22v-2.5h-1v2.5zM11 11v2.5h1v-2.5zM12 24v-7.5h-1v7.5zM16 5v5.5h1v-5.5zM17 21v-2.5h-1v2.5zM21 7v4.5h1v-4.5zM22 19v-2.5h-1v2.5z\"/><path d=\"M6 13v6h1v-6h-1zm-1-1h3v8h-3v-8z\"/><path d=\"M11 16h1v-2h-1v2zm-1-3h3v4h-3v-4z\"/><path d=\"M16 18h1v-7h-1v7zm-1-8h3v9h-3v-9z\"/><path d=\"M21 16h1v-4h-1v4zm-1-5h3v6h-3v-6z\"/></g></svg>";
  },
  BZH7: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M6.5 23v1h17.5v-17.5h-1v16.5z\"/><path fill-rule=\"nonzero\" d=\"M21.5 5v-1h-17.5v17.5h1v-16.5z\"/><path fill-rule=\"nonzero\" d=\"M4.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path fill-rule=\"nonzero\" d=\"M13 9v13h1v-13z\" id=\"Line\"/><path d=\"M13.5 6l2.5 3h-5z\"/><path fill-rule=\"nonzero\" d=\"M19 14h-13v1h13z\"/><path d=\"M19 17v-5l3 2.5z\"/></g></svg>";
  },
  Csdk: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><circle fill=\"currentColor\" cx=\"14\" cy=\"14\" r=\"3\"/></svg>";
  },
  D1H9: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M20 14h-14v1h14z\" id=\"Line\"/><path d=\"M20 17v-5l3 2.5z\"/><path fill-rule=\"nonzero\" d=\"M24 8.5v16.5h1v-16.5zM4 4v16.5h1v-16.5z\"/><path fill-rule=\"nonzero\" d=\"M4.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  EWkI: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M23.886 21.431c-.953-8.558-7.742-15.354-16.299-16.315l-.112.994c8.093.909 14.516 7.338 15.417 15.432l.994-.111z\"/><path d=\"M5 7.5v14h1v-14zM21.5 23h-14v1h14z\"/><path d=\"M5.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  EyuB: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.5 23h13v-1h-13z\"/><path d=\"M7.55 13.088l13.29-6.254-.426-.905-13.29 6.254z\"/><path d=\"M5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 15c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  FVBd: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"evenodd\"><path fill-rule=\"nonzero\" d=\"M23.002 23C23 23 23 18.003 23 18.003L15.998 18C16 18 16 22.997 16 22.997l7.002.003zM15 18.003A1 1 0 0 1 15.998 17h7.004c.551 0 .998.438.998 1.003v4.994A1 1 0 0 1 23.002 24h-7.004A.993.993 0 0 1 15 22.997v-4.994z\"/><path d=\"M19 20h1v2h-1z\"/><path fill-rule=\"nonzero\" d=\"M22 17.5v-2a2.5 2.5 0 0 0-5 0v2h1v-2a1.5 1.5 0 0 1 3 0v2h1z\"/><g fill-rule=\"nonzero\"><path d=\"M3 14.707A1 1 0 0 1 3.293 14L14.439 2.854a1.5 1.5 0 0 1 2.122 0l2.585 2.585a1.5 1.5 0 0 1 0 2.122L8 18.707a1 1 0 0 1-.707.293H4a1 1 0 0 1-1-1v-3.293zm1 0V18h3.293L18.439 6.854a.5.5 0 0 0 0-.708l-2.585-2.585a.5.5 0 0 0-.708 0L4 14.707z\"/><path d=\"M13.146 4.854l4 4 .708-.708-4-4zm-9 9l4 4 .708-.708-4-4z\"/><path d=\"M15.146 6.146l-9 9 .708.708 9-9z\"/></g></g></svg>";
  },
  G1jy: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M15.039 5.969l-.019-.019-2.828 2.828.707.707 2.474-2.474c1.367-1.367 3.582-1.367 4.949 0s1.367 3.582 0 4.949l-2.474 2.474.707.707 2.828-2.828-.019-.019c1.415-1.767 1.304-4.352-.334-5.99-1.638-1.638-4.224-1.749-5.99-.334zM5.97 15.038l-.019-.019 2.828-2.828.707.707-2.475 2.475c-1.367 1.367-1.367 3.582 0 4.949s3.582 1.367 4.949 0l2.474-2.474.707.707-2.828 2.828-.019-.019c-1.767 1.415-4.352 1.304-5.99-.334-1.638-1.638-1.749-4.224-.334-5.99z\"/><path d=\"M10.485 16.141l5.656-5.656.707.707-5.656 5.656z\"/></g></svg>";
  },
  G3Kc: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M6 6h19v-1h-19z\" id=\"Line\"/><path fill-rule=\"nonzero\" d=\"M6 24h19v-1h-19z\"/><path fill-rule=\"nonzero\" d=\"M6 13h17v-1h-17z\"/><path d=\"M14.5 7l2.5 3h-5z\"/><path d=\"M14.5 22l2.5-3h-5z\"/><path fill-rule=\"nonzero\" d=\"M14 10v10h1v-10z\"/><path fill-rule=\"nonzero\" d=\"M4.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  Ijvb: function(o, e, l) {
    "use strict";
    l.d(e, "a", function() {
      return t;
    });
    var t = {
      SyncDrawing: l("G1jy"),
      arrow: l("tceb"),
      cursor: l("WHEt"),
      dot: l("Csdk"),
      drawginmode: l("2dtg"),
      drawginmodeActive: l("FVBd"),
      eraser: l("2lje"),
      group: l("lZXH"),
      hideAllDrawings: l("6oLA"),
      hideAllDrawingsActive: l("dmHa"),
      lockAllDrawings: l("Uh5y"),
      lockAllDrawingsActive: l("43BO"),
      magnet: l("3s8f"),
      strongMagnet: l("xjKU"),
      measure: l("oCKS"),
      removeAllDrawingTools: l("aVjL"),
      showObjectTree: l("qQ3E"),
      zoom: l("kmdM"),
      "zoom-out": l("mbEK"),
    };
  },
  JEPD: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M16 17h6v-6h-6v-4.865l-9.438 7.865 9.438 7.865v-4.865zm7 1h-6v6l-12-10 12-10v6h6v8z\"/></svg>";
  },
  JR4H: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M20.449 8.505l2.103 9.112.974-.225-2.103-9.112zM13.943 14.011l7.631 4.856.537-.844-7.631-4.856zM14.379 11.716l4.812-3.609-.6-.8-4.812 3.609zM10.96 13.828l-4.721 6.744.819.573 4.721-6.744zM6.331 20.67l2.31-13.088-.985-.174-2.31 13.088zM9.041 7.454l1.995 3.492.868-.496-1.995-3.492z\"/><path d=\"M8.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM12.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM20.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  JsLp: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M4.529 18.21l3.157-1.292-.379-.926-3.157 1.292z\"/><path fill-rule=\"nonzero\" d=\"M9.734 16.081l2.97-1.215-.379-.926-2.97 1.215z\"/><path fill-rule=\"nonzero\" d=\"M14.725 14.039l2.957-1.21-.379-.926-2.957 1.21z\"/><path fill-rule=\"nonzero\" d=\"M19.708 12.001l3.114-1.274-.379-.926-3.114 1.274z\"/><path d=\"M8 18h1v3h-1z\" id=\"Path\"/><path d=\"M8 9h1v5h-1z\"/><path fill-rule=\"nonzero\" d=\"M8 18h1v-4h-1v4zm-1-5h3v6h-3v-6z\" id=\"Rectangle-44\"/><path d=\"M18 16h1v3h-1z\" id=\"Path\"/><path d=\"M18 3h1v6h-1z\"/><path fill-rule=\"nonzero\" d=\"M18 16h1v-7h-1v7zm-1-8h3v9h-3v-9z\" id=\"Rectangle-44\"/><path d=\"M13 6h1v5h-1z\"/><path d=\"M13 15h1v5h-1z\"/><path fill-rule=\"nonzero\" d=\"M13 15h1v-4h-1v4zm-1-5h3v6h-3v-6z\"/><path fill-rule=\"nonzero\" d=\"M2.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 11c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  M4my: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.463 12.026l13.537-7.167-.468-.884-13.537 7.167z\"/><path d=\"M22.708 16.824l-17.884 9.468.468.884 17.884-9.468z\"/><path d=\"M22.708 9.824l-15.839 8.386.468.884 15.839-8.386z\"/><path d=\"M5.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 5c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "MP+M": function(o, e, l) {
    "use strict";
    l("YFKU");
    var t = l("+GxX"),
      i = l("/DW5"),
      n = (l("HbRj"), {
        LineTool5PointsPattern: l("JR4H"),
        LineToolABCD: l("yeNt"),
        LineToolArc: l("XSJB"),
        LineToolArrow: l("nkas"),
        LineToolArrowMarker: l("l833"),
        LineToolArrowMarkDown: l("o7ef"),
        LineToolArrowMarkLeft: l("JEPD"),
        LineToolArrowMarkRight: l("pQ6k"),
        LineToolArrowMarkUp: l("chXN"),
        LineToolBalloon: l("Y3NG"),
        LineToolBarsPattern: l("Awez"),
        LineToolBezierCubic: l("VSwc"),
        LineToolBezierQuadro: l("po04"),
        LineToolBrush: l("WF4l"),
        LineToolCallout: l("7jlp"),
        LineToolCircleLines: l("4+EX"),
        LineToolCypherPattern: l("yppm"),
        LineToolDateAndPriceRange: l("BZH7"),
        LineToolDateRange: l("D1H9"),
        LineToolDisjointAngle: l("mDnV"),
        LineToolElliottCorrection: l("cv0m"),
        LineToolElliottDoubleCombo: l("gtlc"),
        LineToolElliottImpulse: l("W3cW"),
        LineToolElliottTriangle: l("N98A"),
        LineToolElliottTripleCombo: l("TyyN"),
        LineToolEllipse: l("4rDL"),
        LineToolExtended: l("S2na"),
        LineToolFibChannel: l("M4my"),
        LineToolFibCircles: l("hL0p"),
        LineToolFibRetracement: l("fhJv"),
        LineToolFibSpeedResistanceArcs: l("39u9"),
        LineToolFibSpeedResistanceFan: l("9QR3"),
        LineToolFibSpiral: l("eKNX"),
        LineToolFibTimeZone: l("xIA3"),
        LineToolFibWedge: l("0Q2B"),
        LineToolFlagMark: l("82rr"),
        LineToolFlatBottom: l("EyuB"),
        LineToolAnchoredVWAP: "",
        LineToolGannComplex: l("W0qL"),
        LineToolGannFixed: l("V53V"),
        LineToolGannFan: l("11T/"),
        LineToolGannSquare: l("8I/E"),
        LineToolGhostFeed: l("JsLp"),
        LineToolHeadAndShoulders: l("jqYn"),
        LineToolHorzLine: l("V9Qq"),
        LineToolHorzRay: l("qSes"),
        LineToolIcon: "icon",
        LineToolInsidePitchfork: l("2fn7"),
        LineToolNote: l("0Ez2"),
        LineToolNoteAbsolute: l("2gmH"),
        LineToolParallelChannel: l("P8r3"),
        LineToolPitchfan: l("iaAB"),
        LineToolPitchfork: l("43aP"),
        LineToolPolyline: l("XkCl"),
        LineToolPath: l("yk2I"),
        LineToolPrediction: l("Z6JI"),
        LineToolPriceLabel: l("RxfQ"),
        LineToolPriceRange: l("v23R"),
        LineToolProjection: l("EWkI"),
        LineToolRay: l("NKMx"),
        LineToolRectangle: l("a/b0"),
        LineToolRegressionTrend: "",
        LineToolRiskRewardLong: l("tD/+"),
        LineToolRiskRewardShort: l("G3Kc"),
        LineToolRotatedRectangle: l("y6kn"),
        LineToolSchiffPitchfork: l("zQJQ"),
        LineToolSchiffPitchfork2: l("qXlY"),
        LineToolSineLine: l("wemi"),
        LineToolText: l("w++H"),
        LineToolTextAbsolute: l("exgs"),
        LineToolThreeDrivers: l("e4c6"),
        LineToolTimeCycles: l("Yo3o"),
        LineToolTrendAngle: l("mD8t"),
        LineToolTrendBasedFibExtension: l("v+GK"),
        LineToolTrendBasedFibTime: l("tdX5"),
        LineToolTrendLine: l("oIsX"),
        LineToolInfoLine: l("b5Ss"),
        LineToolTriangle: l("9wu9"),
        LineToolTrianglePattern: l("O4df"),
        LineToolVertLine: l("Ukrx"),
        LineToolCrossLine: l("v030"),
      }),
      h = l("Ijvb");
    l.d(e, "a", function() {
      return z;
    });
    var r = { keys: ["Shift"], text: window.t("{0} — drawing a straight line at angles of 45") },
      c = { keys: ["Shift"], text: window.t("{0} — circle") },
      s = { keys: ["Shift"], text: window.t("{0} — square") },
      z = {
        LineTool5PointsPattern: { icon: n.LineTool5PointsPattern, localizedName: window.t("XABCD Pattern") },
        LineToolABCD: { icon: n.LineToolABCD, localizedName: window.t("ABCD Pattern") },
        LineToolArc: { icon: n.LineToolArc, localizedName: window.t("Arc") },
        LineToolArrow: { icon: n.LineToolArrow, localizedName: window.t("Arrow") },
        LineToolArrowMarkDown: { icon: n.LineToolArrowMarkDown, localizedName: window.t("Arrow Mark Down") },
        LineToolArrowMarkLeft: { icon: n.LineToolArrowMarkLeft, localizedName: window.t("Arrow Mark Left") },
        LineToolArrowMarkRight: { icon: n.LineToolArrowMarkRight, localizedName: window.t("Arrow Mark Right") },
        LineToolArrowMarkUp: { icon: n.LineToolArrowMarkUp, localizedName: window.t("Arrow Mark Up") },
        LineToolBalloon: { icon: n.LineToolBalloon, localizedName: window.t("Balloon") },
        LineToolBarsPattern: { icon: n.LineToolBarsPattern, localizedName: window.t("Bars Pattern") },
        LineToolBezierCubic: { icon: n.LineToolBezierCubic, localizedName: window.t("Double Curve") },
        LineToolBezierQuadro: { icon: n.LineToolBezierQuadro, localizedName: window.t("Curve") },
        LineToolBrush: { icon: n.LineToolBrush, localizedName: window.t("Brush") },
        LineToolCallout: { icon: n.LineToolCallout, localizedName: window.t("Callout") },
        LineToolCircleLines: { icon: n.LineToolCircleLines, localizedName: window.t("Cyclic Lines") },
        LineToolCypherPattern: { icon: n.LineToolCypherPattern, localizedName: window.t("Cypher Pattern") },
        LineToolDateAndPriceRange: {
          icon: n.LineToolDateAndPriceRange,
          localizedName: window.t("Date and Price Range"),
        },
        LineToolDateRange: { icon: n.LineToolDateRange, localizedName: window.t("Date Range") },
        LineToolDisjointAngle: {
          icon: n.LineToolDisjointAngle,
          localizedName: window.t("Disjoint Channel"),
          hotKey: Object(i.b)(r),
        },
        LineToolElliottCorrection: {
          icon: n.LineToolElliottCorrection,
          localizedName: window.t("Elliott Correction Wave (ABC)"),
        },
        LineToolElliottDoubleCombo: {
          icon: n.LineToolElliottDoubleCombo,
          localizedName: window.t("Elliott Double Combo Wave (WXY)"),
        },
        LineToolElliottImpulse: {
          icon: n.LineToolElliottImpulse,
          localizedName: window.t("Elliott Impulse Wave (12345)"),
        },
        LineToolElliottTriangle: {
          icon: n.LineToolElliottTriangle,
          localizedName: window.t("Elliott Triangle Wave (ABCDE)"),
        },
        LineToolElliottTripleCombo: {
          icon: n.LineToolElliottTripleCombo,
          localizedName: window.t("Elliott Triple Combo Wave (WXYXZ)"),
        },
        LineToolEllipse: { icon: n.LineToolEllipse, localizedName: window.t("Ellipse"), hotKey: Object(i.b)(c) },
        LineToolExtended: { icon: n.LineToolExtended, localizedName: window.t("Extended") },
        LineToolFibChannel: { icon: n.LineToolFibChannel, localizedName: window.t("Fib Channel") },
        LineToolFibCircles: {
          icon: n.LineToolFibCircles,
          localizedName: window.t("Fib Circles"),
          hotKey: Object(i.b)(c),
        },
        LineToolFibRetracement: { icon: n.LineToolFibRetracement, localizedName: window.t("Fib Retracement") },
        LineToolFibSpeedResistanceArcs: {
          icon: n.LineToolFibSpeedResistanceArcs,
          localizedName: window.t("Fib Speed Resistance Arcs"),
        },
        LineToolFibSpeedResistanceFan: {
          icon: n.LineToolFibSpeedResistanceFan,
          localizedName: window.t("Fib Speed Resistance Fan"),
          hotKey: Object(i.b)(s),
        },
        LineToolFibSpiral: { icon: n.LineToolFibSpiral, localizedName: window.t("Fib Spiral") },
        LineToolFibTimeZone: { icon: n.LineToolFibTimeZone, localizedName: window.t("Fib Time Zone") },
        LineToolFibWedge: { icon: n.LineToolFibWedge, localizedName: window.t("Fib Wedge") },
        LineToolFlagMark: { icon: n.LineToolFlagMark, localizedName: window.t("Flag Mark") },
        LineToolFlatBottom: {
          icon: n.LineToolFlatBottom,
          localizedName: window.t("Flat Top/Bottom"),
          hotKey: Object(i.b)(r),
        },
        LineToolAnchoredVWAP: { icon: n.LineToolAnchoredVWAP, localizedName: window.t("Anchored VWAP") },
        LineToolGannComplex: { icon: n.LineToolGannComplex, localizedName: window.t("Gann Square") },
        LineToolGannFixed: { icon: n.LineToolGannFixed, localizedName: window.t("Gann Square Fixed") },
        LineToolGannFan: { icon: n.LineToolGannFan, localizedName: window.t("Gann Fan") },
        LineToolGannSquare: {
          icon: n.LineToolGannSquare,
          localizedName: window.t("Gann Box"),
          hotKey: Object(i.b)({ keys: ["Shift"], text: window.t("{0} — fixed increments") }),
        },
        LineToolHeadAndShoulders: { icon: n.LineToolHeadAndShoulders, localizedName: window.t("Head and Shoulders") },
        LineToolHorzLine: {
          icon: n.LineToolHorzLine,
          localizedName: window.t("Horizontal Line"),
          hotKey: Object(i.b)({ keys: ["Alt", "H"], text: "{0} + {1}" }),
        },
        LineToolHorzRay: { icon: n.LineToolHorzRay, localizedName: window.t("Horizontal Ray") },
        LineToolIcon: { icon: n.LineToolIcon, localizedName: window.t("Font Icons") },
        LineToolInsidePitchfork: { icon: n.LineToolInsidePitchfork, localizedName: window.t("Inside Pitchfork") },
        LineToolNote: { icon: n.LineToolNote, localizedName: window.t("Note") },
        LineToolNoteAbsolute: { icon: n.LineToolNoteAbsolute, localizedName: window.t("Anchored Note") },
        LineToolParallelChannel: {
          icon: n.LineToolParallelChannel,
          localizedName: window.t("Parallel Channel"),
          hotKey: Object(i.b)(r),
        },
        LineToolPitchfan: { icon: n.LineToolPitchfan, localizedName: window.t("Pitchfan") },
        LineToolPitchfork: { icon: n.LineToolPitchfork, localizedName: window.t("Pitchfork") },
        LineToolPolyline: { icon: n.LineToolPolyline, localizedName: window.t("Polyline") },
        LineToolPath: { icon: n.LineToolPath, localizedName: window.t("Path") },
        LineToolPrediction: { icon: n.LineToolPrediction, localizedName: window.t("Forecast") },
        LineToolPriceLabel: { icon: n.LineToolPriceLabel, localizedName: window.t("Price Label") },
        LineToolArrowMarker: { icon: n.LineToolArrowMarker, localizedName: window.t("Arrow Marker") },
        LineToolPriceRange: { icon: n.LineToolPriceRange, localizedName: window.t("Price Range") },
        LineToolProjection: { icon: n.LineToolProjection, localizedName: window.t("Projection") },
        LineToolRay: { icon: n.LineToolRay, localizedName: window.t("Ray") },
        LineToolRectangle: {
          icon: n.LineToolRectangle,
          localizedName: window.t("Rectangle"),
          hotKey: Object(i.b)({ keys: ["Shift"], text: window.t("{0} — square") }),
        },
        LineToolRegressionTrend: { icon: n.LineToolRegressionTrend, localizedName: window.t("Regression Trend") },
        LineToolRiskRewardLong: { icon: n.LineToolRiskRewardLong, localizedName: window.t("Long Position") },
        LineToolRiskRewardShort: { icon: n.LineToolRiskRewardShort, localizedName: window.t("Short Position") },
        LineToolRotatedRectangle: {
          icon: n.LineToolRotatedRectangle,
          localizedName: window.t("Rotated Rectangle"),
          hotKey: Object(i.b)(r),
        },
        LineToolSchiffPitchfork: {
          icon: n.LineToolSchiffPitchfork,
          localizedName: window.t("Modified Schiff Pitchfork"),
        },
        LineToolSchiffPitchfork2: { icon: n.LineToolSchiffPitchfork2, localizedName: window.t("Schiff Pitchfork") },
        LineToolSineLine: { icon: n.LineToolSineLine, localizedName: window.t("Sine Line") },
        LineToolText: { icon: n.LineToolText, localizedName: window.t("Text", { context: "tool" }) },
        LineToolTextAbsolute: { icon: n.LineToolTextAbsolute, localizedName: window.t("Anchored Text") },
        LineToolThreeDrivers: { icon: n.LineToolThreeDrivers, localizedName: window.t("Three Drives Pattern") },
        LineToolTimeCycles: { icon: n.LineToolTimeCycles, localizedName: window.t("Time Cycles") },
        LineToolTrendAngle: {
          icon: n.LineToolTrendAngle,
          localizedName: window.t("Trend Angle"),
          hotKey: Object(i.b)(r),
        },
        LineToolTrendBasedFibExtension: {
          icon: n.LineToolTrendBasedFibExtension,
          localizedName: window.t("Trend-Based Fib Extension"),
        },
        LineToolTrendBasedFibTime: {
          icon: n.LineToolTrendBasedFibTime,
          localizedName: window.t("Trend-Based Fib Time"),
        },
        LineToolTrendLine: { icon: n.LineToolTrendLine, localizedName: window.t("Trend Line"), hotKey: Object(i.b)(r) },
        LineToolInfoLine: { icon: n.LineToolInfoLine, localizedName: window.t("Info Line") },
        LineToolTriangle: { icon: n.LineToolTriangle, localizedName: window.t("Triangle") },
        LineToolTrianglePattern: { icon: n.LineToolTrianglePattern, localizedName: window.t("Triangle Pattern") },
        LineToolVertLine: {
          icon: n.LineToolVertLine,
          localizedName: window.t("Vertical Line"),
          hotKey: Object(i.b)({ keys: ["Alt", "V"], text: "{0} + {1}" }),
        },
        LineToolCrossLine: { icon: n.LineToolCrossLine, localizedName: $.t("Cross Line") },
        SyncDrawing: {
          icon: h.a.SyncDrawing,
          iconActive: h.a.SyncDrawingActive,
          localizedName: window.t(
            "New drawings are replicated to all charts in the layout and shown when the same ticker is selected",
          ),
        },
        arrow: { icon: h.a.arrow, localizedName: window.t("Arrow") },
        cursor: { icon: h.a.cursor, localizedName: window.t("Cross") },
        dot: { icon: h.a.dot, localizedName: window.t("Dot") },
        drawginmode: {
          icon: h.a.drawginmode,
          iconActive: h.a.drawginmodeActive,
          localizedName: window.t("Stay in Drawing Mode"),
        },
        eraser: { icon: h.a.eraser, localizedName: window.t("Eraser") },
        group: { icon: h.a.group, localizedName: window.t("Show Hidden Tools") },
        hideAllDrawings: {
          icon: h.a.hideAllDrawings,
          iconActive: h.a.hideAllDrawingsActive,
          localizedName: window.t("Hide All Drawing Tools"),
          hotKey: Object(i.b)({ keys: ["Ctrl", "Alt", "H"], text: "{0} + {1} + {2}" }),
        },
        lockAllDrawings: {
          icon: h.a.lockAllDrawings,
          iconActive: h.a.lockAllDrawingsActive,
          localizedName: window.t("Lock All Drawing Tools"),
        },
        magnet: {
          icon: h.a.magnet,
          localizedName: window.t("Magnet Mode snaps drawings placed near price bars to the closest OHLC value"),
          hotKey: Object(i.b)({ keys: ["Ctrl"], text: "{0}" }),
        },
        measure: {
          icon: h.a.measure,
          localizedName: window.t("Measure"),
          hotKey: Object(i.b)({ keys: ["Shift"], text: window.t("{0} + Click on the chart") }),
        },
        removeAllDrawingTools: { icon: h.a.removeAllDrawingTools, localizedName: window.t("Remove Drawings") },
        showObjectsTree: { icon: h.a.showObjectTree, localizedName: window.t("Show Object Tree") },
        zoom: { icon: h.a.zoom, localizedName: window.t("Zoom In") },
        "zoom-out": { icon: h.a["zoom-out"], localizedName: window.t("Zoom Out") },
      };
    Object(t.isFeatureEnabled)("remove-line-tool-ghost-feed")
      || (z.LineToolGhostFeed = { icon: n.LineToolGhostFeed, localizedName: window.t("Ghost Feed") });
  },
  N98A: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M5.238 18.469l4.17-4.17-.707-.707-4.17 4.17zM16.47 17.763l-.707.707-4.265-4.265.707-.707zM22.747 13.546l-4.192 4.192.707.707 4.192-4.192z\"/><path fill-rule=\"nonzero\" d=\"M10.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path d=\"M13.746 7h-1.258l-.5-1.301h-2.289l-.473 1.301h-1.227l2.23-5.727h1.223l2.293 5.727zm-2.129-2.266l-.789-2.125-.773 2.125h1.563z\"/><path d=\"M22.582 7v-5.727h4.246v.969h-3.09v1.27h2.875v.965h-2.875v1.559h3.199v.965z\"/></g></svg>";
  },
  NKMx: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M8.354 20.354l5-5-.707-.707-5 5z\"/><path d=\"M16.354 12.354l8-8-.707-.707-8 8z\"/><path d=\"M14.5 15c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM6.5 23c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  O4df: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M9.457 18.844l-5.371 2.4.408.913 5.371-2.4z\"/><path d=\"M13.13 17.203l.408.913 13.688-6.116-6.736-3.01-.408.913 4.692 2.097z\"/><path d=\"M11.077 5.88l5.34 2.386.408-.913-5.34-2.386z\"/><path d=\"M7.401 4.237l.408-.913-5.809-2.595v19.771h1v-18.229z\"/><path d=\"M3.708 20.772l5.51-14.169-.932-.362-5.51 14.169zM9.265 6.39l1.46 10.218.99-.141-1.46-10.218zM13.059 17.145l4.743-6.775-.819-.573-4.743 6.775z\"/><path d=\"M9.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM11.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 10c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM2.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  P8r3: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M8.354 18.354l10-10-.707-.707-10 10zM12.354 25.354l5-5-.707-.707-5 5z\"/><path d=\"M20.354 17.354l5-5-.707-.707-5 5z\"/><path d=\"M19.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM6.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  RxfQ: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M6.995 5c.008 0 .005 15.5.005 15.5h-1v-15.493c0-.556.451-1.007.995-1.007h17.01c.549 0 .995.45.995 1.007v11.986c0 .556-.45 1.007-1.007 1.007h-12.993l-3.104 3.104-.707-.707 3.397-3.397h13.407c.004 0 .007-11.993.007-11.993 0-.007-17.005-.007-17.005-.007z\"/><path d=\"M6.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  S2na: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4.354 25.354l5-5-.707-.707-5 5z\"/><path d=\"M12.354 17.354l5-5-.707-.707-5 5z\"/><path d=\"M20.354 9.354l5-5-.707-.707-5 5z\"/><path d=\"M18.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM10.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  TyyN: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M5.238 18.469l4.17-4.17-.707-.707-4.17 4.17zM16.47 17.763l-.707.707-4.265-4.265.707-.707zM22.747 13.546l-4.192 4.192.707.707 4.192-4.192z\"/><path fill-rule=\"nonzero\" d=\"M10.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path d=\"M8.395 7l-1.367-5.727h1.184l.863 3.934 1.047-3.934h1.375l1.004 4 .879-4h1.164l-1.391 5.727h-1.227l-1.141-4.281-1.137 4.281z\"/><path d=\"M22.086 7v-1.043l3.008-3.715h-2.668v-.969h4.191v.898l-3.137 3.863h3.258v.965z\"/></g></svg>";
  },
  Uh5y: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"m6 13a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2m1 0a2 2 0 0 0 1 1h12a2 2 0 0 0 1-1v-9a2 2 0 0 0-1-1H8a2 2 0 0 0-1 1m6 5a1 1 0 0 1 2 0v2a1 1 0 0 1-2 0m-3-9V7a1 1 0 0 1 8 0h-1a1 1 0 0 0-6 0v4\"/></svg>";
  },
  Ukrx: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M15 12.5v-8.5h-1v8.5zM14 16.5v8.5h1v-8.5z\"/><path d=\"M14.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  V53V: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><g fill=\"none\" stroke=\"currentColor\" transform=\"translate(1 3)\"><path d=\"M4.5 21.5h19V.5h-21v19m0-5h5m4 0h12M3 7.5h20M16.5 1v20\"/><path d=\"M9.5.5v12m0 4v5m-5.6-1.4l4.2-4.2M11 13L23 1M4.4 21l19.1-6M3.1 19.6L9 .5m14.5 21a21 21 0 0 0-21-21\"/><circle cx=\"2.5\" cy=\"21.5\" r=\"2\"/><circle cx=\"9.5\" cy=\"14.5\" r=\"2\"/></g></svg>";
  },
  V9Qq: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4 15h8.5v-1h-8.5zM16.5 15h8.5v-1h-8.5z\"/><path d=\"M14.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  VSwc: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M17.605 19.584c.295-2.304-.584-3.586-3.274-5.959-2.462-2.172-3.191-3.241-2.94-5.111l-.991-.133c-.312 2.322.567 3.61 3.269 5.994 2.452 2.163 3.181 3.227 2.943 5.083l.992.127z\"/><path d=\"M6.053 24.868c3.772 1.644 7.307 1.643 9.712-1.249l-.769-.64c-2.045 2.458-5.133 2.459-8.544.972l-.4.917z\"/><path d=\"M12.949 5.087c2.044-2.525 5.157-2.54 8.602-1.036l.4-.917c-3.81-1.663-7.376-1.646-9.779 1.324l.777.629z\"/><path d=\"M4.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM16.5 23c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM11.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  W0qL: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M22.5 4v-1h-19.5v19.5h1v-18.5z\"/><path d=\"M5.493 24v1h19.507v-19.5h-1v18.5z\"/><path d=\"M5.275 23.432l18.213-18.213-.707-.707-18.213 18.213z\"/><path d=\"M5.568 24.383l19.079-5.906-.296-.955-19.079 5.906z\" id=\"Line-Copy-18\"/><path d=\"M4.587 22.68l5.891-19.032-.955-.296-5.891 19.032z\"/><path d=\"M3.5 11h21v-1h-21z\"/><path d=\"M3.5 18h21v-1h-21z\"/><path d=\"M10 3.5v21h1v-21z\"/><path d=\"M17 3.5v21h1v-21z\"/><path d=\"M23.975 23.475l1.025 1.025c0-11.874-9.626-21.5-21.5-21.5l1.025 1.025c10.506.517 18.932 8.944 19.45 19.45z\"/><path d=\"M3.5 26c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 5c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  W3cW: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M5.238 18.469l4.17-4.17-.707-.707-4.17 4.17zM16.47 17.763l-.707.707-4.265-4.265.707-.707zM22.747 13.546l-4.192 4.192.707.707 4.192-4.192z\"/><path fill-rule=\"nonzero\" d=\"M10.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path d=\"M11.148 7h-1.098v-4.137c-.401.375-.874.652-1.418.832v-.996c.286-.094.598-.271.934-.533.336-.262.566-.567.691-.916h.891v5.75z\"/><path d=\"M23.355 5.527l1.094-.113c.031.247.124.443.277.588.154.145.331.217.531.217.229 0 .423-.093.582-.279.159-.186.238-.467.238-.842 0-.352-.079-.615-.236-.791-.158-.176-.363-.264-.615-.264-.315 0-.598.139-.848.418l-.891-.129.563-2.98h2.902v1.027h-2.07l-.172.973c.245-.122.495-.184.75-.184.487 0 .9.177 1.238.531.339.354.508.814.508 1.379 0 .471-.137.892-.41 1.262-.372.505-.889.758-1.551.758-.529 0-.96-.142-1.293-.426-.333-.284-.533-.665-.598-1.145z\"/></g></svg>";
  },
  WF4l: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M1.789 23l.859-.854.221-.228c.18-.19.38-.409.597-.655.619-.704 1.238-1.478 1.815-2.298.982-1.396 1.738-2.776 2.177-4.081 1.234-3.667 5.957-4.716 8.923-1.263 3.251 3.785-.037 9.38-5.379 9.38h-9.211zm9.211-1c4.544 0 7.272-4.642 4.621-7.728-2.45-2.853-6.225-2.015-7.216.931-.474 1.408-1.273 2.869-2.307 4.337-.599.852-1.241 1.653-1.882 2.383l-.068.078h6.853z\"/><path d=\"M18.182 6.002l-1.419 1.286c-1.031.935-1.075 2.501-.096 3.48l1.877 1.877c.976.976 2.553.954 3.513-.045l5.65-5.874-.721-.693-5.65 5.874c-.574.596-1.507.609-2.086.031l-1.877-1.877c-.574-.574-.548-1.48.061-2.032l1.419-1.286-.672-.741z\"/></g></svg>";
  },
  WHEt: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path d=\"M18 15h8v-1h-8z\"/><path d=\"M14 18v8h1v-8zM14 3v8h1v-8zM3 15h8v-1h-8z\"/></g></svg>";
  },
  XSJB: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M8.013 23.846c4.923 2.247 9.792 2.626 13.448-.517l-.652-.758c-3.277 2.817-7.775 2.467-12.381.365l-.415.91z\"/><path d=\"M24.035 20.056c2.262-4.176 1.814-8.73-.13-12.98l-.909.416c1.826 3.993 2.244 8.24.16 12.088l.879.476z\"/><path d=\"M8.221 21.401l13.249-14.077-.728-.685-13.249 14.077z\"/><path d=\"M6.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 23c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  XkCl: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M16.5 6h4v-1h-4z\" id=\"Line\"/><path d=\"M16.5 15h4v-1h-4z\"/><path d=\"M8.5 23h4v-1h-4z\"/><path d=\"M8.298 11.591l5.097-4.46-.659-.753-5.097 4.46zM22 7.5v5h1v-5z\"/><path d=\"M14 16.5v4h1v-4z\"/><path d=\"M6 14.5v6h1v-6z\"/><path d=\"M6.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM14.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM14.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM6.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM14.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  Y3NG: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M9.586 18h7.414c3.309 0 6-2.689 6-6 0-3.309-2.687-6-5.994-6h-5.012c-3.306 0-5.994 2.686-5.994 5.994v9.592l3.586-3.586zm-4.586 6v-12.006c0-3.863 3.137-6.994 6.994-6.994h5.012c3.863 0 6.994 3.142 6.994 7 0 3.866-3.142 7-7 7h-7l-5 5z\"/></svg>";
  },
  Yo3o: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M27.186 9.411c-.691-.27-1.429-.411-2.186-.411-3.314 0-6 2.686-6 6h1c0-2.761 2.239-5 5-5 .632 0 1.246.117 1.821.342l.364-.931z\"/><path fill-rule=\"nonzero\" d=\"M9 15c0-3.314-2.686-6-6-6-.754 0-1.489.139-2.177.407l.363.932c.573-.223 1.185-.339 1.814-.339 2.761 0 5 2.239 5 5h1z\"/><path d=\"M8 15h1v1h-1v-1zm11 0h1v1h-1v-1zm1 0h-1c0-2.761-2.239-5-5-5s-5 2.239-5 5h-1c0-3.314 2.686-6 6-6s6 2.686 6 6z\"/><path fill-rule=\"nonzero\" d=\"M8.5 19c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM19.5 19c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  Z6JI: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path d=\"M19 11h5l-2.5 3z\"/><circle cx=\"21.5\" cy=\"16.5\" r=\"1.5\"/><path fill-rule=\"nonzero\" d=\"M22 11v-6h-1v6z\"/><path d=\"M14 18h1v3h-1z\" id=\"Path\"/><path d=\"M14 5h1v6h-1z\"/><path d=\"M7 19h1v3h-1z\"/><path d=\"M7 6h1v7h-1z\"/><path fill-rule=\"nonzero\" d=\"M7 13v6h1v-6h-1zm-1-1h3v8h-3v-8zM14 18h1v-7h-1v7zm-1-8h3v9h-3v-9z\"/></g></svg>";
  },
  "a/b0": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.5 6h13v-1h-13z\" id=\"Line\"/><path d=\"M7.5 23h13v-1h-13z\"/><path d=\"M5 7.5v13h1v-13z\"/><path d=\"M22 7.5v13h1v-13z\"/><path d=\"M5.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  aVjL: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M8 21c0 1.1.825 2 1.833 2h7.333c1.008 0 1.833-.9 1.833-2v-12h-11v12zm-1 0v-13h13v13c0 1.634-1.252 3-2.833 3h-7.333c-1.581 0-2.833-1.366-2.833-3z\"/><path d=\"M17 6l-1-1h-5l-1 1h-3v1h13v-1z\"/><path fill-rule=\"nonzero\" d=\"M10 11v9.062h1v-9.062z\"/><path fill-rule=\"nonzero\" d=\"M13 11v9.062h1v-9.062z\"/><path fill-rule=\"nonzero\" d=\"M16 11v9.062h1v-9.062z\"/></g></svg>";
  },
  b2d7: function(o, e, l) {
    "use strict";
    l.d(e, "a", function() {
      return i;
    });
    var t, i, n = l("aIyQ"), h = l.n(n), r = l("Vdly");
    !function(o) {
      function e() {
        o.favorites = [],
          Object(r.getJSON)("chart.favoriteDrawings", []).forEach(function(e) {
            o.favorites.push(e.tool || e);
          }),
          o.favoritesSynced.fire();
      }
      o.favorites = [],
        o.favoritesSynced = new h.a(),
        o.favoriteIndex = function(e) {
          return o.favorites.indexOf(e);
        },
        o.saveFavorites = function() {
          Object(r.setJSON)("chart.favoriteDrawings", o.favorites);
        },
        e(),
        r.onSync.subscribe(null, e);
    }(t || (t = {})),
      function(o) {
        function e() {
          return t.favorites.length;
        }
        function l(o) {
          return -1 !== t.favoriteIndex(o);
        }
        o.favoriteAdded = new h.a(),
          o.favoriteRemoved = new h.a(),
          o.favoriteMoved = new h.a(),
          o.favoritesSynced = t.favoritesSynced,
          o.favorites = function() {
            return t.favorites.slice();
          },
          o.favoritesCount = e,
          o.favorite = function(o) {
            return o < 0 || o >= e() ? "" : t.favorites[o];
          },
          o.addFavorite = function(e) {
            return !l(e) && (t.favorites.push(e), t.saveFavorites(), o.favoriteAdded.fire(e), !0);
          },
          o.removeFavorite = function(e) {
            var l = t.favoriteIndex(e);
            return -1 !== l && (t.favorites.splice(l, 1), t.saveFavorites(), o.favoriteRemoved.fire(e), !0);
          },
          o.isFavorite = l,
          o.moveFavorite = function(l, i) {
            if (i < 0 || i >= e()) return !1;
            var n = t.favoriteIndex(l);
            return -1 !== n && i !== n
              && (t.favorites.splice(n, 1),
                t.favorites.splice(i, 0, l),
                t.saveFavorites(),
                o.favoriteMoved.fire(l, n, i),
                !0);
          };
      }(i || (i = {}));
  },
  b5Ss: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\" clip-rule=\"evenodd\"><path d=\"M22.4989 4C21.6705 4 20.9989 4.67157 20.9989 5.5C20.9989 5.91456 21.1664 6.28904 21.4387 6.56106C21.7106 6.83282 22.0848 7 22.4989 7C23.3274 7 23.9989 6.32843 23.9989 5.5C23.9989 4.67157 23.3274 4 22.4989 4ZM19.9989 5.5C19.9989 4.11929 21.1182 3 22.4989 3C23.8796 3 24.9989 4.11929 24.9989 5.5C24.9989 6.88071 23.8796 8 22.4989 8C21.9899 8 21.5159 7.8475 21.1209 7.58617L7.58575 21.1214C7.84733 21.5165 8 21.9907 8 22.5C8 23.8807 6.88071 25 5.5 25C4.11929 25 3 23.8807 3 22.5C3 21.1193 4.11929 20 5.5 20C6.00932 20 6.48351 20.1527 6.87864 20.4143L20.4136 6.87929C20.1518 6.48403 19.9989 6.0096 19.9989 5.5ZM5.5 21C4.67157 21 4 21.6716 4 22.5C4 23.3284 4.67157 24 5.5 24C6.32843 24 7 23.3284 7 22.5C7 22.0856 6.83265 21.7113 6.56066 21.4393C6.28867 21.1673 5.91435 21 5.5 21Z\"/><path d=\"M16 19.5C16 18.1193 17.1193 17 18.5 17H23.5C24.8807 17 26 18.1193 26 19.5V22.5C26 23.8807 24.8807 25 23.5 25H18.5C17.1193 25 16 23.8807 16 22.5V19.5ZM18.5 18C17.6716 18 17 18.6716 17 19.5V22.5C17 23.3284 17.6716 24 18.5 24H23.5C24.3284 24 25 23.3284 25 22.5V19.5C25 18.6716 24.3284 18 23.5 18H18.5Z\"/></g></svg>";
  },
  chXN: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M11 16v6h6v-6h4.865l-7.865-9.438-7.865 9.438h4.865zm7 7h-8v-6h-6l10-12 10 12h-6v6z\"/></svg>";
  },
  cv0m: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M5.238 18.469l4.17-4.17-.707-.707-4.17 4.17zM16.47 17.763l-.707.707-4.265-4.265.707-.707zM22.747 13.546l-4.192 4.192.707.707 4.192-4.192z\"/><path fill-rule=\"nonzero\" d=\"M10.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path d=\"M13.746 7h-1.258l-.5-1.301h-2.289l-.473 1.301h-1.227l2.23-5.727h1.223l2.293 5.727zm-2.129-2.266l-.789-2.125-.773 2.125h1.563z\"/><path d=\"M26.246 4.895l1.121.355c-.172.625-.458 1.089-.857 1.393-.4.303-.907.455-1.521.455-.76 0-1.385-.26-1.875-.779-.49-.52-.734-1.23-.734-2.131 0-.953.246-1.693.738-2.221.492-.527 1.139-.791 1.941-.791.701 0 1.27.207 1.707.621.26.245.456.596.586 1.055l-1.145.273c-.068-.297-.209-.531-.424-.703-.215-.172-.476-.258-.783-.258-.424 0-.769.152-1.033.457-.264.305-.396.798-.396 1.48 0 .724.13 1.24.391 1.547.26.307.599.461 1.016.461.307 0 .572-.098.793-.293.221-.195.38-.503.477-.922z\"/></g></svg>";
  },
  dmHa: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M17.537 7.635l-.789.789c-.886-.275-1.812-.424-2.748-.424-3.841 0-7.521 2.503-8.962 6 .562 1.364 1.465 2.577 2.585 3.549l-.709.709c-1.265-1.112-2.274-2.506-2.881-4.077l-.07-.18.07-.18c1.535-3.975 5.645-6.82 9.966-6.82 1.213 0 2.409.224 3.537.635zm3.549 2.108c1.265 1.112 2.274 2.506 2.881 4.077l.07.18-.07.18c-1.535 3.975-5.646 6.82-9.966 6.82-1.213 0-2.409-.224-3.537-.635l.789-.789c.886.275 1.812.424 2.748.424 3.84 0 7.521-2.503 8.962-6-.562-1.364-1.465-2.577-2.585-3.549l.709-.709zm-6.049.392l-4.902 4.902c-.088-.33-.135-.677-.135-1.036 0-2.213 1.788-4.001 4.001-4.001.358 0 .705.047 1.036.135zm2.828 2.829c.088.331.135.679.135 1.038 0 2.213-1.786 3.999-3.999 3.999-.359 0-.707-.047-1.038-.135l4.901-4.901zm-12.365 10.243l-.707-.707 17.707-17.707.707.707-17.707 17.707z\"/></svg>";
  },
  e4c6: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M.303 17.674l1.104.473.394-.919-1.104-.473z\"/><path d=\"M5.133 19.744l3.335 1.429.394-.919-3.335-1.429z\"/><path d=\"M12.134 22.744l3.352 1.436.394-.919-3.352-1.436z\"/><path d=\"M19.203 25.774l1.6.686.394-.919-1.6-.686z\"/><path d=\"M.3 4.673l1.13.484.394-.919-1.13-.484-.394.919zm.394-.919l1.13.484-.394.919-1.13-.484.394-.919z\"/><path d=\"M5.141 6.747l3.325 1.425.394-.919-3.325-1.425z\"/><path d=\"M12.133 9.744l3.353 1.437.394-.919-3.353-1.437z\"/><path d=\"M19.221 12.782l5.838 2.502.394-.919-5.838-2.502z\"/><path d=\"M3 7.473v8.969h1v-8.969zM8.93 9.871l-4.616 6.594.819.573 4.616-6.594zM11 19.5v-9h-1v9zM15.898 12.916l-4.616 6.594.819.573 4.616-6.594zM18 22.5v-9h-1v9zM24.313 5.212l-6.57 17.247.934.356 6.57-17.247z\"/><path d=\"M3.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM10.5 23c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 13c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM25.5 5c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 26c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM10.5 10c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  eKNX: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4.395 10.18c3.432-4.412 10.065-4.998 13.675-.973l.745-.668c-4.044-4.509-11.409-3.858-15.209 1.027l.789.614z\"/><path d=\"M19.991 12.494c.877 2.718.231 5.487-1.897 7.543-2.646 2.556-6.752 2.83-9.188.477-1.992-1.924-2.027-5.38-.059-7.281 1.582-1.528 3.78-1.587 5.305-.115 1.024.99 1.386 2.424.876 3.491l.902.431c.709-1.482.232-3.37-1.084-4.641-1.921-1.855-4.734-1.78-6.695.115-2.378 2.297-2.337 6.405.059 8.719 2.846 2.749 7.563 2.435 10.577-.477 2.407-2.325 3.147-5.493 2.154-8.569l-.952.307z\"/><path d=\"M21.01 9.697l3.197-3.197-.707-.707-3.197 3.197z\"/><path d=\"M14.989 15.719l3.674-3.674-.707-.707-3.674 3.674z\"/><path d=\"M13.5 18c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM19.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  exgs: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"m6.5 5C5.68 5 5 5.67 5 6.5v2h1v-2c0-.27.23-.5.5-.5H11v16H9v1h5v-1h-2V6h4.5c.28 0 .5.22.5.5v2h1v-2c0-.83-.67-1.5-1.5-1.5h-10zM25 19c0 2.48-2.02 4-4.5 4S16 21.49 16 19h1c0 1.8 1.42 3 3.5 3s3.5-1.2 3.5-3m-6-2h5v1h-5m3-3.5v8h-1v-8m.5.5a1 1 0 0 1 0-3 1 1 0 0 1 0 3\"/></svg>";
  },
  fhJv: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M3 5h22v-1h-22z\"/><path d=\"M3 17h22v-1h-22z\"/><path d=\"M3 11h19.5v-1h-19.5z\"/><path d=\"M5.5 23h19.5v-1h-19.5z\"/><path d=\"M3.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  gtlc: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M5.238 18.469l4.17-4.17-.707-.707-4.17 4.17zM16.47 17.763l-.707.707-4.265-4.265.707-.707zM22.747 13.546l-4.192 4.192.707.707 4.192-4.192z\"/><path fill-rule=\"nonzero\" d=\"M10.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM17.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path d=\"M8.395 7l-1.367-5.727h1.184l.863 3.934 1.047-3.934h1.375l1.004 4 .879-4h1.164l-1.391 5.727h-1.227l-1.141-4.281-1.137 4.281z\"/><path d=\"M24.086 7v-2.41l-2.098-3.316h1.355l1.348 2.266 1.32-2.266h1.332l-2.105 3.324v2.402z\"/></g></svg>";
  },
  hL0p: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M23.868 7.825c2.791 3.916 2.918 9.33-.065 13.435-3.733 5.138-10.925 6.277-16.063 2.544l.721-.714c4.682 3.294 11.157 2.229 14.534-2.418 2.641-3.635 2.657-8.502.153-12.133l.721-.714z\"/><path d=\"M8.477 5.899c3.584-2.509 8.298-2.514 11.865-.127l.718-.721c-3.845-2.669-9.099-2.813-13.157.028-5.203 3.643-6.467 10.814-2.824 16.016l.718-.721c-3.201-4.737-2.022-11.185 2.68-14.476z\"/><path d=\"M14.5 22c4.142 0 7.5-3.358 7.5-7.5 0-4.142-3.358-7.5-7.5-7.5-4.142 0-7.5 3.358-7.5 7.5 0 4.142 3.358 7.5 7.5 7.5zm0 1c-4.694 0-8.5-3.806-8.5-8.5s3.806-8.5 8.5-8.5 8.5 3.806 8.5 8.5-3.806 8.5-8.5 8.5z\"/><path d=\"M14.5 19c2.485 0 4.5-2.015 4.5-4.5s-2.015-4.5-4.5-4.5-4.5 2.015-4.5 4.5 2.015 4.5 4.5 4.5zm0 1c-3.038 0-5.5-2.462-5.5-5.5s2.462-5.5 5.5-5.5 5.5 2.462 5.5 5.5-2.462 5.5-5.5 5.5z\"/><path d=\"M22.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM6.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  iaAB: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M20.349 20.654l4.489-.711-.156-.988-4.489.711z\"/><path d=\"M7.254 22.728l9.627-1.525-.156-.988-9.627 1.525z\"/><path d=\"M7.284 22.118l15.669-8.331-.469-.883-15.669 8.331z\"/><path d=\"M6.732 21.248l8.364-15.731-.883-.469-8.364 15.731z\"/><path d=\"M17.465 18.758l-8.188-8.188-.707.707 8.188 8.188z\"/><path d=\"M6.273 20.818l1.499-9.467-.988-.156-1.499 9.467z\"/><path d=\"M8.329 7.834l.715-4.516-.988-.156-.715 4.516z\"/><path d=\"M7.354 21.354l17-17-.707-.707-17 17z\"/><path d=\"M5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM7.5 11c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 22c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  jqYn: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4.436 21.667l2.083-9.027-.974-.225-2.083 9.027zM10.046 16.474l-2.231-4.463-.894.447 2.231 4.463zM13.461 6.318l-2.88 10.079.962.275 2.88-10.079zM18.434 16.451l-2.921-10.224-.962.275 2.921 10.224zM21.147 12.089l-2.203 4.405.894.447 2.203-4.405zM25.524 21.383l-2.09-9.055-.974.225 2.09 9.055z\"/><path d=\"M1 19h7.5v-1h-7.5z\"/><path d=\"M12.5 19h4v-1h-4z\"/><path d=\"M20.5 19h6.5v-1h-6.5z\"/><path d=\"M6.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM10.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM25.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM14.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  kmdM: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\" fill=\"currentColor\"><path d=\"M17.646 18.354l4 4 .708-.708-4-4z\"/><path d=\"M12.5 21a8.5 8.5 0 1 1 0-17 8.5 8.5 0 0 1 0 17zm0-1a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z\"/><path d=\"M9 13h7v-1H9z\"/><path d=\"M13 16V9h-1v7z\"/></svg>";
  },
  l833: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\" fill=\"currentColor\"><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.38 4.04h.04a1.3 1.3 0 0 1 1.21.33c.***********.33 1.2v.04l-2.45 8.32c-.14.55-.57.91-1 1.04-.45.14-1.2.07-1.55-.63l-.34-.64-12.6 10.03c-.3.25-.67.31-1 .25a1.3 1.3 0 0 1-1.01-1.06c-.05-.31.02-.69.29-.99l.02-.02a35.13 35.13 0 0 0 .47-.57 1749.6 1749.6 0 0 0 5-6.15l3.7-4.57.91-1.13-.63-.33c-.7-.36-.77-1.1-.64-1.55.13-.44.5-.86 1.03-1l8.22-2.57zm-7.95 3.53c-.35.08-.5.56-.2.7l1.71.91-.64.8a17294.8 17294.8 0 0 1-10.24 12.6l-.01.02c-.***********.33.35l13.55-10.78.92 1.7c.**********.7-.2l2.44-8.31c.05-.24-.1-.4-.35-.35l-8.2 2.56zM5.8 23.27z\"/></svg>";
  },
  lZXH: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 30 30\" width=\"30\" height=\"30\"><path fill=\"currentColor\" d=\"M5.5 13A2.5 2.5 0 0 0 3 15.5 2.5 2.5 0 0 0 5.5 18 2.5 2.5 0 0 0 8 15.5 2.5 2.5 0 0 0 5.5 13zm9.5 0a2.5 2.5 0 0 0-2.5 2.5A2.5 2.5 0 0 0 15 18a2.5 2.5 0 0 0 2.5-2.5A2.5 2.5 0 0 0 15 13zm9.5 0a2.5 2.5 0 0 0-2.5 2.5 2.5 2.5 0 0 0 2.5 2.5 2.5 2.5 0 0 0 2.5-2.5 2.5 2.5 0 0 0-2.5-2.5z\"/></svg>";
  },
  mD8t: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M15.5 22.5c0-2.697-1.073-5.225-2.947-7.089l-.705.709c1.687 1.679 2.652 3.952 2.652 6.38h1z\"/><path d=\"M7.354 21.354l14-14-.707-.707-14 14z\"/><path d=\"M7.5 23h16.5v-1h-16.5z\"/><path d=\"M22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  mDnV: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M20.534 4.664l-13.318 4.701.333.943 13.318-4.701zM20.802 22.371l-13.285-4.689-.333.943 13.285 4.689z\"/><path d=\"M5.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 19c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  mbEK: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\" fill=\"currentColor\"><path d=\"M17.646 18.354l4 4 .708-.708-4-4z\"/><path d=\"M12.5 21a8.5 8.5 0 1 1 0-17 8.5 8.5 0 0 1 0 17zm0-1a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z\"/><path d=\"M9 13h7v-1H9z\"/></svg>";
  },
  nkas: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M7.354 21.354l14-14-.707-.707-14 14z\"/><path d=\"M21 7l-8 3 5 5z\"/><path fill-rule=\"nonzero\" d=\"M22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  o7ef: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M17 12v-6h-6v6h-4.865l7.865 9.438 7.865-9.438h-4.865zm-7-7h8v6h6l-10 12-10-12h6v-6z\"/></svg>";
  },
  oCKS: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M2 9.75a1.5 1.5 0 0 0-1.5 1.5v5.5a1.5 1.5 0 0 0 1.5 1.5h24a1.5 1.5 0 0 0 1.5-1.5v-5.5a1.5 1.5 0 0 0-1.5-1.5zm0 1h3v2.5h1v-2.5h3.25v3.9h1v-3.9h3.25v2.5h1v-2.5h3.25v3.9h1v-3.9H22v2.5h1v-2.5h3a.5.5 0 0 1 .5.5v5.5a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5v-5.5a.5.5 0 0 1 .5-.5z\" transform=\"rotate(-45 14 14)\"/></svg>";
  },
  oIsX: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.354 21.354l14-14-.707-.707-14 14z\"/><path d=\"M22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  pQ6k: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M12 11h-6v6h6v4.865l9.438-7.865-9.438-7.865v4.865zm-7 7v-8h6v-6l12 10-12 10v-6h-6z\"/></svg>";
  },
  po04: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M6.256 20.652c.548-3.024 1.607-5.962 3.329-8.312l-.807-.591c-1.825 2.493-2.933 5.565-3.506 8.725l.984.178z\"/><path d=\"M12.243 9.657c2.365-1.764 5.345-2.846 8.416-3.402l-.178-.984c-3.21.581-6.326 1.712-8.836 3.584l.598.802z\"/><path d=\"M10.5 12c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  qQ3E: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M14 18.634l-.307-.239-7.37-5.73-2.137-1.665 9.814-7.633 9.816 7.634-.509.394-1.639 1.269-7.667 5.969zm7.054-6.759l1.131-.876-8.184-6.366-8.186 6.367 1.123.875 7.063 5.491 7.054-5.492z\"/><path d=\"M7 14.5l-1 .57 8 6.43 8-6.5-1-.5-7 5.5z\"/><path d=\"M7 17.5l-1 .57 8 6.43 8-6.5-1-.5-7 5.5z\"/></g></svg>";
  },
  qSes: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M8.5 15h16.5v-1h-16.5z\"/><path d=\"M6.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  qXlY: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M10.275 20.432l11.579-11.579-.707-.707-11.579 11.579z\"/><path d=\"M8.336 13.043l8.621 8.621.707-.707-8.621-8.621zM9.149 10.558l7.058-7.058-.707-.707-7.058 7.058z\" id=\"Line\"/><path d=\"M20.149 21.558l7.058-7.058-.707-.707-7.058 7.058z\"/><path d=\"M6.5 23h10v-1h-10z\"/><path d=\"M4.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM7.5 13c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "tD/+": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M6 6h19v-1h-19z\" id=\"Line\"/><path fill-rule=\"nonzero\" d=\"M6 24h19v-1h-19z\"/><path fill-rule=\"nonzero\" d=\"M6 17h17v-1h-17z\"/><path d=\"M14.5 7l2.5 3h-5z\"/><path d=\"M14.5 22l2.5-3h-5z\"/><path fill-rule=\"nonzero\" d=\"M14 10v10h1v-10z\"/><path fill-rule=\"nonzero\" d=\"M4.5 18c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM24.5 18c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  tceb: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M11.682 16.09l3.504 6.068 1.732-1-3.497-6.057 3.595-2.1L8 7.74v10.512l3.682-2.163zm-.362 1.372L7 20V6l12 7-4.216 2.462 3.5 6.062-3.464 2-3.5-6.062z\"/></svg>";
  },
  tdX5: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M20 2v22h1v-22z\"/><path d=\"M24 2v22h1v-22z\"/><path d=\"M4.673 11.471l3.69 10.333.942-.336-3.69-10.333z\"/><path d=\"M17 21.535v-19.535h-1v19.535z\"/><path d=\"M11.5 24h3v-1h-3z\"/><path d=\"M4.5 11c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM9.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM16.5 25c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  "v+GK": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4 25h22v-1h-22z\" id=\"Line\"/><path d=\"M4 21h22v-1h-22z\"/><path d=\"M6.5 17h19.5v-1h-19.5z\"/><path d=\"M5 14.5v-3h-1v3zM6.617 9.275l10.158-3.628-.336-.942-10.158 3.628z\"/><path d=\"M18.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 11c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 18c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  v030: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M4 15h8.5v-1h-8.5zM16.5 15h8.5v-1h-8.5z\"/><path d=\"M15 12v-8.5h-1v8.5zM14 16.5v8.5h1v-8.5z\"/><path d=\"M14.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  v23R: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path fill-rule=\"nonzero\" d=\"M4 5h16.5v-1h-16.5zM25 24h-16.5v1h16.5z\" id=\"Line\"/><path fill-rule=\"nonzero\" d=\"M6.5 26c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM22.5 6c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/><path fill-rule=\"nonzero\" d=\"M14 9v14h1v-14z\"/><path d=\"M14.5 6l2.5 3h-5z\"/></g></svg>";
  },
  "w++H": function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"m9.5 5C8.68 5 8 5.67 8 6.5v2h1v-2c0-.27.23-.5.5-.5H14v16h-2v1h5v-1h-2V6h4.5c.28 0 .5.22.5.5v2h1v-2c0-.83-.67-1.5-1.5-1.5h-10z\"/></svg>";
  },
  wemi: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M5.205 14.111l-.013-.172c-.054-.906.022-1.966.327-2.961.395-1.289 1.111-2.215 2.221-2.666l-.377-.926c-1.42.578-2.324 1.746-2.8 3.3-.346 1.128-.429 2.299-.369 3.313l.017.219.994-.106z\"/><path d=\"M11.051 8.554c1.12.893 1.766 2.343 2.973 6.099 1.274 3.963 1.947 5.48 3.28 6.557l.629-.778c-1.112-.899-1.754-2.345-2.956-6.085-1.28-3.982-1.957-5.503-3.302-6.575l-.623.782z\"/><path d=\"M21.722 21.558c1.439-.634 2.428-1.886 3.046-3.551.311-.836.509-1.726.618-2.616.066-.539.088-.967.088-1.227l-1 .002-.01.304c-.012.241-.035.51-.071.798-.101.818-.282 1.634-.563 2.39-.533 1.434-1.354 2.474-2.512 2.985l.403.915z\"/><path d=\"M9.5 9c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM19.5 23c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  xIA3: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M17 4v21h1v-21z\"/><path d=\"M23 4v21h1v-21z\"/><path d=\"M5 16.5v8.5h1v-8.5z\"/><path d=\"M5 4v8.5h1v-8.5z\"/><path d=\"M11 16.5v8.5h1v-8.5z\"/><path d=\"M11 4v8.5h1v-8.5z\"/><path d=\"M5.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM11.5 16c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  xjKU: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" fill-rule=\"nonzero\" d=\"M14 5a7 7 0 0 0-7 7v3h4v-3a3 3 0 1 1 6 0v3h4v-3a7 7 0 0 0-7-7zm7 11h-4v3h4v-3zm-10 0H7v3h4v-3zm-5-4a8 8 0 1 1 16 0v8h-6v-8a2 2 0 1 0-4 0v8H6v-8zm3.293 11.294l-1.222-2.037.858-.514 1.777 2.963-2 1 1.223 2.037-.858.514-1.778-2.963 2-1zm9.778-2.551l.858.514-1.223 2.037 2 1-1.777 2.963-.858-.514 1.223-2.037-2-1 1.777-2.963z\"/></svg>";
  },
  y6kn: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M14.743 3.55l-4.208 4.208.707.707 4.208-4.208zM7.71 10.583l-4.187 4.187.707.707 4.187-4.187zM3.536 18.244l6.171 6.171.707-.707-6.171-6.171zM13.232 24.475l4.22-4.22-.707-.707-4.22 4.22zM20.214 17.494l4.217-4.217-.707-.707-4.217 4.217zM24.423 9.716l-6.218-6.218-.707.707 6.218 6.218z\"/><path d=\"M2.5 18c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM9.5 11c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM16.5 4c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM11.5 27c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 20c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM25.5 13c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  yeNt: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M21.487 5.248l-12.019 1.502.124.992 12.019-1.502zM6.619 9.355l-2.217 11.083.981.196 2.217-11.083zM6.534 22.75l12.071-1.509-.124-.992-12.071 1.509zM21.387 18.612l2.21-11.048-.981-.196-2.21 11.048zM8.507 9.214l10.255 10.255.707-.707-10.255-10.255z\"/><path d=\"M7.5 9c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM4.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM20.5 22c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  yk2I: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M11 10.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm4 7a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0zm11-8.8V13h1V7h-6v1h4.3l-7.42 7.41a2.49 2.49 0 0 0-2.76 0l-3.53-3.53a2.5 2.5 0 1 0-4.17 0L1 18.29l.7.71 6.42-6.41a2.49 2.49 0 0 0 2.76 0l3.53 3.53a2.5 2.5 0 1 0 4.17 0z\"/></svg>";
  },
  yppm: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\"><path d=\"M15.246 21.895l1.121.355c-.172.625-.458 1.089-.857 1.393-.4.303-.907.455-1.521.455-.76 0-1.385-.26-1.875-.779-.49-.52-.734-1.23-.734-2.131 0-.953.246-1.693.738-2.221.492-.527 1.139-.791 1.941-.791.701 0 1.27.207 1.707.621.26.245.456.596.586 1.055l-1.145.273c-.068-.297-.209-.531-.424-.703-.215-.172-.476-.258-.783-.258-.424 0-.769.152-1.033.457-.264.305-.396.798-.396 1.48 0 .724.13 1.24.391 1.547.26.307.599.461 1.016.461.307 0 .572-.098.793-.293.221-.195.38-.503.477-.922z\"/><path fill-rule=\"nonzero\" d=\"M20.449 8.505l2.103 9.112.974-.225-2.103-9.112zM13.943 14.011l7.631 4.856.537-.844-7.631-4.856zM14.379 11.716l4.812-3.609-.6-.8-4.812 3.609zM10.96 13.828l-4.721 6.744.819.573 4.721-6.744zM6.331 20.67l2.31-13.088-.985-.174-2.31 13.088zM9.041 7.454l1.995 3.492.868-.496-1.995-3.492z\"/><path fill-rule=\"nonzero\" d=\"M8.5 7c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM5.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM12.5 14c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM20.5 8c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM23.5 21c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
  zQJQ: function(o, e) {
    o.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><g fill=\"currentColor\" fill-rule=\"nonzero\"><path d=\"M7.854 22.854l14-14-.707-.707-14 14z\"/><path d=\"M8.336 13.043l8.621 8.621.707-.707-8.621-8.621zM9.149 10.558l7.058-7.058-.707-.707-7.058 7.058z\" id=\"Line\"/><path d=\"M20.149 21.558l7.058-7.058-.707-.707-7.058 7.058z\"/><path d=\"M5.5 23h11v-1h-11z\"/><path d=\"M7.5 13c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM18.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5zM3.5 24c.828 0 1.5-.672 1.5-1.5s-.672-1.5-1.5-1.5-1.5.672-1.5 1.5.672 1.5 1.5 1.5zm0 1c-1.381 0-2.5-1.119-2.5-2.5s1.119-2.5 2.5-2.5 2.5 1.119 2.5 2.5-1.119 2.5-2.5 2.5z\"/></g></svg>";
  },
}]);
