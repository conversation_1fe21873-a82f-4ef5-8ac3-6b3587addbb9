import clsx from 'clsx'
import type { GetServerSideProps } from 'next'
import Link from 'next/link'
import Script from 'next/script'
import { type FC, Fragment, type ReactNode, useEffect, useState } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { createRoot } from 'react-dom/client'
import Zoom from 'react-medium-image-zoom'
import { FeaturedMedia } from '~/src/components-news/Article/ArticleFeaturedMedia.component'
import ArticleAudioPlayer from '~/src/components-news/ArticleAudioPlayer/ArticleAudioPlayer'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardMobile } from '~/src/components-news/ArticleTeasers/TeaserCardMobile'
import { TeaserTextOnly } from '~/src/components-news/ArticleTeasers/TeaserTextOnly'
import { TeaserTextOnlyWithAuthor } from '~/src/components-news/ArticleTeasers/TeaserTextOnlyWithAuthor'
import AuthorDetails from '~/src/components-news/AuthorDetails/AuthorDetails'
import { useVideoPlayer } from '~/src/components-news/VideoPlayer/useVideoPlayer.util'
import ArticlePageEmpty from '~/src/components/Article/ArticlePageEmpty'
import BuySellButton from '~/src/components/BuySellButton/BuySellButton'
import CommentDrawer from '~/src/components/Comment/CommentDrawer'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import LayoutNewsLanding from '~/src/components/LayoutNewsLanding/LayoutNewsLanding'
import { GenericNewsListWithAuthor } from '~/src/components/generic-news-list/generic-news-list.component'
import { AuthorImage } from '~/src/components/image-with-fallback/image-with-fallback.component'
import {
  NewsCategoryTitle,
  NewsCategoryTitleDetailPage,
} from '~/src/components/news-category/news-category.component'
import NewsMeta from '~/src/components/news/meta'
import SocialsSideBar from '~/src/components/socials/SocialsSideBar'
import { SocialsKitco } from '~/src/components/socials/socials-kitco.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import { TagLink } from '~/src/components/tag-link/tag-link.component'
import type {
  ArticleTeaserFragmentFragment,
  Author,
  Commentary,
  NewsGenericCommentariesQuery,
  OpinionsByCategoryGenericQuery,
  Tag,
} from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import { opinions } from '~/src/lib/opinions-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type { TeasersUnion } from '~/src/types/types'
import { articleDate } from '~/src/utils/article-date.util'
import cs from '~/src/utils/cs'
import { useInfinite, useParams } from '~/src/utils/infiniteScroll'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import StrippedString from '~/src/utils/strippedString'
import useRecordView from '~/src/utils/useRecordView'
import useScreenSize from '~/src/utils/useScreenSize'
import styles from './opinion.module.scss'

interface OpinionProps {
  opinionData: Commentary
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const variablesCategory = {
    urlAlias: `/opinion/${ctx.query.opinion[0]}/${ctx.query.opinion[1]}`,
    limit: 4,
    offset: 0,
  }
  const auHash = ctx?.query?.auHash ?? ('' as any)

  const nodeUrlAlias = `/opinion/${ctx.query.opinion[0]}/${ctx.query.opinion[1]}`

  const { dehydratedState } = await ssrQueries({
    ctxRes: ctx.res,
    queries: [
      opinions.nodeByUrlAlias({
        variables: {
          urlAlias: nodeUrlAlias,
          auHash,
        },
      }),
      news.newsTrending({
        variables: { limit: 10 },
      }),
    ],
  })

  const manipulate = nodeUrlAlias
    ?.split('/')
    .filter((x: string) => x !== 'opinion')

  const dateRegex = /^\d{4}-\d{2}-\d{2}$/

  const isDate = dateRegex.test(manipulate[1])

  for (const x of dehydratedState.queries) {
    const queryKey = x.queryKey.find((item) => item?.urlAlias || false)
    if (queryKey?.urlAlias === nodeUrlAlias && !x.state.data.nodeByUrlAlias) {
      return { notFound: true }
    }
  }

  // cache control one hour
  ctx.res.setHeader('Cache-Control', 's-maxage=3600, stale-while-revalidate')

  return {
    props: {
      dehydratedState,
      urlAlias: nodeUrlAlias,
      variablesCategory,
      auHash,
      isDate,
    },
  }
}

const listAuthorStr = (data: Commentary) => {
  const authorData = data?.author
  const supportingAuthors = data?.supportingAuthors || []
  const unifyAuthors: <AUTHORS>

  let listAuthorString = 'By '
  unifyAuthors?.forEach((x, idx) => {
    listAuthorString += `${x?.name}${
      idx !== unifyAuthors?.length - 1 ? ' and ' : ''
    }`
  })

  return listAuthorString
}

const OpinionRouterPage: FC<any> = ({
  urlAlias,
  variablesCategory,
  auHash,
  isDate,
}) => {
  const { data } = kitcoQuery(
    opinions.nodeByUrlAlias({
      variables: { urlAlias, auHash },
      options: {
        enabled: true,
      },
    }),
  )

  const opinionData = data?.nodeByUrlAlias as Commentary
  const { isMobile } = useScreenSize()
  useRecordView(Boolean(opinionData), opinionData?.id)

  const pFirst = StrippedString(
    opinionData?.bodyWithEmbeddedMedia.value,
  )?.replace('&nbsp;', ' ')

  if (!opinionData) return <ArticlePageEmpty />

  if (isDate) {
    return (
      <LayoutNewsLanding title={`${data?.nodeByUrlAlias?.title} | Kitco News`}>
        <NewsMeta
          title={data?.nodeByUrlAlias?.title}
          description={pFirst}
          image={
            opinionData?.teaserImage?.detail?.default?.srcset ??
            opinionData?.image?.detail?.default?.srcset
          }
          authorTwitter={opinionData?.author?.twitterId}
        />
        {isMobile && <OpinionMobile opinionData={opinionData} />}
        {!isMobile && <OpinionDesktopAndTablet opinionData={opinionData} />}
      </LayoutNewsLanding>
    )
  }

  return <OpinionsSubcategory urlAlias={variablesCategory?.urlAlias} />
}

const OpinionDesktopAndTablet: FC<OpinionProps> = ({ opinionData }) => {
  return (
    <>
      <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <div className="flex w-[190px]">
          <NewsCategoryTitleDetailPage category={'/opinion'} />
        </div>
        <div className="w-[calc(100%_-_190px)] pl-10">
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {opinionData?.title}
          </h1>
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-full px-4 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <LeftContent opinionData={opinionData} />
        <OpinionContent opinionData={opinionData} />
      </div>
    </>
  )
}

const LeftContent: FC<OpinionProps> = ({ opinionData }) => {
  return (
    <div className="w-[190px]">
      <AuthorBlock
        authorData={opinionData?.author}
        publishDate={opinionData?.createdAt}
        updateDate={opinionData?.updatedAt}
        supportingAuthors={opinionData?.supportingAuthors}
      />
      {opinionData?.source !== null && (
        <>
          <Spacer className="h-[30px]" />
          <SourceBlock
            name={opinionData?.source?.name}
            description={opinionData?.source?.description}
            subtitle={opinionData?.source?.subtitle}
          />
        </>
      )}
      <AudioPlayer opinionData={opinionData} />
      <Spacer className="h-[30px]" />
      <AdvertisingSlot
        id={'left-rail-1'}
        className="sticky top-4 mx-auto hidden h-[600px] w-[160px] desktop:block no-print"
      />
    </div>
  )
}

const SideBarOpinion: FC<OpinionProps> = ({ opinionData }) => {
  return (
    <>
      <div
        className="sticky top-3 m-0 flex flex-col justify-start self-start border-0
        p-0 pr-4 align-baseline text-black md:flex md:items-center
        md:gap-16"
      >
        <aside
          className="m-0 flex w-full flex-col items-center justify-start
          gap-8 border-0 p-0 align-baseline"
        >
          <SocialsSideBar
            hidePrint={true}
            listAuthorStr={listAuthorStr(opinionData)}
          >
            <CommentDrawer
              className=""
              category="opinions"
              storyID={opinionData.id}
              elementID="OpinionCommentBoxDesktop"
            />
          </SocialsSideBar>
        </aside>
      </div>
    </>
  )
}

const OpinionContent: FC<OpinionProps> = ({ opinionData }) => {
  return (
    <div className="flex w-[calc(100%_-_190px)] flex-row-reverse gap-4 pl-10">
      <article>
        <div className="flex justify-items-end gap-10 lg:grid-cols-3">
          <div className="col-span-1 lg:col-span-2">
            <BulletNews summaryBullets={opinionData?.summaryBullets} />
            <FeaturedMedia articleData={opinionData} />
            <BodyBlock
              body={opinionData?.bodyWithEmbeddedMedia}
              exitsPresentationImage={
                !!opinionData?.image?.detail?.default?.srcset
              }
            />
            <Spacer className="h-6" />
            <AuthorDetails
              authorData={opinionData?.author}
              supportingAuthors={opinionData?.supportingAuthors}
            />
            {opinionData?.tags?.length ? (
              <>
                <Spacer className="h-6" />
                <Tags data={opinionData?.tags} />
              </>
            ) : (
              <></>
            )}
            <Spacer className="h-6" />
            <Disclaimer />
            <Spacer className="h-6" />
            {/* <ArticleComment />
            <Spacer className="h-6" /> */}
            <div
              className="dianomi_context min-h-[702px] w-full tablet:min-h-[400px]"
              data-dianomi-context-id="246"
            />
            <Script
              src="https://www.dianomi.com/js/contextfeed.js"
              id="dianomi_context_script"
            />
            <RelatedArticles
              currentNodeId={opinionData?.id}
              currentNodeCategory={opinionData?.category?.urlAlias}
            />
          </div>
          <aside className="hidden w-[300px] lg:col-span-1 lg:block">
            <AdvertisingSlot
              id={'right-rail-1'}
              className="mx-auto mb-5 hidden h-[250px] w-[300px] desktop:block no-print"
            />
            <BuySellButton
              width={240}
              containerClassName="flex justify-center py-5"
              buttonClassName="text-[14px]"
            />
            <div className="rounded-md border border-ktc-borders p-5">
              <TrendingNowSection />
            </div>
            <AdvertisingSlot
              id={'right-rail-2'}
              className="sticky top-4 mx-auto mt-5 hidden h-[600px] w-[300px] desktop:block no-print"
            />
          </aside>
        </div>
      </article>
      <SideBarOpinion opinionData={opinionData} />
    </div>
  )
}

const OpinionMobile: FC<OpinionProps> = ({ opinionData }) => {
  return (
    <div className="mx-auto flex w-full max-w-full px-5 md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
      <div className="w-full">
        <NewsCategoryTitleDetailPage category={'/opinion'} />
        <article>
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {opinionData?.title}
          </h1>
          <div className="grid grid-cols-1 gap-10 lg:grid-cols-3">
            <div className="col-span-1 lg:col-span-2">
              <BulletNews summaryBullets={opinionData?.summaryBullets} />
              <AuthorBlockMobile
                authorData={opinionData?.author}
                publishDate={opinionData?.createdAt}
                updateDate={opinionData?.updatedAt}
                supportingAuthors={opinionData?.supportingAuthors}
              />
              {opinionData?.source !== null && (
                <>
                  <Spacer className="h-[30px]" />
                  <SourceBlock
                    name={opinionData?.source?.name}
                    description={opinionData?.source?.description}
                    subtitle={opinionData?.source?.subtitle}
                  />
                </>
              )}
              <Spacer className="h-[30px]" />
              <div className="flex justify-between">
                <AudioPlayer opinionData={opinionData} isMobile={true} />
                <SocialsKitco
                  className={cs([
                    'mx-3',
                    opinionData?.audioTts?.assetUuid
                      ? 'w-1/2 justify-between'
                      : 'w-full justify-evenly',
                  ])}
                  hidePrint={true}
                  listAuthorStr={listAuthorStr(opinionData)}
                />
              </div>
              <Spacer className="h-[30px]" />
              <FeaturedMedia articleData={opinionData} />
              <BodyBlock
                body={opinionData?.bodyWithEmbeddedMedia}
                exitsPresentationImage={
                  !!opinionData?.image?.detail?.default?.srcset
                }
              />
              <Spacer className="h-6" />
              <AuthorDetails
                authorData={opinionData?.author}
                supportingAuthors={opinionData?.supportingAuthors}
              />
              {opinionData?.tags?.length ? (
                <>
                  <Spacer className="h-6" />
                  <Tags data={opinionData?.tags} />
                </>
              ) : (
                <></>
              )}
              <Spacer className="h-6" />
              <LowerShareBlock opinionData={opinionData} />
              <Spacer className="h-6" />
              <Disclaimer />
              <Spacer className="h-6" />
              {/* <ArticleComment />
              <Spacer className="h-6" /> */}
              <div
                className="dianomi_context min-h-[702px] w-full tablet:min-h-[400px]"
                data-dianomi-context-id="246"
              />
              <Script
                src="https://www.dianomi.com/js/contextfeed.js"
                id="dianomi_context_script"
              />
              <RelatedArticles
                currentNodeId={opinionData?.id}
                currentNodeCategory={opinionData?.category?.urlAlias}
              />
            </div>
          </div>
        </article>
      </div>
    </div>
  )
}

const AuthorBlock: FC<AuthorProps> = ({
  authorData,
  publishDate,
  updateDate,
  supportingAuthors = [],
}) => {
  const unifyAuthors: <AUTHORS>
  const isMultiAuthor: boolean = unifyAuthors?.length >= 2

  return (
    <>
      <div className="mt-5">
        <div className="flex w-full items-center -space-x-[0.75em] pb-2.5">
          {unifyAuthors?.map((x, idx) => (
            <AuthorImage
              src={x?.imageUrl}
              className={clsx(
                'h-20 w-20 rounded-full object-cover',
                isMultiAuthor ? 'border-2 border-white' : undefined,
              )}
              style={{
                zIndex: 100 - idx,
              }}
              urlAlias={x?.urlAlias}
              key={x.name}
            />
          ))}
        </div>
        <div>
          <h6 className="font-medium tracking-[0.0168em]">
            <span>By&nbsp;</span>
            {unifyAuthors?.map((x, idx) => (
              <Fragment key={idx}>
                <Link
                  href={x?.urlAlias || ''}
                  className="text-sm font-bold text-[#373737]"
                  key={x?.id + idx + x.name}
                >
                  {x?.name}
                </Link>
                {idx !== unifyAuthors?.length - 1 ? (
                  <span>&nbsp;and&nbsp;</span>
                ) : null}
              </Fragment>
            ))}
          </h6>
          <Spacer className="h-[30px]" />
          <div>
            <div className="text-xs font-bold text-ktc-desc-gray">
              Published:
            </div>
            <time className="text-xs text-ktc-gray">
              {articleDate(publishDate)}
            </time>
          </div>
          <div>
            <div className="mt-2 text-xs font-bold text-ktc-desc-gray">
              Updated:
            </div>
            <time className="text-xs text-ktc-gray">
              {articleDate(updateDate)}
            </time>
          </div>
        </div>
      </div>
    </>
  )
}

const AuthorBlockMobile: FC<AuthorProps> = ({
  authorData,
  publishDate,
  updateDate,
  supportingAuthors = [],
}) => {
  const unifyAuthors: <AUTHORS>
  const isMultiAuthor: boolean = unifyAuthors?.length >= 2

  return (
    <div className="flex justify-between">
      <div className="flex w-full -space-x-[0.75em]">
        {unifyAuthors?.map((x, idx) => (
          <AuthorImage
            src={x?.imageUrl}
            className={clsx(
              'h-10 w-10 rounded-full object-cover',
              isMultiAuthor ? 'border-2 border-white' : undefined,
            )}
            style={{
              zIndex: 100 - idx,
            }}
            urlAlias={x?.urlAlias}
            key={x?.id + idx}
          />
        ))}
        <div className="pl-6">
          <h6 className="text-[16px] font-normal leading-[23px] tracking-[0.0168em]">
            <span>By&nbsp;</span>
            {unifyAuthors?.map((x, idx) => (
              <Fragment key={idx}>
                <Link
                  href={x?.urlAlias || ''}
                  className="font-bold text-[#373737]"
                  key={x?.id + idx}
                >
                  {x?.name}
                </Link>
                {idx !== unifyAuthors?.length - 1 ? (
                  <span>&nbsp;and&nbsp;</span>
                ) : null}
              </Fragment>
            ))}
          </h6>
          <div>
            <span className="font-normal text-ktc-date-gray">
              Published&nbsp;
            </span>
            <time className="font-normal text-ktc-date-gray">
              {articleDate(publishDate)}
            </time>
          </div>
          <div>
            <span className="font-normal text-ktc-date-gray">
              Updated&nbsp;
            </span>
            <time className="font-normal text-ktc-date-gray">
              {articleDate(updateDate)}
            </time>
          </div>
        </div>
      </div>
    </div>
  )
}

const LowerShareBlock: FC<{ opinionData: Commentary }> = ({ opinionData }) => {
  return (
    <div className="flex w-full flex-col items-center gap-3">
      <h3 className="text-[16px] font-bold underline">Share</h3>
      <SocialsKitco
        className="gap-4"
        listAuthorStr={listAuthorStr(opinionData)}
      />
    </div>
  )
}

function BulletNews({ summaryBullets }: { summaryBullets: Array<string> }) {
  if (!summaryBullets?.length) {
    return null
  }

  return (
    <ul
      className={clsx(
        'pb-6 pl-4 text-[16px] font-semibold',
        styles.articleBulletNews,
      )}
    >
      {summaryBullets?.map((x, idx) => <li key={idx}>{x}</li>)}
    </ul>
  )
}

const SourceBlock: FC<{
  name: string
  description: string
  subtitle: string
}> = ({ name, description, subtitle }) => (
  <div className="flex flex-col items-start border border-gray-300 bg-[#f8f8f8] p-2.5 text-base leading-5">
    <h5>
      <div className="font-bold">{name}</div>
      <div className="font-normal">{subtitle}</div>
    </h5>
    <div
      className="pt-2 text-xs font-normal text-ktc-desc-gray "
      dangerouslySetInnerHTML={{ __html: description }}
    />
  </div>
)

interface AuthorProps {
  authorData: Author
  publishDate: string
  updateDate: string
  supportingAuthors: <AUTHORS>
}

const Tags: FC<{ data: Tag[] }> = ({ data }) => {
  return (
    <div className="rounded-xl border border-ktc-borders p-5">
      <h3 className="font-mulish pb-2 leading-5">
        <span>Tags:</span>
      </h3>
      <div className="flex flex-wrap gap-2">
        {data?.map((t, idx) => (
          <TagLink key={idx} href={t.urlAlias} name={t.name} />
        ))}
      </div>
    </div>
  )
}

const Disclaimer = () => {
  return (
    <div className="mb-10 text-[10px] text-xs leading-[14px] text-ktc-gray">
      <span className="font-bold text-ktc-desc-gray">Disclaimer:&nbsp;</span>
      The views expressed in this article are those of the author and may not
      reflect those of Kitco Metals Inc. The author has made every effort to
      ensure accuracy of information provided; however, neither Kitco Metals
      Inc. nor the author can guarantee such accuracy. This article is strictly
      for informational purposes only. It is not a solicitation to make any
      exchange in commodities, securities or other financial instruments. Kitco
      Metals Inc. and the author of this article do not accept culpability for
      losses and/ or damages arising from the use of this publication.
    </div>
  )
}

const RelatedArticles: FC<{
  currentNodeCategory: string
  currentNodeId: number
}> = ({ currentNodeCategory, currentNodeId }) => {
  const { data } = kitcoQuery(
    news.newsCommentaries({
      variables: { limit: 4, offset: 0 },
      options: {
        enabled: true,
        select: (d: NewsGenericCommentariesQuery) => {
          const filterItems = d?.commentaries?.items
            // filter current node
            ?.filter(
              (x: ArticleTeaserFragmentFragment) => x.id !== currentNodeId,
            )
            .slice(0, 3)
          const preserve = { ...d?.commentaries, items: filterItems }

          return { ...d, nodeListByCategory: preserve }
        },
      },
    }),
  )

  function filterCurrentFromData() {
    return data?.commentaries?.items
      ?.filter((x: ArticleTeaserFragmentFragment) => x.id !== currentNodeId)
      .slice(0, 3)
  }

  const { isMobile } = useScreenSize()
  const articles = filterCurrentFromData()

  const ShowTeaserCard = () => {
    return (
      <>
        {articles?.map((article: ArticleTeaserFragmentFragment) => {
          if (article.category?.urlAlias.includes('/opinion/')) {
            return !isMobile ? (
              <TeaserCard
                key={article.id}
                node={{
                  ...article,
                  category: {
                    ...article.category,
                    urlAlias: article.category.urlAlias.replace(
                      '/opinion/',
                      '/opinion/',
                    ),
                  },
                }}
                size="sm"
              />
            ) : (
              <TeaserCardMobile
                key={article.id}
                node={{
                  ...article,
                  category: {
                    ...article.category,
                    urlAlias: article.category.urlAlias.replace(
                      '/opinion/',
                      '/opinion/',
                    ),
                  },
                }}
                size="sm"
              />
            )
          }

          if (!isMobile) {
            return <TeaserCard key={article.id} node={article} size="sm" />
          }
          return <TeaserCardMobile key={article.id} node={article} size="sm" />
        })}
      </>
    )
  }

  return (
    <aside>
      <h3 className="font-mulish mb-5 border-b border-ktc-borders pb-2.5 text-2xl">
        Related Articles
      </h3>
      {!articles?.length ? (
        <NoRelatedArticlesShowLatestOpinions
          currentNodeId={currentNodeId}
          currentNodeCategory={currentNodeCategory}
          classTeaserCard="grid grid-rows-3 gap-10 lg:grid-cols-3 lg:grid-rows-1"
          isMobile={isMobile}
        />
      ) : (
        <div
          className={clsx(
            'grid grid-rows-3 gap-10 lg:grid-cols-3 lg:grid-rows-1',
          )}
        >
          <ShowTeaserCard />
        </div>
      )}
    </aside>
  )
}

function NoRelatedArticlesShowLatestOpinions(props: {
  currentNodeId: number
  classTeaserCard: string
  currentNodeCategory: string
  isMobile: boolean
}) {
  // NOTE: trending is not a query, it's preserved state from the ssr query
  const { data } = kitcoQuery(
    opinions.opinionsByCategoryGeneric({
      variables: {
        limit: 5,
        offset: 0,
        urlAlias: props.currentNodeCategory,
      },
      options: {
        enabled: true,
        select: (d: OpinionsByCategoryGenericQuery) => {
          const filterItems = d?.nodeListByCategory?.items
            ?.filter(
              (x: ArticleTeaserFragmentFragment) =>
                x.id !== props.currentNodeId,
            )
            .slice(0, 3)

          // this mostly just preserves the return type of the query
          const preserveDataModel = {
            ...d?.nodeListByCategory,
            items: filterItems,
          }

          return { ...d, nodeListQueue: preserveDataModel }
        },
      },
    }),
  )

  return (
    <div className={props.classTeaserCard}>
      {data?.nodeListByCategory?.items?.map(
        (article: ArticleTeaserFragmentFragment) => {
          if (!props.isMobile) {
            return <TeaserCard key={article.id} node={article} size="sm" />
          }
          return <TeaserCardMobile key={article.id} node={article} size="sm" />
        },
      )}
    </div>
  )
}

function BodyBlock({
  body,
  exitsPresentationImage,
}: {
  body: Commentary['bodyWithEmbeddedMedia']
  exitsPresentationImage?: boolean
}): ReactNode {
  const [scriptData, setScriptData] = useState<HTMLScriptElement[]>([])
  const { isTablet, isDesktop } = useScreenSize()

  useVideoPlayer({
    multiple: body?.embeddedMedia?.map((x) => ({
      assetUuid: x?.assetUuid,
      startTime: x?.startTime,
      endTime: x?.endTime,
      snippetUuid: x?.snippetUuid,
      thumbnailUuid: x?.thumbnailUuid,
      assetType: 'video',
      manuallyCreateParentNodes: true,
    })),
  })

  useEffect(() => {
    const parent = document.querySelectorAll('#articleBody p')

    const elementHasImg = Array.from(parent).filter((item) => {
      const img = item.querySelectorAll('img')

      if (img.length === 0) return false

      return item
    })

    for (const item of elementHasImg) {
      item.outerHTML = item.outerHTML.replaceAll('\n', '')
    }
  }, [body])

  //  Sometimes users add script tags in the WYSIWYG editor, for example Twitter Embeds.
  //  ReactHtmlParser will not parse script tags, so we must do that separately.
  //  Traditionally, this was done creating and injecting <script> tags, however this
  //  caused an issue where the script would run before the html was available, even when
  //  respecting the original scripts async and defer properties.  By using nextJS's <Script />
  //  component, we are able to respect React LifeCycles which solves this issue.
  useEffect(() => {
    if (typeof window === 'undefined') return
    //create an element and add the bodyHTML so that we can parse out script tags
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = body?.value
    const scripts = tempDiv.querySelectorAll(
      'script',
    ) as unknown as HTMLScriptElement[]

    if (scripts) {
      setScriptData([...scripts])
    }
  }, [body])

  useEffect(() => {
    if (typeof window === 'undefined' || (!isTablet && !isDesktop)) return

    // // select all images in the article body with attribute ckeditor-kitco-image
    const images = document.querySelectorAll('img[ckeditor-kitco-image]') // img[ckeditor-kitco-image]
    for (const image of images) {
      const uuid = image.getAttribute('data-uuid')
      const className = `zoom-element-${uuid}`
      image.outerHTML = `<div class='${className}'>`
      const root = createRoot(document.getElementsByClassName(className)[0])
      root.render(
        <Zoom key={uuid} classDialog="custom-zoom" zoomMargin={50}>
          <img
            id={image.id}
            src={`https://storage.googleapis.com/kitco-image-prod/${uuid}`}
            alt={'teaser image'}
            className={clsx('rounded-lg', 'preview-image mb-2.5 block w-full')}
          />
        </Zoom>,
      )
    }
  }, [body, isTablet, isDesktop])

  return (
    <>
      <div
        className={cs([
          'relative text-base',
          styles.articleBodyStyles,
          styles.articleWrapper,
          !exitsPresentationImage && styles.exitsPresentationImage,
        ])}
        id="articleBody"
        dangerouslySetInnerHTML={{ __html: body?.value }}
      />
      {scriptData?.map((element, index) => {
        //  iterate over each script tag which was found, and create a nextJs <Script/> component instead
        //  note that "lazyOnLoad' strategy works well for Twitter embeds, but if this causes issues with
        //  other use cases, perhaps we should use "afterInteractive" (the default) if the original script
        //  tag has the "defer" property set
        return (
          <Script
            key={`script-${index}`}
            id={element?.id}
            src={element?.src}
            strategy={'lazyOnload'}
          />
        )
      })}
    </>
  )
}

const TrendingNowSection: FC = () => {
  const { data } = kitcoQuery(
    news.newsTrending({
      variables: { limit: 10 },
    }),
  )
  return (
    <div className="flex flex-col">
      <h3 className="border-b border-ktc-borders pb-2.5 text-[20px] leading-[26px]">
        <span>Trending News</span>
      </h3>
      <div className="flex flex-grow flex-col justify-between">
        {data?.nodeListTrending
          ?.slice(0, 5)
          .map((x: ArticleTeaserFragmentFragment) => {
            return (
              <div className="mt-5 flex" key={x?.id}>
                <TeaserTextOnly
                  key={x?.id}
                  node={x}
                  hideSummary={true}
                  size={'sm'}
                />
              </div>
            )
          })}
      </div>
    </div>
  )
}

const AudioPlayer: FC<{
  opinionData: OpinionProps['opinionData']
  isMobile?: boolean
}> = ({ opinionData, isMobile = false }) => {
  if (!opinionData?.audioTts?.assetUuid) return

  if (isMobile) {
    return (
      <div className="w-1/2">
        <ArticleAudioPlayer
          assetSnippetUuid={opinionData?.audioTts?.assetUuid}
        />
      </div>
    )
  }

  return (
    <>
      <Spacer className="h-[30px]" />
      <ArticleAudioPlayer assetSnippetUuid={opinionData?.audioTts?.assetUuid} />
    </>
  )
}

export default OpinionRouterPage

const OpinionsSubcategory: FC<any> = ({ urlAlias }) => {
  const { isMobile } = useScreenSize()
  const { params, incrementParams } = useParams(10)
  const { data } = kitcoQuery(
    opinions.opinionsByCategoryGeneric({
      variables: { ...params, urlAlias: urlAlias },
      options: { enabled: true },
    }),
  )

  const { ref, items, loading } = useInfinite({
    items: data?.nodeListByCategory?.items,
    incrementParams,
    total: data?.nodeListByCategory?.total,
  })

  return (
    <LayoutNewsLanding title="Opinion">
      <div className="mx-auto box-border w-full max-w-full px-[20px] md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <NewsCategoryTitle />
        <FirstSection isMobile={isMobile} data={items?.slice(0, 4)} />
        <GenericNewsListWithAuthor data={items?.slice(4)} />
        <div ref={ref}>{loading && <div>Loading...</div>}</div>
      </div>
    </LayoutNewsLanding>
  )
}

type Data = OpinionsByCategoryGenericQuery['nodeListByCategory']['items']

const FirstSection: FC<{ isMobile: boolean; data: Data }> = ({
  isMobile,
  data,
}) => {
  if (!isMobile) return <FirstSectionDesktop data={data} />

  return (
    <>
      <Spacer className="h-2.5" />
      <FirstSectionMobile data={data} />
    </>
  )
}

const FirstSectionDesktop: FC<{ data: Data }> = ({ data }) => {
  const oneData = data?.[0] as TeasersUnion

  return (
    <div className="flex flex-col border-b border-ktc-borders pb-10 lg:flex-row">
      <div className="w-full border-0 border-ktc-borders md:border-b md:pb-[40px] lg:w-[53.4%] lg:border-0 lg:pb-0 lg:pr-[40px]">
        <div className="mb-2 overflow-hidden">
          <ImageMS
            src={
              oneData?.image?.detail?.default?.srcset ??
              oneData?.legacyThumbnailImageUrl
            }
            hasLegacyThumbnailImageUrl={!!oneData?.legacyThumbnailImageUrl}
            alt={`${oneData?.title} teaser image`}
            priority={true}
            width={1202}
            height={676}
            service="icms"
            className={clsx('aspect-video rounded-lg object-cover', 'w-full')}
          />
        </div>
        <TeaserTextOnlyWithAuthor
          node={oneData}
          size="xl"
          hideCategory={true}
          hideSummary={false}
        />
      </div>
      <div className="mt-10 flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-[calc(100%_-_53.4%_+_40px)] lg:border-l lg:pl-[40px]">
        <div className="flex flex-col gap-10">
          {data
            ?.slice(1, data?.length)
            .map((node: TeasersUnion, idx: number) => (
              <TeaserTextOnlyWithAuthor
                node={node}
                size="md"
                hideCategory={true}
                hideSummary={false}
                key={node.id ?? idx}
              />
            ))}
        </div>
      </div>
    </div>
  )
}

const FirstSectionMobile: FC<{ data: Data }> = ({ data }) => {
  return (
    <div className="flex flex-col border-t border-t-ktc-borders pt-5">
      <div className="flex w-full flex-col border-l-ktc-borders pl-0 lg:mt-0 lg:w-1/2 lg:border-l lg:pl-[40px]">
        <div className="block">
          {data
            ?.slice(0, data?.length)
            .map((node: TeasersUnion, idx: number) => (
              <div
                className="mb-5 flex gap-5 border-b border-b-ktc-borders pb-5"
                key={node.id ?? idx}
              >
                <div className="w-[120px] flex-initial">
                  <ImageMS
                    src={
                      (node as any)?.image?.detail?.default?.srcset ??
                      (node as any)?.legacyThumbnailImageUrl
                    }
                    hasLegacyThumbnailImageUrl={
                      !!(node as any)?.legacyThumbnailImageUrl
                    }
                    alt={`${(node as any)?.title} teaser image`}
                    priority={true}
                    width={304}
                    height={170}
                    service="icms"
                    className={clsx('relative aspect-video w-full rounded-lg')}
                  />
                </div>
                <div className="w-[calc(100%_-_140px)] flex-initial">
                  <TeaserTextOnlyWithAuthor
                    node={node}
                    size="sm"
                    hideCategory={true}
                    hideSummary={true}
                  />
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}
