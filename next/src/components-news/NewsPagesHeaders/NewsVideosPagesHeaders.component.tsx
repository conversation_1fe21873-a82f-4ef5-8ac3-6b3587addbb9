import { <PERSON>u, <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuItem, MenuItems } from '@headlessui/react'
import clsx from 'clsx'
import Link from 'next/link'
import { Fragment, useRef } from 'react'
import { BiChevronDown } from 'react-icons/bi'
import { Query } from '~/src/components/Query/Query'
import { vcms } from '~/src/lib/vcms-factory.lib'

const categories = vcms.categories({
  options: {
    enabled: true,
  },
})

export function NewsVideosPagesHeader() {
  return (
    <div
      className={clsx(
        'max-w-full',
        'flex items-center justify-between',
        'lg:block',
        'mt-[10px] pb-[0.625rem]',
      )}
    >
      <h1 className="text-[24px] uppercase leading-[29px] lg:mb-5 lg:h-[35px] lg:text-[48px] lg:leading-[40px]">
        Video News
      </h1>
      <Query fetcher={categories}>
        {(res) => (
          <div className="hidden lg:block">
            {res.isLoading || res.isFetching ? (
              <VideoPlaylistsMenuDesktopLoading />
            ) : (
              <VideoPlaylistsMenuDesktop />
            )}
          </div>
        )}
      </Query>
      <div className="block lg:hidden">
        <VideoPlaylistsMenu buttonLabel="Shows" buttonClassName="pt-[5px]" />
      </div>
    </div>
  )
}

function VideoPlaylistsMenuDesktop() {
  return (
    <Query fetcher={categories}>
      {(res) => (
        <div className="relative flex h-[16px] items-center">
          <ul className="flex items-center overflow-hidden">
            {res?.data?.VideoConsumerCategories?.map((x, idx) => (
              <Fragment key={x.id}>
                <li className="flex items-center">
                  <Link
                    href={`/news/video${x?.urlAlias}` || '/notfound'}
                    className="whitespace-nowrap text-base text-white"
                    key={x.id}
                  >
                    <span>{x.name}</span>
                  </Link>
                  {idx !== res?.data?.VideoConsumerCategories?.length - 1 ? (
                    <div className="mx-4 h-1 w-1 rounded-full bg-white" />
                  ) : null}
                </li>
              </Fragment>
            ))}
          </ul>
          <div className="relative w-full lg:w-auto">
            <VideoPlaylistsMenu buttonLabel="MORE" />
          </div>
        </div>
      )}
    </Query>
  )
}

function VideoPlaylistsMenuDesktopLoading() {
  return (
    <div className="relative flex items-center">
      <ul className="flex items-center overflow-hidden">
        {Array.from(Array(6).keys()).map((_, idx: number) => (
          <Fragment key={idx}>
            <li className="flex items-center">
              <span className="animate-loading mx-4 h-6 w-20 rounded-md" />
              {idx !== 6 ? (
                <div className="mx-4 h-1 w-1 rounded-full bg-white" />
              ) : null}
            </li>
          </Fragment>
        ))}
      </ul>
    </div>
  )
}

function VideoPlaylistsMenu({
  buttonLabel,
  buttonClassName,
}: {
  buttonLabel: string
  buttonClassName?: string
}) {
  const buttonRef = useRef(null)
  const dropdownRef = useRef(null)
  const timeoutDuration = 200
  let timeout: any

  const openMenu = () => buttonRef?.current?.click()

  const closeMenu = () =>
    dropdownRef?.current?.dispatchEvent(
      new KeyboardEvent('keydown', {
        key: 'Escape',
        bubbles: true,
        cancelable: true,
      }),
    )

  const onMouseEnter = (closed: boolean) => {
    clearTimeout(timeout)
    closed && openMenu()
  }
  const onMouseLeave = (open: boolean) => {
    const timeout = setTimeout(() => closeMenu(), timeoutDuration)
    return open && timeout
  }

  return (
    <Query fetcher={categories}>
      {(res) => (
        <Menu>
          {({ open }) => (
            <>
              <div
                onClick={openMenu}
                onKeyDown={openMenu}
                onFocus={() => onMouseEnter(!open)}
                onMouseEnter={() => onMouseEnter(!open)}
                onBlur={() => onMouseLeave(open)}
                onMouseLeave={() => onMouseLeave(open)}
              >
                <MenuButton
                  ref={buttonRef}
                  className={clsx(
                    'flex items-center pl-4 font-semibold',
                    buttonClassName,
                  )}
                  // onMouseEnter={() => setShowMoreMenu(true)}
                  // onMouseLeave={() => setShowMoreMenu(false)}
                >
                  <span>{buttonLabel}</span>
                  <BiChevronDown />
                </MenuButton>
              </div>
              {open && (
                <MenuItems
                  static
                  ref={dropdownRef}
                  onMouseEnter={() => onMouseEnter(!open)}
                  onMouseLeave={() => onMouseLeave(open)}
                  className={clsx(
                    'absolute z-30 bg-gray-100 p-4',
                    'left-0 w-full', //mobile styles
                    'lg:left-auto lg:right-0 lg:w-auto', // tablet and desktop
                  )}
                >
                  {res?.data?.VideoConsumerCategories?.map((x) => (
                    <MenuItem key={x.id} as={Fragment}>
                      <div className="my-1 py-1">
                        <Link
                          className="whitespace-nowrap font-semibold text-black"
                          href={`/news/video${x?.urlAlias}` || '/notfound'}
                        >
                          {x.name}
                        </Link>
                      </div>
                    </MenuItem>
                  ))}
                </MenuItems>
              )}
            </>
          )}
        </Menu>
      )}
    </Query>
  )
}

// only to be used /news/video/[date]
// and /news/video/[date]/[alias]
export function NewsVideosPagesCategoryAndDetailHeader({
  routeLabel,
}: {
  routeLabel: string
}) {
  return (
    <div
      className={clsx(
        'max-w-full',
        'flex items-center justify-between',
        'lg:block',
        'mt-[10px] pb-[0.625rem]',
      )}
    >
      <h1 className="flex items-center gap-2 text-[24px] uppercase lg:gap-3 lg:text-[48px]">
        <Link
          href="/news/video"
          className="h-[35px] leading-[29px] text-white/70 lg:leading-[40px]"
          style={{ fontFamily: 'Bebas Neue' }}
        >
          Video News
        </Link>
        <span
          className="h-[35px] leading-[29px] opacity-60 lg:leading-[40px]"
          style={{ fontFamily: 'Bebas Neue' }}
        >
          /
        </span>
        {!routeLabel ? (
          <span className="w-26 animate-loading block h-8 h-[35px] rounded-md leading-[29px] lg:leading-[40px]" />
        ) : (
          <span
            style={{ fontFamily: 'Bebas Neue' }}
            className="h-[35px] leading-[29px] lg:leading-[40px]"
          >
            {routeLabel}
          </span>
        )}
      </h1>
    </div>
  )
}
