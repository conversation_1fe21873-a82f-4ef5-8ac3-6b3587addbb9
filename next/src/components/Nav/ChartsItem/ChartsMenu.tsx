import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const charts: SectionItems[] = [
  {
    name: 'Gold',
    href: '/charts/gold',
    as: '/charts/gold',
  },
  {
    name: 'Silver',
    href: '/charts/[commodity]',
    as: '/charts/silver',
  },
  {
    name: 'Platinum',
    href: '/charts/[commodity]',
    as: '/charts/platinum',
  },
  {
    name: 'Palladium',
    href: '/charts/[commodity]',
    as: '/charts/palladium',
  },
]

export const cryptos: SectionItems[] = [
  {
    name: 'Bitcoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/bitcoin',
  },
  {
    name: 'Ethereum',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ethereum',
  },
  {
    name: '<PERSON><PERSON>oin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/litecoin',
  },
  {
    name: 'Mon<PERSON>',
    href: '/price/crypto/[name]',
    as: '/price/crypto/monero',
  },
  {
    name: 'Ripple',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ripple',
  },
  {
    name: 'Dash',
    href: '/price/crypto/[name]',
    as: '/price/crypto/dash',
  },
]

export const forYou: SectionItems[] = []

const ChartsMenu = () => {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList title="Cryptos" items={cryptos} />
      </Navigation.SubMenuColumn>
      {/*
      <Navigation.SubMenuColumn>
        <SectionList title="For you" items={forYou} />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList title="Extras" items={currencies} />
      </Navigation.SubMenuColumn>
      */}
    </Navigation.SubMenuGrid>
  )
}

export default ChartsMenu
