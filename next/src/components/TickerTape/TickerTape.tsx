import type { FC } from 'react'
import useSWR from 'swr'

const fetcher = (url: string) => fetch(url).then((r) => r.json())

// TODO: PLEASE FINISH this is very half baked at the moment
const useData = () => {
  const { data, error } = useSWR(
    '/api/getQuote/$DOWI,$NASX,$SPX,$NYA,$DXY,^USDAUD,^USDCAD,^USDCNY,^USDEUR,^USDGBP,^USDJPY,GCZ20,SIZ20,HGZ20,GCG21,PLF21,HGH21',
    fetcher,
  )

  if (error) {
    console.error('Error fetching data:', error)
    return []
  }

  if (!data) {
    return []
  }

  return data.results.map((x: any) => ({
    ...x,
    symbol: x.symbol.split(''),
  }))
}

const TickerTape: FC = () => {
  const data = useData()

  return (
    <div className="bg-[#232323] py-4">
      <h1>Ticker Tape</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  )
}

export default TickerTape
