import Image from 'next/image'
import Link from 'next/link'

const DisclaimerFooter = () => {
  return (
    <>
      <Image
        src="/logos/cme_group.svg"
        alt="Kitco Gold Index"
        width={108}
        height={25}
        className="mb-4"
      />
      <div className="flex flex-col items-start justify-end gap-1 self-stretch">
        <div className="self-stretch">
          <span className="font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
            All{' '}
          </span>
          <Link
            href="https://www.barchart.com/solutions/data/market"
            target="_blank"
            rel="noopener noreferrer"
            className="font-['Mulish'] text-xs font-normal leading-none text-sky-700 underline"
          >
            market data
          </Link>
          <span className="font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
            {' '}
            is provided by Barchart Solutions.
          </span>
        </div>
        <div className="self-stretch font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
          Futures: at least a 10 minute delay. Information is provided 'as is'
          and solely for informational purposes, not for trading purposes or
          advice.
        </div>
      </div>
      <div className="flex flex-col items-start justify-end gap-1 self-stretch">
        <div className="self-stretch">
          <span className="font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
            To see all exchange delays and terms of use, please{' '}
          </span>
          <Link
            href="https://www.barchart.com/solutions/terms"
            target="_blank"
            rel="noopener noreferrer"
            className="font-['Mulish'] text-xs font-normal leading-none text-sky-700 underline"
          >
            see disclaimer
          </Link>
        </div>
        <div className="self-stretch font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
          Information is provided 'as is' and solely for informational purposes,
          not for trading purposes or advice…
        </div>
      </div>
    </>
  )
}

export default DisclaimerFooter
