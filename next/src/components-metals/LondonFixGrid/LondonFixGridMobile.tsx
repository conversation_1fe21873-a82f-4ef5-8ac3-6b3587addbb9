// src/components-metals/LondonFixGridMobile/LondonFixGridMobile.tsx
'use client'

import Image from 'next/image'
import type { FC } from 'react'
import { useState } from 'react'
import DatePicker from '~/src/components/DatePicker/DatePicker'
import type { LondonFixQuery } from '~/src/generated'
import { useMorningFix } from '~/src/hooks/MorningFix/useMorningFix'
import { useTrueTime } from '~/src/hooks/useTrueTime'
import { useHubTimeInfo } from '~/src/hooks/MorningFix/useHubTimeInfo'
import {
  getCountdown,
  isPriceAvailable,
} from '~/src/utils/MorningFix/morningFixAvailability'
import { getMorningFixDefaultDate } from '~/src/utils/MorningFix/morningFixDate'
import {
  CURRENCY_OPTIONS,
  UOM_OPTIONS,
  convertPrice,
  formatPrice,
  type Currency,
  type UnitOfMeasure,
} from '~/src/utils/morningFixConversions'
import InfoModal from './InfoModal'
import styles from './LondonFixGridMobile.module.scss'

interface MobileGridProps {
  data?: LondonFixQuery
  variant?: 'landing' | 'precious-metals'
  initialAbbrMap?: Record<string,string>
}

const LondonFixGridMobile: FC<MobileGridProps> = ({
                                                    data: _,
                                                    variant = 'precious-metals',
                                                    initialAbbrMap = {},
                                                  }) => {
  const [date, setDate] = useState(getMorningFixDefaultDate())
  const [currency, setCurrency] = useState<Currency>('USD')
  const [unit, setUnit] = useState<UnitOfMeasure>('OZ')
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false)
  const [isToolDrawerOpen, setIsToolDrawerOpen] = useState(false)

  const now = useTrueTime()
  const abbrMap = useHubTimeInfo(initialAbbrMap)
  const { hubs, loading, error } = useMorningFix(
    date,
    currency as 'USD' | 'EUR',
    1
  )

  const sortedHubs = hubs.slice().sort((a, b) => {
    const order = ['hk', 'mumbai', 'london', 'ny']
    return order.indexOf(a.key) - order.indexOf(b.key)
  })

  const wrapperClass =
    variant === 'landing' ? styles.wrapperMobileLanding : styles.wrapperMobile

  return (
    <>
      <div className={wrapperClass}>
        <div className={styles.headerMobile}>
          <Image
            src="/icons/kitco_morning_fix_icons/05InfoIconDark.png"
            alt="Info"
            width={21}
            height={21}
            className={`${styles.headerMobileIcon} cursor-pointer hover:opacity-80`}
            onClick={() => setIsInfoModalOpen(true)}
            title="Info"
          />
          <h2 className={styles.headerMobileTitle}>KITCO Morning Fix</h2>
          <div className={styles.headerMobileIconContainer}>
            <Image
              src="/icons/kitco_morning_fix_icons/10ToolIconDark.png"
              alt="Tool"
              width={21}
              height={21}
              className={`${styles.headerMobileIcon} cursor-pointer hover:opacity-80`}
              onClick={() => setIsToolDrawerOpen(!isToolDrawerOpen)}
              title="Tool"
            />
            {isToolDrawerOpen && (
              <div className={styles.toolDrawer}>
                <div className={styles.toolDrawerTitle}>
                  Morning Fix Options
                </div>
                <div className={styles.toolDrawerIcons}>
                  <Image
                    src="/icons/kitco_morning_fix_icons/06CalendarIconDark.png"
                    alt="Calendar"
                    width={21}
                    height={21}
                    className={styles.toolDrawerIcon}
                    title="Calendar"
                  />
                  <div className={styles.toolDrawerSeparator} />
                  <Image
                    src="/icons/kitco_morning_fix_icons/07WidgetIconDark.png"
                    alt="Widget"
                    width={23}
                    height={23}
                    className={styles.toolDrawerIconLarge}
                    title="Get Widget"
                  />
                  <div className={styles.toolDrawerSeparator} />
                  <Image
                    src="/icons/kitco_morning_fix_icons/08FormulaIconDark.png"
                    alt="Formula"
                    width={21}
                    height={21}
                    className={styles.toolDrawerIcon}
                    title="Formula"
                  />
                  <div className={styles.toolDrawerSeparator} />
                  <Image
                    src="/icons/kitco_morning_fix_icons/09SubscribeIconDark.png"
                    alt="Subscribe"
                    width={21}
                    height={21}
                    className={styles.toolDrawerIcon}
                    title="Subscribe"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className={styles.controlsMobile}>
          <div className={styles.leftColumnMobile}>
            <div className={styles.topRowMobile}>
              <DatePicker
                value={date}
                onChange={setDate}
                className={styles.mobileDatePicker}
              />
            </div>
            <div className={styles.bottomRowMobile}>
              <select
                className={styles.mobileCurrencySelect}
                value={currency}
                onChange={(e) => setCurrency(e.target.value as Currency)}
              >
                {CURRENCY_OPTIONS.map((curr) => (
                  <option key={curr} value={curr}>
                    {curr}
                  </option>
                ))}
              </select>
              <select
                className={styles.mobileUomSelect}
                value={unit}
                onChange={(e) => setUnit(e.target.value as UnitOfMeasure)}
              >
                {UOM_OPTIONS.map((uom) => (
                  <option key={uom} value={uom}>
                    {uom}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className={styles.rightColumnMobile}>
            <div className={styles.mobileInfoLabel}>
              Prices posted around <strong>10:30 AM</strong> local time in each hub
            </div>
          </div>
        </div>

        <div className={styles.cardsMobile}>
          {loading && <div className="p-4 text-center">Loading...</div>}
          {error && <div className="p-4 text-center text-red-500">Error: {error}</div>}

          {!error &&
            sortedHubs.map((hub) => {
              const available = isPriceAvailable(hub.key, date, now)
              const countdown = getCountdown(hub.key, date, now)

              return (
                <div key={hub.key} className={styles.cardMobile}>
                  <div className={styles.cardHeader}>
                    <div className="w-10 h-6 flex items-center justify-center mr-3">
                      {hub.key === 'hk' && (
                        <Image
                          src="/icons/kitco_morning_fix_icons/01FlagHonkKong.png"
                          alt="Hong Kong Flag"
                          width={40}
                          height={24}
                        />
                      )}
                      {hub.key === 'mumbai' && (
                        <Image
                          src="/icons/kitco_morning_fix_icons/02FlagIndia.png"
                          alt="India Flag"
                          width={40}
                          height={24}
                        />
                      )}
                      {hub.key === 'london' && (
                        <Image
                          src="/icons/kitco_morning_fix_icons/03FlagUK.png"
                          alt="UK Flag"
                          width={40}
                          height={24}
                        />
                      )}
                      {hub.key === 'ny' && (
                        <Image
                          src="/icons/kitco_morning_fix_icons/04FlagUSA.png"
                          alt="USA Flag"
                          width={40}
                          height={24}
                        />
                      )}
                    </div>
                    <div className={styles.cardTitle}>
                      <div className={styles.cityTimeContainer}>
                        <div className="text-lg font-semibold">{hub.label}</div>
                        {available ? (
                          <div className={styles.timestamp}>
                            {hub.timestamp}
                            {abbrMap[hub.key] ? ` ${abbrMap[hub.key]}` : ''}
                          </div>
                        ) : (
                          <div className={styles.countdown}>
                            {`Coming in ${countdown.hours}h${countdown.minutes} min`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <ul
                    className={`${styles.commodityList} ${
                      !available ? styles.disabledOverlay : ''
                    }`}
                  >
                    {(['gold', 'silver', 'platinum', 'palladium'] as const).map(
                      (m) => (
                        <li key={m} className={styles.commodityItem}>
                          <span className={styles.commodityLabel}>{m}</span>
                          <span
                            className={
                              available
                                ? styles.commodityValue
                                : `${styles.commodityValue} ${styles.blueText}`
                            }
                          >
                            {available && Number.isFinite(hub.prices[m])
                              ? formatPrice(
                                convertPrice(hub.prices[m], unit),
                                unit
                              )
                              : '-'}
                          </span>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )
            })}
        </div>
      </div>

      <InfoModal
        isOpen={isInfoModalOpen}
        onClose={() => setIsInfoModalOpen(false)}
      />
    </>
  )
}

export default LondonFixGridMobile
