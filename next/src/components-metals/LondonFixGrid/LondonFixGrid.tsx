// src/components-metals/LondonFixGrid/LondonFixGrid.tsx
'use client'

import Image from 'next/image'
import type { FC } from 'react'
import { useState } from 'react'
import DatePicker from '~/src/components/DatePicker/DatePicker'
import type { LondonFixQuery } from '~/src/generated'
import { useMorningFix } from '~/src/hooks/MorningFix/useMorningFix'
import { useTrueTime } from '~/src/hooks/useTrueTime'
import { useHubTimeInfo } from '~/src/hooks/MorningFix/useHubTimeInfo'
import {
  getCountdown,
  isPriceAvailable,
} from '~/src/utils/MorningFix/morningFixAvailability'
import { getMorningFixDefaultDate } from '~/src/utils/MorningFix/morningFixDate'
import {
  CURRENCY_OPTIONS,
  UOM_OPTIONS,
  convertPrice,
  formatPrice,
  type Currency,
  type UnitOfMeasure,
} from '~/src/utils/morningFixConversions'
import InfoModal from './InfoModal'
import styles from './LondonFixGrid.module.scss'

interface MorningFixGridProps {
  data?: LondonFixQuery
  initialAbbrMap?: Record<string,string>
}

const MorningFixGrid: FC<MorningFixGridProps> = ({
                                                   data: _,
                                                   initialAbbrMap = {},
                                                 }) => {
  const [date, setDate] = useState(getMorningFixDefaultDate())
  const [currency, setCurrency] = useState<Currency>('USD')
  const [unit, setUnit] = useState<UnitOfMeasure>('OZ')
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false)

  const now = useTrueTime()
  const abbrMap = useHubTimeInfo(initialAbbrMap)
  const { hubs } = useMorningFix(date, currency as 'USD' | 'EUR', 1)

  const sortedHubs = hubs.slice().sort((a, b) => {
    const order = ['hk', 'mumbai', 'london', 'ny']
    return order.indexOf(a.key) - order.indexOf(b.key)
  })

  return (
    <>
      <div className={styles.wrapper}>
        <div className={styles.header}>
          <Image
            src="/icons/kitco_morning_fix_icons/05InfoIconDark.png"
            alt="Info"
            width={21}
            height={21}
            className={`${styles.headerIcon} cursor-pointer hover:opacity-80`}
            onClick={() => setIsInfoModalOpen(true)}
            title="Info"
          />
          <h2 className={styles.headerTitle}>KITCO Morning Fix</h2>
          <div className="flex items-center space-x-2">
            <Image
              src="/icons/kitco_morning_fix_icons/06CalendarIconDark.png"
              alt="Calendar"
              width={21}
              height={21}
              className={`${styles.headerIcon} cursor-pointer hover:opacity-80`}
              title="Calendar"
            />
            <div className="h-4 border-l border-gray-300" />
            <Image
              src="/icons/kitco_morning_fix_icons/07WidgetIconDark.png"
              alt="Widget"
              width={23}
              height={23}
              className={`${styles.widgetIcon} cursor-pointer hover:opacity-80`}
              title="Get Widget"
            />
            <div className="h-4 border-l border-gray-300" />
            <Image
              src="/icons/kitco_morning_fix_icons/08FormulaIconDark.png"
              alt="Formula"
              width={21}
              height={21}
              className={`${styles.headerIcon} cursor-pointer hover:opacity-80`}
              title="Formula"
            />
            <div className="h-4 border-l border-gray-300" />
            <Image
              src="/icons/kitco_morning_fix_icons/09SubscribeIconDark.png"
              alt="Subscribe"
              width={21}
              height={21}
              className={`${styles.headerIcon} cursor-pointer hover:opacity-80`}
              title="Subscribe"
            />
          </div>
        </div>

        <div className={styles.controls}>
          <DatePicker
            value={date}
            onChange={setDate}
            className={styles.datePicker}
          />
          <div className={styles.infoLabel}>
            Prices posted around <strong>10:30 AM</strong> local time in each hub
          </div>
          <div className={styles.controlsRight}>
            <select
              className={styles.currencySelect}
              value={currency}
              onChange={(e) => setCurrency(e.target.value as Currency)}
            >
              {CURRENCY_OPTIONS.map((curr) => (
                <option key={curr} value={curr}>
                  {curr}
                </option>
              ))}
            </select>
            <select
              className={styles.uomSelect}
              value={unit}
              onChange={(e) => setUnit(e.target.value as UnitOfMeasure)}
            >
              {UOM_OPTIONS.map((uom) => (
                <option key={uom} value={uom}>
                  {uom}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className={styles.gridContainer}>
          {sortedHubs.map((hub) => {
            const available = isPriceAvailable(hub.key, date, now)
            const countdown = getCountdown(hub.key, date, now)

            return (
              <div key={hub.key} className={styles.gridify}>
                <div className={styles.cardHeader}>
                  <div className={styles.flagIcon}>
                    {hub.key === 'hk' && (
                      <Image
                        src="/icons/kitco_morning_fix_icons/01FlagHonkKong.png"
                        alt="Hong Kong Flag"
                        width={32}
                        height={22}
                      />
                    )}
                    {hub.key === 'mumbai' && (
                      <Image
                        src="/icons/kitco_morning_fix_icons/02FlagIndia.png"
                        alt="India Flag"
                        width={32}
                        height={22}
                      />
                    )}
                    {hub.key === 'london' && (
                      <Image
                        src="/icons/kitco_morning_fix_icons/03FlagUK.png"
                        alt="UK Flag"
                        width={32}
                        height={22}
                      />
                    )}
                    {hub.key === 'ny' && (
                      <Image
                        src="/icons/kitco_morning_fix_icons/04FlagUSA.png"
                        alt="USA Flag"
                        width={32}
                        height={22}
                      />
                    )}
                  </div>
                  <div className={styles.cityTimeContainer}>
                    <div className={styles.cityLabel}>{hub.label}</div>
                    {available ? (
                      <div className={styles.timestamp}>
                        {hub.timestamp}
                        {abbrMap[hub.key] ? ` ${abbrMap[hub.key]}` : ''}
                      </div>
                    ) : (
                      <div className={styles.countdown}>
                        {`Coming in ${countdown.hours}h${countdown.minutes} min`}
                      </div>
                    )}
                  </div>
                </div>

                <table
                  className={`${styles.dataTable} ${
                    !available ? styles.disabledOverlay : ''
                  }`}
                >
                  <tbody>
                  {(['gold', 'silver', 'platinum', 'palladium'] as const).map(
                    (m) => (
                      <tr key={m}>
                        <td className={styles.labelCell}>{m.toUpperCase()}</td>
                        <td
                          className={`${styles.priceCell} ${
                            !available ? styles.pending : ''
                          }`}
                        >
                          {available && Number.isFinite(hub.prices[m])
                            ? formatPrice(
                              convertPrice(hub.prices[m], unit),
                              unit
                            )
                            : '-'}
                        </td>
                      </tr>
                    )
                  )}
                  </tbody>
                </table>
              </div>
            )
          })}
        </div>
      </div>

      <InfoModal
        isOpen={isInfoModalOpen}
        onClose={() => setIsInfoModalOpen(false)}
      />
    </>
  )
}

export default MorningFixGrid
