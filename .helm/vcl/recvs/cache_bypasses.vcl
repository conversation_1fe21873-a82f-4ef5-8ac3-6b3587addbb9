# Charts Images - Do not cache
if (req.url ~ "^/chart-images/") {
    return(pass);
}

# Static Sitemaps - Serve from Google Cloud
if (req.url ~ "^/static-sitemaps/(.*)") {
    set req.http.host = "{{ .Values.varnish.extra.sitemapBucket }}";
    set req.backend_hint = www_dir.backend("{{ .Values.varnish.extra.sitemapBucket }}", "80");
    set req.url = "/" + regsub(req.url, "^/static-sitemaps/(.*)", "sitemaps/\1");
    return(pass);
}
