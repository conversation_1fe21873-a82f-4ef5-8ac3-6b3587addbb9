export const demandConfig = {
  openx: {
    delDomain: 'zerohedge-d.openx.net',
  },
  criteo: {
    networkId: 9788,
  },
  rubicon: {
    accountId: '20438',
    siteId: '340058',
  },
  pubmatic: {
    publisherId: '158347',
  },
  medianet: {
    cid: '8CUZX691M',
  },
  onetag: {
    pubId: '76b2c6676b744dc',
  },
  minutemedia: {
    org: '01h84awt6qqr',
  },
  brightcom: {
    publisherId: 20931,
  },
  eplanning: {
    ci: '99480',
  },
}

export const sizeDefaults = {
  skyscraper: [[160, 600]],
  rectangle: [[300, 250]],
  rhpsb1: [
    [300, 250],
    [336, 280],
  ],
  rhpsb2: [
    [300, 250],
    [336, 280],
    [300, 600],
  ],
  mediumRectangle: [
    [300, 250],
    [300, 600],
  ],
  leaderboard: [
    [970, 250],
    [728, 90],
    [320, 100],
    [320, 50],
    [300, 50],
  ],
  native: ['fluid', [1, 1]],
  billboard: ['fluid', [1, 1], [300, 250], [300, 600], [728, 90]],
  footer: [
    [728, 90],
    [320, 100],
    [320, 50],
  ],
  banner: [
    [336, 280],
    [300, 250],
    [320, 50],
    [300, 50],
    [320, 100],
    [728, 90],
  ],
  inContent: [
    [300, 250],
    [336, 280],
  ],
  interstitial: [
    [300, 250],
    [320, 480],
    [336, 280],
  ],
  square: [
    [125, 125],
    [180, 150],
  ],
  video: [
    [1, 1],
    [336, 280],
    [300, 250],
  ],
  largeBanner: [
    [336, 280],
    [300, 250],
    [728, 90],
    [970, 250],
  ],
  vidBanner: [[320, 100]],
  rightRailAMQ: ['fluid', [300, 250], [300, 600]],
  pressRelease: ['fluid', [320, 100], [320, 50], [728, 90]],
  pressReleaseMin: ['fluid', [320, 100], [320, 50]],
}
