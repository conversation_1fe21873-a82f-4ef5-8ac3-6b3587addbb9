import dotenv from 'dotenv'
import admin from 'firebase-admin'
import { createRequire } from 'node:module'
import { syncAuth } from './sync-firebase-auth.js'
import { syncRealtimeDatabase } from './sync-firebase-realtime-database.js'
/**
 * This script exports users from Firebase from the production
 * environment and imports them into the development environment.
 *
 * The script is intended to be run in a Node.js environment.
 *
 * Prerequisites:
 * - The Firebase Admin SDK must be installed in the project.
 * - Service account credentials for the production and development environments
 *  must be available in the project.
 *  - The service account credentials must have the necessary permissions to
 *  read and write user data in the Firebase project.
 *  - The script can be run using the following command:
 *  `yarn sync-firebase`
 *
 *
 *  The script performs the following steps:
 *  1. Export users from the production environment.
 *  2. Export users from the development environment.
 *  3. Update and create users in the development environment based on the
 *  users exported from production.
 *  4. Delete users from the development environment that do not exist in
 *  the production environment.
 *  - The script logs the progress and errors to the console.
 *  - The script can be extended to handle additional user properties or
 *  perform other operations as needed.
 *  - The script can be scheduled to run periodically to keep the user data
 *  synchronized between environments.
 */

/**
 * Initialize Firebase apps for production and development
 */

// Base path for environment configuration files and service account JSON files
const envBasePath = '../.circleci/environments'

// Set the debug flag to true to enable logging in the dotenv module
const debug = false

// Load environment variables from production and development .env files
dotenv.config({
  path: `${envBasePath}/prod/.env.production`,
  debug: debug,
  override: true,
})
const prodDatabaseURL = process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL

dotenv.config({
  path: `${envBasePath}/dev/.env.development`,
  debug: debug,
  override: true,
})
const devDatabaseURL = process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL

/**
 * Initialize Firebase apps for production and development
 */

// Base path for environment configuration files and service account JSON files
const basePath = '../../.circleci/environments'

const require = createRequire(import.meta.url)

const serviceAccountProd = require(`${basePath}/prod/firebase-admin.json`)
const prodApp = admin.initializeApp(
  {
    credential: admin.credential.cert(serviceAccountProd),
    databaseURL: prodDatabaseURL,
  },
  'prod',
)

// Initialize the Firebase app for the development environment
const serviceAccountDev = require(`${basePath}/dev/firebase-admin.json`)
const devApp = admin.initializeApp(
  {
    credential: admin.credential.cert(serviceAccountDev),
    databaseURL: devDatabaseURL,
  },
  'dev',
)

console.log('Starting Firebase synchronization...')
console.log('')
console.log(
  'Synchronizing users from Firebase Auth from production to development...',
)
// Synchronize users from Firebase Auth from production to development
await syncAuth(devApp, prodApp)

console.log('')
console.log(
  'Synchronizing data from the Realtime Database from production to development...',
)
// Synchronize data from the Realtime Database from production to development
await syncRealtimeDatabase(devApp, prodApp)

console.log('')
console.log('Firebase synchronization completed.')

// Close Firebase app instances to ensure all connections are closed
await admin.app('prod').delete()
console.log('Closed Firebase production app.')

await admin.app('dev').delete()
console.log('Closed Firebase development app.')

console.log('Exiting process.')
process.exit(0)
