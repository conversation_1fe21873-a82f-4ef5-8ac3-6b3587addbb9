(window.webpackJsonp = window.webpackJsonp || []).push([["chart-event-hint"], {
  "+EG+": function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return i;
    }),
      n.d(t, "b", function() {
        return s;
      });
    var r = n("mrSG"),
      o = n("q1tI"),
      i = function(e) {
        function t() {
          return null !== e && e.apply(this, arguments) || this;
        }
        return Object(r.__extends)(t, e),
          t.prototype.shouldComponentUpdate = function() {
            return !1;
          },
          t.prototype.render = function() {
            return o.createElement("div", {
              style: { position: "fixed", zIndex: 150, left: 0, top: 0 },
              ref: this.props.reference,
            });
          },
          t;
      }(o.Component),
      s = o.createContext(null);
  },
  "79vt": function(e, t, n) {
    e.exports = {
      container: "container-RYiwcUsM",
      content: "content-2QNYOBPt",
      arrowHolder: "arrowHolder-1VdSgMwV",
      "arrowHolder--below": "arrowHolder--below-FwRPJmQk",
      "arrowHolder--above": "arrowHolder--above-3dafVndi",
      "arrowHolder--before": "arrowHolder--before-J9LaRkO3",
      "arrowHolder--after": "arrowHolder--after-3LNL_hrq",
      "arrowHolder--above-fix": "arrowHolder--above-fix-1jpQDk8-",
      "arrowHolder--before-rtl-fix": "arrowHolder--before-rtl-fix-zFykJHSt",
      "arrowHolder--after-ltr-fix": "arrowHolder--after-ltr-fix-1W2mlyUF",
      label: "label-2D_bIpcf",
      closeButton: "closeButton-2425ZrEc",
    };
  },
  AiMB: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return l;
    }),
      n.d(t, "b", function() {
        return d;
      });
    var r = n("mrSG"),
      o = n("q1tI"),
      i = n("i8i4"),
      s = n("e3/o"),
      a = n("jAh7"),
      c = n("+EG+"),
      l = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._uuid = Object(s.guid)(), t;
        }
        return Object(r.__extends)(t, e),
          t.prototype.componentWillUnmount = function() {
            this._manager().removeWindow(this._uuid);
          },
          t.prototype.render = function() {
            var e = this._manager().ensureWindow(this._uuid, this.props.layerOptions);
            return e.style.top = this.props.top || "",
              e.style.bottom = this.props.bottom || "",
              e.style.left = this.props.left || "",
              e.style.right = this.props.right || "",
              e.style.pointerEvents = this.props.pointerEvents || "",
              i.createPortal(o.createElement(d.Provider, { value: this }, this.props.children), e);
          },
          t.prototype.moveToTop = function() {
            this._manager().moveToTop(this._uuid);
          },
          t.prototype._manager = function() {
            return null === this.context ? Object(a.getRootOverlapManager)() : this.context;
          },
          t.contextType = c.b,
          t;
      }(o.PureComponent),
      d = o.createContext(null);
  },
  Iivm: function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", { value: !0 }), n("mrSG").__exportStar(n("swCq"), t);
  },
  PN5r: function(e, t, n) {
    "use strict";
    n.r(t);
    var r = n("mrSG"),
      o = n("q1tI"),
      i = n.n(o),
      s = n("i8i4"),
      a = n("TSYQ"),
      c = n("Iivm"),
      l = n("AiMB"),
      d = n("To8B"),
      u = n("79vt");
    o.PureComponent;
    function p(e) {
      var t = e.className,
        n = e.containerClassName,
        i = e.reference,
        s = e.style,
        l = e.arrow,
        p = void 0 === l || l,
        h = e.arrowClassName,
        f = e.arrowReference,
        m = e.onClose,
        v = e.arrowStyle,
        w = e.children,
        _ = Object(r.__rest)(e, [
          "className",
          "containerClassName",
          "reference",
          "style",
          "arrow",
          "arrowClassName",
          "arrowReference",
          "onClose",
          "arrowStyle",
          "children",
        ]);
      return o.createElement(
        "div",
        Object(r.__assign)({}, _, { className: t, ref: i, style: s }),
        p && o.createElement("div", { className: h, ref: f, style: v }),
        o.createElement(
          "div",
          { className: a(u.container, n) },
          o.createElement("div", { className: u.content }, w),
          m && o.createElement(c.Icon, { className: u.closeButton, icon: d, onClick: m }),
        ),
      );
    }
    var h = n("g2Cz");
    function f(e) {
      var t = e.isOpened, n = e.text, r = e.onClose;
      return t
        ? i.a.createElement(
          "div",
          { className: h.container },
          i.a.createElement(
            "div",
            { className: h.centerElement },
            i.a.createElement(p, { arrow: !1, onClose: r }, i.a.createElement("div", { className: h.text }, n)),
          ),
        )
        : null;
    }
    n.d(t, "ChartEventHintRenderer", function() {
      return m;
    });
    var m = function() {
      function e(e) {
        this._wrap = document.createElement("div"), this._container = e;
      }
      return e.prototype.show = function(e, t) {
        var n = this;
        if (this._wrap) {
          this.hide(), this._container.append(this._wrap);
          var i = {
            isOpened: !0,
            text: e,
            onClose: function() {
              t && t(), n.hide();
            },
          };
          s.render(o.createElement(f, Object(r.__assign)({}, i)), this._wrap);
        }
      },
        e.prototype.hide = function() {
          this._wrap && (s.unmountComponentAtNode(this._wrap), this._wrap.remove());
        },
        e.prototype.destroy = function() {
          this.hide(), delete this._wrap;
        },
        e;
    }();
  },
  To8B: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\"><path fill=\"currentColor\" d=\"M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z\"/></svg>";
  },
  g2Cz: function(e, t, n) {
    e.exports = { container: "container-2yI8A9Vu", centerElement: "centerElement-UEMVlR9F", text: "text-2Q2WewIi" };
  },
  jAh7: function(e, t, n) {
    "use strict";
    n.r(t),
      n.d(t, "OverlapManager", function() {
        return i;
      }),
      n.d(t, "getRootOverlapManager", function() {
        return a;
      });
    var r = n("Eyy1"),
      o = function() {
        function e() {
          this._storage = [];
        }
        return e.prototype.add = function(e) {
          this._storage.push(e);
        },
          e.prototype.remove = function(e) {
            this._storage = this._storage.filter(function(t) {
              return e !== t;
            });
          },
          e.prototype.has = function(e) {
            return this._storage.includes(e);
          },
          e.prototype.getItems = function() {
            return this._storage;
          },
          e;
      }(),
      i = function() {
        function e(e) {
          void 0 === e && (e = document),
            this._storage = new o(),
            this._windows = new Map(),
            this._index = 0,
            this._document = e,
            this._container = e.createDocumentFragment();
        }
        return e.prototype.setContainer = function(e) {
          var t = this._container, n = null === e ? this._document.createDocumentFragment() : e;
          !function(e, t) {
            Array.from(e.childNodes).forEach(function(e) {
              e.nodeType === Node.ELEMENT_NODE && t.appendChild(e);
            });
          }(t, n), this._container = n;
        },
          e.prototype.registerWindow = function(e) {
            this._storage.has(e) || this._storage.add(e);
          },
          e.prototype.ensureWindow = function(e, t) {
            void 0 === t && (t = { position: "fixed", direction: "normal" });
            var n = this._windows.get(e);
            if (void 0 !== n) return n;
            this.registerWindow(e);
            var r = this._document.createElement("div");
            if (
              r.style.position = t.position,
                r.style.zIndex = this._index.toString(),
                r.dataset.id = e,
                void 0 !== t.index
            ) {
              var o = this._container.childNodes.length;
              if (t.index >= o) this._container.appendChild(r);
              else if (t.index <= 0) this._container.insertBefore(r, this._container.firstChild);
              else {
                var i = this._container.childNodes[t.index];
                this._container.insertBefore(r, i);
              }
            } else {"reverse" === t.direction
                ? this._container.insertBefore(r, this._container.firstChild)
                : this._container.appendChild(r);}
            return this._windows.set(e, r), ++this._index, r;
          },
          e.prototype.unregisterWindow = function(e) {
            this._storage.remove(e);
            var t = this._windows.get(e);
            void 0 !== t && (null !== t.parentElement && t.parentElement.removeChild(t), this._windows.delete(e));
          },
          e.prototype.getZindex = function(e) {
            var t = this.ensureWindow(e);
            return parseInt(t.style.zIndex || "0");
          },
          e.prototype.moveToTop = function(e) {
            this.getZindex(e) !== this._index && (this.ensureWindow(e).style.zIndex = (++this._index).toString());
          },
          e.prototype.removeWindow = function(e) {
            this.unregisterWindow(e);
          },
          e;
      }(),
      s = new WeakMap();
    function a(e) {
      void 0 === e && (e = document);
      var t = e.getElementById("overlap-manager-root");
      if (null !== t) return Object(r.ensureDefined)(s.get(t));
      var n = new i(e),
        o = function(e) {
          var t = e.createElement("div");
          return t.style.position = "absolute",
            t.style.zIndex = 150..toString(),
            t.style.top = "0px",
            t.style.left = "0px",
            t.id = "overlap-manager-root",
            t;
        }(e);
      return s.set(o, n), n.setContainer(o), e.body.appendChild(o), n;
    }
  },
  swCq: function(e, t, n) {
    "use strict";
    Object.defineProperty(t, "__esModule", { value: !0 }), t.Icon = void 0;
    var r = n("mrSG"), o = n("q1tI");
    t.Icon = o.forwardRef(function(e, t) {
      var n = e.icon, i = void 0 === n ? "" : n, s = r.__rest(e, ["icon"]);
      return o.createElement("span", r.__assign({}, s, { ref: t, dangerouslySetInnerHTML: { __html: i } }));
    });
  },
}]);
