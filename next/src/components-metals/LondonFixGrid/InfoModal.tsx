// src/components-metals/LondonFixGrid/InfoModal.tsx
'use client'

import { FC } from 'react'
import styles from './InfoModal.module.scss'

interface InfoModalProps {
  isOpen: boolean
  onClose: () => void
}

const InfoModal: FC<InfoModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        {/* Close button */}
        <button className={styles.closeButton} onClick={onClose}>
          ×
        </button>

        {/* Modal header */}
        <h2 className={styles.modalTitle}>About the Kitco Morning Fix</h2>

        {/* Modal body */}
        <div className={styles.modalBody}>
          <p>
            The Kitco Morning Fix publishes daily benchmark prices for gold,
            silver, platinum, and palladium at 10:30 AM local time in four key
            trading hubs — New York, London, Hong Kong, and Mumbai. Determining
            Fixes across these centers generates anchor prices in every major
            market window and keeps North American, European, and Asia-Pacific
            investors aligned.
          </p>

          <p>
            Benchmarks are derived from the live over-the-counter spot market.
            Our transparent algorithm blends competitive bid-ask quotes from
            leading bullion dealers, then filters outliers to deliver a balanced
            snapshot of each metal's fair value.
          </p>

          <p>
            Traders, analysts, and long-term holders can use the Fix to validate
            trades, spot trends, and add context to intraday moves.
          </p>

          <p>
            Questions or feedback? Write to{' '}
            <a
              href="mailto:<EMAIL>"
              className={styles.emailLink}
            >
              <EMAIL>
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  )
}

export default InfoModal
