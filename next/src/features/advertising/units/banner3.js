import { demandConfig, sizeDefaults } from './demandConfig'

export const banner3 = {
  id: 'banner-3',
  path: '/21841313772,22554256/kitco/mid_banner_three',
  sizes: sizeDefaults.banner,
  sizeMappingName: 'banner',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            {
              minViewPort: [0, 0],
              sizes: [
                [300, 250],
                [336, 280],
              ],
            },
            { minViewPort: [768, 0], sizes: [[728, 90]] },
            { minViewPort: [1270, 0], sizes: [[728, 90]] },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091228',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091229',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'criteo',
          params: {
            networkId: demandConfig.criteo.networkId,
          },
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1802120',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '1199488',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '810797',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560686',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'p7dvw4qXVgdFmobZh0iVrAIg',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'oMhlPk2h78uNh9BQHYgv7cXj',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '858365941',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: 'b79b9d61064957cf91b9',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'minutemedia',
          params: {
            org: demandConfig.minutemedia.org,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467941,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
      ],
    },
  ],
}
