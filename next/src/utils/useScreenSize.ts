import { useMedia } from 'use-media'

const useScreenSize = (): {
  isDesktopExtraLarge: boolean
  isDesktop: boolean
  isTablet: boolean
  isMobile: boolean
  isDesktopForNavBar: boolean
  isDesktopAds: boolean
} => {
  const isDesktopExtraLarge = useMedia({ minWidth: '1280px' })
  // kitco doesnt follow screen size standards
  const isDesktop = useMedia({ minWidth: '1240px' })
  const isDesktopForNavBar = useMedia({ minWidth: '1270px' })
  const isTablet = useMedia({ minWidth: '768px', maxWidth: '1240px' })
  const isMobile = useMedia({ maxWidth: '767px' })
  // I've added this temporarily, the desktop one above does NOT match what is used in CSS. But changing the original one could have far reaching impact. - TC 2/8/24
  const isDesktopAds = useMedia({ minWidth: '1270px' })

  return {
    isDesktopExtraLarge,
    isDesktop,
    isMobile,
    isTablet,
    isDesktopForNavBar,
    isDesktopAds,
  }
}

export default useScreenSize
