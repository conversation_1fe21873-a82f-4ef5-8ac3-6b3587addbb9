import { type User, onAuthStateChanged } from 'firebase/auth'
import { useRouter } from 'next/router'
import * as process from 'process'
import { useEffect, useState } from 'react'
import { FormState } from '~/src/components/Auth/Form/LoginForm'
import { fetchSSOToken } from '~/src/services/discourse/api'
import { auth } from '~/src/services/firebase/config'
import { getUsername } from '~/src/services/firebase/service'

/**
 * Custom hook to handle authentication logic.
 * This hook manages the state related to user authentication and redirects.
 */
export function useDiscourseAuth() {
  // Here we're using the useRouter hook to get the query parameters from the URL
  const router = useRouter()

  // State for the errors that might occur during the login process
  const [error, setError] = useState<string | null>(null)

  // State for the forum URL that the user will be redirected to (SSO)
  const [forumUrl, setForumUrl] = useState<string>('')

  // State for the form state (login, register or setup username)
  const [formState, setFormState] = useState<FormState>(FormState.LOGIN)

  // State for the user's login status
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // State for the need to set a username
  const [needUsername, setNeedUsername] = useState(false)

  // State for the redirect URL
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null)
  const [waitingForRedirect, setWaitingForRedirect] = useState(false)

  // State for waiting for the token
  const [waitingForToken, setWaitingForToken] = useState(false)
  const [fetchingToken, setFetchingToken] = useState(false)

  // State for the user registration status
  const [isRegistering, setIsRegistering] = useState(false)

  /**
   * Gets the user's token and fetches the SSO token from the server.
   *
   * @param user
   */
  const getToken = async (user: User) => {
    try {
      // Clear the error message
      setError(null)
      setFetchingToken(true)

      // Get the SSO parameters from the query
      const { sso, sig } = router.query

      // Check if the SSO parameters are available
      if (!sso || !sig) {
        console.error('Missing SSO parameters')
        setFetchingToken(false)
        return
      }

      // Check if the user is verified and has a username
      if (!(await checkUser(user))) {
        setFetchingToken(false)
        return
      }

      // Everything is good, set the user as logged in
      setIsLoggedIn(true)

      // Get the user's ID token
      const idToken: string = await user.getIdToken()

      // Fetch the URL from the server
      const url: string = await fetchSSOToken(idToken, sso, sig)

      // If the URL is not available, display an error message
      if (!url) {
        setError(
          'An error occurred while trying to log in. Please try again. #824',
        )
        setFetchingToken(false)
        return
      }

      // Set the forum URL for manual redirection
      setForumUrl(url)
      setRedirectUrl(url)

      // Token is fetched, stop asking for it
      setWaitingForToken(false)
      setFetchingToken(false)
    } catch (e) {
      console.error(e)
      setError(
        'An error occurred while trying to log in. Please try again. #821',
      )
      setFetchingToken(false)
    }
  }

  /**
   * Redirects the user to the forum.
   *
   * If the forum SSO URL is not available, redirects the user to the forum
   * to try again.
   *
   * @returns {void}
   */
  const handleRedirect = (): void => {
    console.log('Redirecting, waiting for token:', waitingForToken)
    if (forumUrl !== '') {
      setRedirectUrl(forumUrl)
    } else {
      setRedirectUrl(process.env.NEXT_PUBLIC_DISCOURSE_URL)
    }
    setWaitingForRedirect(true)
  }

  /**
   * Redirect the user to the forum after getting the token.
   */
  useEffect(() => {
    console.log(
      'Trying to redirect:',
      redirectUrl,
      waitingForRedirect,
      waitingForToken,
    )
    if (
      !waitingForToken &&
      waitingForRedirect &&
      redirectUrl &&
      router.isReady
    ) {
      router.push(redirectUrl).catch((err) => {
        console.error('Error during redirect:', redirectUrl, err)
      })
      setRedirectUrl(null)
      setWaitingForRedirect(false)
    }
  }, [redirectUrl, router.isReady, waitingForRedirect, waitingForToken])

  /**
   * Checks if the user is logged in and if the user is verified.
   * If the user is not verified, it shows a message to verify the email.
   * If the user does not have a username, it shows a message to set a username.
   * If the user is not logged in, it shows an error message.
   *
   * @param user
   */
  const checkUser = async (user: User) => {
    // Double check if the user is logged in
    if (!user) {
      setIsLoggedIn(false)
      return false
    }

    // Check if the user is verified
    if (!user.emailVerified) {
      setIsLoggedIn(false)
      // Show the error only if the user is not registering
      if (!isRegistering) {
        setError('Please verify your email address before continuing.')
      }
      setFormState(FormState.VERIFY_EMAIL)
      return false
    }

    const hasUsername = await getUsername(user.email)

    // Check if user has a username
    if (hasUsername === null) {
      // If the user does not have a username, show a message to set a username
      setIsLoggedIn(false)
      setFormState(FormState.SETUP_USERNAME)
      setNeedUsername(true)
      return false
    }

    return true
  }

  /**
   * Check if the user is logged in and fetch the SSO token from the server.
   */
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setError(null)

      // Check if the user is logged in
      if (user) {
        setWaitingForToken(true)
      } else {
        setIsLoggedIn(false)
        setRedirectUrl(null)
        setWaitingForToken(false)
      }
    })

    // Clean up the observer
    return () => unsubscribe()
  }, [])

  /**
   * Fetch the token from the server when all conditions are met.
   */
  useEffect(() => {
    const fetchToken = async () => {
      if (
        waitingForToken &&
        !fetchingToken &&
        formState !== FormState.SETUP_USERNAME
      ) {
        await getToken(auth.currentUser)
      }
    }
    fetchToken()
  }, [waitingForToken, router.query, needUsername, fetchingToken, formState])

  /**
   * Reset form state when the user has set a username.
   */
  useEffect(() => {
    if (needUsername === false && formState === FormState.SETUP_USERNAME) {
      setFormState(FormState.LOGIN)
    }
  }, [needUsername, formState])

  return {
    isLoggedIn,
    error,
    forumUrl,
    formState,
    handleRedirect,
    needUsername,
    setIsRegistering,
    setNeedUsername,
    waitingForToken,
  }
}
