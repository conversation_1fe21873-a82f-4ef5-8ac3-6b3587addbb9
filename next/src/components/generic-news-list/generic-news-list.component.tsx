import clsx from 'clsx'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { type FC, Fragment } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { TeaserCard } from '~/src/components-news/ArticleTeasers/TeaserCard'
import { TeaserCardForNewParent } from '~/src/components-news/ArticleTeasers/TeaserCardForNewParent'
import { TeaserTextOnlyWithAuthor } from '~/src/components-news/ArticleTeasers/TeaserTextOnlyWithAuthor'
import { TeaserWide } from '~/src/components-news/ArticleTeasers/TeaserWide'
import { TeaserWideForNewParent } from '~/src/components-news/ArticleTeasers/TeaserWideForNewParent'
import { TeaserWideForSubCategory } from '~/src/components-news/ArticleTeasers/TeaserWideForSubCategory'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import { PressReleaseSidebar } from '~/src/components/PressReleases/PressReleaseSidebar'
import type {
  NewsByCategoryGenericQuery,
  NodeListQueueQuery,
} from '~/src/generated'
import type { TeasersUnion } from '~/src/types/types'
import useScreenSize from '~/src/utils/useScreenSize'

type Data =
  | NewsByCategoryGenericQuery['nodeListByCategory']['items']
  | NodeListQueueQuery['queue']['items']

interface Props {
  data: Data | any
  disableAdverts?: boolean
  hideCategory?: boolean
  layoutSecond?: boolean
  layoutNewParent?: boolean
  classTitle?: string
}

export const GenericNewsList: FC<Props> = ({
  data,
  disableAdverts,
  hideCategory,
  layoutSecond,
}) => {
  const { isDesktop, isMobile } = useScreenSize()
  let adCounter = 2

  if (isMobile && layoutSecond) {
    return (
      <GenericNewsListMobileLayoutSecond
        data={data}
        disableAdverts={disableAdverts}
      />
    )
  }

  if (isMobile) {
    return <GenericNewsListMobile data={data} disableAdverts={disableAdverts} />
  }

  function advertInjector(idx: number) {
    if (disableAdverts) return false
    if (idx === 2 || (idx % 7 === 0 && idx !== 0 && adCounter < 3)) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pt-5">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <Fragment key={idx}>
            {idx === 0 && (
              <TeaserWideForSubCategory
                size="xl"
                aspectRatio="auto"
                node={node}
                hideCategory={hideCategory}
              />
            )}
            {idx > 0 && (
              <TeaserWideForSubCategory
                node={node}
                size="lg"
                aspectRatio="auto"
                key={node.id}
                hideCategory={hideCategory}
              />
            )}
            {advertInjector(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={'mx-auto mb-10 md:h-[90px] md:w-[728px]'}
              />
            )}
          </Fragment>
        ))}
      </div>
      {isDesktop && (
        <div className="relative">
          <AdvertisingSlot
            id={'right-rail-1'}
            className={
              'sticky top-[100px] mx-auto mt-60 hidden h-[250px] w-[300px] xl:block'
            }
          />
        </div>
      )}
    </div>
  )
}

export const GenericNewsListLayoutNewParent: FC<Props> = ({
  data,
  disableAdverts,
  hideCategory,
  classTitle,
}) => {
  const { isMobile } = useScreenSize()
  let adCounter = 1

  if (isMobile) {
    return (
      <GenericNewsListMobileLayoutNewParent
        data={data}
        disableAdverts={disableAdverts}
        hideCategory={hideCategory}
      />
    )
  }

  function advertInjector(idx: number) {
    if (disableAdverts) return false
    if (idx === 3 || ((idx - 3) % 5 === 0 && idx !== 0 && adCounter < 3)) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-[20px] py-10">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <Fragment key={idx}>
            {idx === 0 && (
              <TeaserWideForNewParent
                size="xl"
                key={node.id}
                aspectRatio="16x9"
                node={node}
                hideCategory={hideCategory}
                classTitle={classTitle}
              />
            )}
            {idx > 0 && (
              <TeaserWideForNewParent
                node={node}
                size="lg"
                aspectRatio="16x9"
                key={node.id}
                hideCategory={hideCategory}
              />
            )}
            {advertInjector(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={'mx-auto my-12 md:h-[90px] md:w-[728px]'}
              />
            )}
          </Fragment>
        ))}
      </div>
    </div>
  )
}

export const GenericNewsListMobile: FC<Props> = ({
  data,
  hideCategory,
  disableAdverts,
}) => {
  let adCounter = 0
  function shouldInsertAdvert(idx: number) {
    if (idx % 4 === 0 && idx !== 0 && adCounter < 3) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pb-5">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <>
            {idx === 0 && (
              <div className="pb-5">
                <TeaserCard size="md" node={node} hideCategory={hideCategory} />
              </div>
            )}
            {idx > 0 && (
              <div key={idx} className="pt-5">
                <TeaserWide
                  node={node}
                  size="md"
                  aspectRatio="16x9"
                  key={node.id}
                  hideCategory={hideCategory}
                />
              </div>
            )}
            {!disableAdverts && shouldInsertAdvert(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={
                  'mx-auto my-5 h-[250px] w-[300px] bg-purple-800 md:my-10 md:h-[90px] md:w-[728px]'
                }
              />
            )}
          </>
        ))}
      </div>
    </div>
  )
}

export const GenericNewsListMobileLayoutSecond: FC<Props> = ({
  data,
  hideCategory,
}) => {
  let adCounter = 1
  function shouldInsertAdvert(idx: number) {
    if (idx % 4 === 0 && idx !== 0 && adCounter < 3) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pb-5">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <Fragment key={idx}>
            <div key={idx} className="border-t border-ktc-borders pt-5">
              <TeaserWide
                node={node}
                size="md"
                aspectRatio="16x9"
                key={node.id}
                hideCategory={hideCategory}
              />
            </div>
            {shouldInsertAdvert(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={
                  'mx-auto my-5 h-[250px] w-[300px] md:my-10 md:h-[90px] md:w-[728px]'
                }
              />
            )}
          </Fragment>
        ))}
      </div>
    </div>
  )
}

export const GenericNewsListMobileLayoutNewParent: FC<Props> = ({
  data,
  hideCategory,
  disableAdverts,
}) => {
  let adCounter = 1
  const router = useRouter()
  const isMiningPage = router.pathname.includes('/news/category/mining') // This check is needed because there is no uniqueness about the mining landing page on mobile. - TC 1/23/24
  function shouldInsertAdvert(idx: number, isMiningPage: boolean) {
    // We move the 1st ad if on the mining page, as we swap it with the mining banner. - TC 1/23/24
    let insertCounter = 5
    if (isMiningPage) {
      insertCounter = 10
    }
    if (idx % insertCounter === 0 && idx !== 0 && adCounter < 4) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pb-5">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <Fragment key={idx}>
            {idx === 0 && (
              <div className="mb-[18px] border-b border-ktc-borders pb-5 last:border-b-0">
                <TeaserCardForNewParent
                  size="md"
                  node={node}
                  hideCategory={hideCategory}
                />
              </div>
            )}
            {idx > 0 && (
              <div
                key={idx}
                className="mb-[19px] border-b border-ktc-borders last:border-b-0"
              >
                <TeaserWideForNewParent
                  node={node}
                  size="sm"
                  aspectRatio="16x9"
                  key={node.id}
                  hideCategory={hideCategory}
                />
              </div>
            )}
            {idx === 3 && isMiningPage && (
              <AdvertisingSlot
                id="mining-content-billboard"
                className="mx-auto my-[20px] flex min-h-[600px] w-[100%] max-w-[300px] items-center justify-center border-[1px] border-[#ccc] native-sm:h-[252px] native-sm:min-h-[200px] native-sm:max-w-[970px]"
              />
            )}
            {!disableAdverts && shouldInsertAdvert(idx, isMiningPage) && (
              <div
                key={idx + 'adv'}
                className="mb-[18px] border-b border-ktc-borders"
              >
                <AdvertisingSlot
                  id={`banner-${adCounter}`}
                  className={
                    'mx-auto my-[19px] h-[250px] w-[300px] md:my-10 md:h-[90px] md:w-[728px]'
                  }
                />
              </div>
            )}
          </Fragment>
        ))}
      </div>
    </div>
  )
}

export const GenericNewsListWithAuthor: FC<Props> = ({
  data,
  disableAdverts,
}) => {
  const { isMobile } = useScreenSize()

  if (isMobile) {
    return (
      <GenericNewsListWithAuthorMobile
        data={data}
        disableAdverts={disableAdverts}
      />
    )
  }

  return (
    <GenericNewsListWithAuthorDesktopAndTablet
      data={data}
      disableAdverts={disableAdverts}
    />
  )
}

const GenericNewsListWithAuthorMobile: FC<Props> = ({
  data,
  disableAdverts,
}) => {
  const { isDesktop } = useScreenSize()
  let adCounter = 1

  function advertInjector(idx: number) {
    if (disableAdverts) return false
    if (isDesktop) return false
    if (idx === 3 || ((idx - 3) % 5 === 0 && idx !== 0 && adCounter < 3)) {
      adCounter++
      return true
    }
  }

  return (
    <div className="flex flex-col pt-5">
      <>
        {data.map((x, idx: number) => (
          <Fragment key={idx}>
            <div
              className="mb-5 flex gap-5 border-t border-t-ktc-borders pt-5"
              key={x.id}
            >
              <div className="w-[120px] flex-initial">
                <Link href={x?.urlAlias ?? '/'}>
                  <ImageMS
                    src={
                      x?.image?.detail?.default?.srcset ??
                      x?.legacyThumbnailImageUrl
                    }
                    hasLegacyThumbnailImageUrl={!!x?.legacyThumbnailImageUrl}
                    alt={`${x?.title} teaser image`}
                    priority={true}
                    width={400}
                    height={340}
                    service="icms"
                    className={clsx(
                      'w-full',
                      'relative',
                      'mb-2.5 aspect-[4/3] rounded-lg',
                    )}
                  />
                </Link>
              </div>
              <div className="w-[calc(100%_-_140px)] flex-initial">
                <TeaserTextOnlyWithAuthor
                  node={x as any}
                  size="sm"
                  hideCategory={true}
                  hideSummary={true}
                />
              </div>
            </div>
            {advertInjector(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={
                  'mx-auto mb-5 h-[250px] w-[300px] tablet:h-[90px] tablet:w-[728px]'
                }
              />
            )}
          </Fragment>
        ))}
      </>
    </div>
  )
}

const GenericNewsListWithAuthorDesktopAndTablet: FC<Props> = ({
  data,
  disableAdverts,
}) => {
  const { isDesktop } = useScreenSize()
  let adCounter = 1

  function advertInjector(idx: number) {
    if (disableAdverts) return false
    if (isDesktop) return false
    if (idx === 3 || ((idx - 3) % 5 === 0 && idx !== 0 && adCounter < 3)) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pt-10">
      <div className="col-span-3 lg:col-span-2">
        {data?.map((node, idx: number) => (
          <Fragment key={idx}>
            {idx === 0 && (
              <div className="mb-10 flex gap-5">
                <div className="w-[51.66%]">
                  <Link href={node?.urlAlias ?? '/'}>
                    <ImageMS
                      src={
                        node?.teaserImage?.detail?.default?.srcset ??
                        node?.image?.detail?.default?.srcset ??
                        node?.legacyThumbnailImageUrl
                      }
                      hasLegacyThumbnailImageUrl={
                        !!node?.legacyThumbnailImageUrl
                      }
                      alt={`${node?.title} teaser image`}
                      priority={true}
                      width={800}
                      height={450}
                      service="icms"
                      className={clsx(
                        'aspect-[8/5] rounded-lg object-cover',
                        'w-full',
                      )}
                    />
                  </Link>
                </div>
                <div className="w-[calc(100%_-_51.66%_+_20px)]">
                  <TeaserTextOnlyWithAuthor
                    node={node}
                    size="xxl"
                    hideCategory={true}
                    hideSummary={false}
                    key={node?.id}
                    classTitle="mt-[-5px]"
                  />
                </div>
              </div>
            )}
            {idx > 0 && (
              <div className="mb-10 flex flex-row gap-5" key={node?.id}>
                <div className="w-[150px]">
                  <Link href={node?.urlAlias ?? '/'}>
                    <ImageMS
                      src={
                        node?.teaserImage?.detail?.default?.srcset ??
                        node?.image?.detail?.default?.srcset ??
                        node?.legacyThumbnailImageUrl
                      }
                      hasLegacyThumbnailImageUrl={
                        !!node?.legacyThumbnailImageUrl
                      }
                      alt={`${node?.title} teaser image`}
                      priority={true}
                      width={800}
                      height={450}
                      service="icms"
                      className={clsx(
                        'aspect-[4/3] rounded-lg object-cover',
                        'w-full',
                      )}
                    />
                  </Link>
                </div>

                <div className="w-[calc(100%_-_170px)]">
                  <TeaserTextOnlyWithAuthor
                    node={node}
                    size="md"
                    hideCategory={true}
                    hideSummary={false}
                    classTitle="mt-[-4px]"
                  />
                </div>
              </div>
            )}
            {advertInjector(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={
                  'mx-auto mb-10 bg-purple-400 md:h-[90px] md:w-[728px]'
                }
              />
            )}
          </Fragment>
        ))}
      </div>
      {isDesktop && (
        <div className="relative">
          <AdvertisingSlot
            id={'right-rail-1'}
            className={'sticky top-4 mx-auto h-[250] w-[300px]'}
          />
        </div>
      )}
    </div>
  )
}

export const GenericNewsListMining: FC<Props> = ({
  data,
  disableAdverts,
  hideCategory,
}) => {
  const { isMobile, isDesktopExtraLarge } = useScreenSize()
  let adCounter = 1

  if (isMobile) {
    return (
      <GenericNewsListMiningMobile
        data={data}
        disableAdverts={disableAdverts}
      />
    )
  }

  function advertInjector(idx: number) {
    if (disableAdverts) return false
    if (idx === 2 || (idx % 7 === 0 && idx !== 0 && adCounter < 3)) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pt-5">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <Fragment key={idx}>
            {idx === 0 && (
              <TeaserWideForSubCategory
                size="xl"
                aspectRatio="auto"
                node={node}
                hideCategory={hideCategory}
                classTitle="mt-[-5px]"
              />
            )}
            {idx > 0 && (
              <TeaserWideForSubCategory
                node={node}
                size="lg"
                aspectRatio="auto"
                key={node.id}
                hideCategory={hideCategory}
              />
            )}
            {advertInjector(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={'mx-auto mb-10 md:h-[90px] md:w-[728px]'}
              />
            )}
          </Fragment>
        ))}
      </div>
      {isDesktopExtraLarge && <PressReleaseSidebar />}
    </div>
  )
}

const GenericNewsListMiningMobile: FC<Props> = ({ data, hideCategory }) => {
  let adCounter = 0
  function shouldInsertAdvert(idx: number) {
    if (idx % 4 === 0 && idx !== 0 && adCounter < 3) {
      adCounter++
      return true
    }
  }

  return (
    <div className="grid grid-cols-3 gap-10 pb-5">
      <div className="col-span-3 xl:col-span-2">
        {data?.map((node: TeasersUnion, idx: number) => (
          <Fragment key={idx}>
            {idx === 0 && (
              <div className="pb-5">
                <TeaserCard size="md" node={node} />
              </div>
            )}
            {idx > 0 && (
              <div key={idx} className="pt-5">
                <TeaserWide
                  node={node}
                  size="md"
                  aspectRatio="16x9"
                  key={node.id}
                  hideCategory={hideCategory}
                />
              </div>
            )}
            {shouldInsertAdvert(idx) && (
              <AdvertisingSlot
                id={`banner-${adCounter}`}
                className={
                  'bg-black-100 mx-auto mb-10 h-[250px] w-[300px] md:h-[90px] md:w-[728px]'
                }
              />
            )}
          </Fragment>
        ))}
      </div>
    </div>
  )
}
