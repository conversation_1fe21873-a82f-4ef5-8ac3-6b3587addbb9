// 1. At top:
import { useState, useEffect } from 'react'

const ENDPOINTS: Record<string,string> = {
  ny:      'https://worldtimeapi.org/api/timezone/America/New_York',
  london:  'https://worldtimeapi.org/api/timezone/Europe/London',
  hk:      'https://worldtimeapi.org/api/timezone/Asia/Hong_Kong',
  mumbai:  'https://worldtimeapi.org/api/timezone/Asia/Calcutta',
}

/**
 * initialMap: server‑side abbreviations to hydrate client immediately.
 */
export function useHubTimeInfo(initialMap: Record<string,string> = {}) {
  const [abbrMap, setAbbrMap] = useState<Record<string,string>>(initialMap)

  useEffect(() => {
    Promise.all(
      Object.entries(ENDPOINTS).map(async ([key, url]) => {
        try {
          const res = await fetch(url)
          const { abbreviation } = await res.json()
          return [key, abbreviation] as [string,string]
        } catch {
          return [key, initialMap[key] || ''] as [string,string]
        }
      })
    ).then(pairs => setAbbrMap(Object.fromEntries(pairs)))
  }, [])

  return abbrMap
}
