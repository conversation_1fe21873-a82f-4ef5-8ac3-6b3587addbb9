(window.webpackJsonp = window.webpackJsonp || []).push([[48], {
  "+ByK": function(e, t, n) {
    e.exports = {
      itemWrap: "itemWrap-3qF9ynvx",
      item: "item-112BZuXZ",
      icon: "icon-2y6cSg4c",
      selected: "selected-3tUrY97Z",
      label: "label-1uw3rZaL",
    };
  },
  "4Fxa": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M12.143 20l1.714-12H12V7h5v1h-2.143l-1.714 12H15v1h-5v-1h2.143z\"/></svg>";
  },
  "6w4h": function(e, t, n) {
    e.exports = {
      row: "row-1NK-hr1x",
      wrap: "wrap-tVR5Scov",
      breakpointNormal: "breakpointNormal-KzkqSNOX",
      breakpointMedium: "breakpointMedium-pbm8vBGT",
      breakpointSmall: "breakpointSmall-32f3vdsC",
    };
  },
  "7EmB": function(e, t, n) {
    e.exports = {
      range: "range-2i0X47Lu",
      valueInput: "valueInput-2CKQO1Lv",
      rangeSlider: "rangeSlider-suG521NL",
      input: "input-2kx6q_pc",
    };
  },
  "8XTa": function(e, t, n) {
    e.exports = { lineEndSelect: "lineEndSelect-25TizNST", right: "right-3IlPseCZ" };
  },
  "9gev": function(e, t, n) {
    e.exports = {
      dropdown: "dropdown-3Y1U1Nkm",
      normal: "normal-i7fM20bU",
      big: "big-2ruaa2z2",
      dropdownMenu: "dropdownMenu-3UShCdED",
    };
  },
  CHgb: function(e, t, n) {
    "use strict";
    n.d(t, "c", function() {
      return d;
    }),
      n.d(t, "a", function() {
        return p;
      }),
      n.d(t, "b", function() {
        return m;
      });
    var a = n("mrSG"),
      r = n("q1tI"),
      i = n.n(r),
      o = n("TSYQ"),
      l = n.n(o),
      c = n("H172"),
      s = n("Iivm"),
      u = n("+ByK");
    function d(e) {
      var t = e.menuItemClassName, n = Object(a.__rest)(e, ["menuItemClassName"]);
      return i.a.createElement(c.a, Object(a.__assign)({}, n, { menuItemClassName: l()(t, u.itemWrap) }));
    }
    function p(e) {
      return i.a.createElement(
        "div",
        { className: l()(u.item, u.selected) },
        i.a.createElement(s.Icon, { className: u.icon, icon: e.icon }),
      );
    }
    function m(e) {
      return i.a.createElement(
        "div",
        { className: u.item },
        i.a.createElement(s.Icon, { className: l()(u.icon, e.iconClassName), icon: e.icon }),
        i.a.createElement("div", { className: u.label }, e.label),
      );
    }
  },
  CaTF: function(e, t, n) {
    e.exports = {
      colorPicker: "colorPicker-3NIIN0Y8",
      fontStyleButton: "fontStyleButton-1445FY6N",
      dropdown: "dropdown-5N0LMJdQ",
      dropdownMenu: "dropdownMenu-yysG7ZzF",
      textarea: "textarea-2fko2YtQ",
      normal: "normal-AMDLZbUS",
      big: "big-1CfoFALo",
    };
  },
  EJl2: function(e, t, n) {
    e.exports = {
      input: "input-DGMBjOG0",
      control: "control-fEqNtKpC",
      item: "item-1ym_rlZM",
      cell: "cell-2byf6BGW",
      fragmentCell: "fragmentCell-1FhKQVpC",
      withTitle: "withTitle-QRL8YpBY",
      title: "title-3K1l5aiR",
    };
  },
  FIOl: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M8.5 13.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm0 0H24\"/></svg>";
  },
  G7lD: function(e, t, n) {
    e.exports = {
      range: "range-46to1pZu",
      disabled: "disabled-v1pYljFO",
      rangeSlider: "rangeSlider-10OqoFDT",
      rangeSliderMiddleWrap: "rangeSliderMiddleWrap-3-EULCcf",
      rangeSliderMiddle: "rangeSliderMiddle-3BlpfHSS",
      dragged: "dragged-36bXd7Hw",
      pointer: "pointer-23eauHul",
      rangePointerWrap: "rangePointerWrap-1vnhGySq",
    };
  },
  HWhk: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path fill=\"currentColor\" fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z\"/></svg>";
  },
  J4oI: function(e, t, n) {
    e.exports = { lineStyleSelect: "lineStyleSelect-1s1ap44b" };
  },
  K5B3: function(e, t, n) {
    e.exports = { input: "input-2Cr8E1vs" };
  },
  KacW: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return h;
    });
    var a = n("mrSG"),
      r = (n("YFKU"), n("q1tI")),
      i = n.n(r),
      o = n("TSYQ"),
      l = n.n(o),
      c = n("8Uy/"),
      s = n("CHgb"),
      u = n("bQEj"),
      d = n("UXdH"),
      p = n("ZSM+"),
      m = n("J4oI"),
      f = [{ type: c.LINESTYLE_SOLID, icon: u, label: window.t("Line") }, {
        type: c.LINESTYLE_DASHED,
        icon: d,
        label: window.t("Dashed Line"),
      }, { type: c.LINESTYLE_DOTTED, icon: p, label: window.t("Dotted Line") }];
    var h = function(e) {
      function t() {
        return null !== e && e.apply(this, arguments) || this;
      }
      return Object(a.__extends)(t, e),
        t.prototype.render = function() {
          var e,
            t,
            n = this.props,
            r = n.lineStyle,
            o = n.className,
            c = n.lineStyleChange,
            u = n.disabled,
            d = n.additionalItems,
            p = n.allowedLineStyles,
            h = (e = p,
              t = Object(a.__spreadArrays)(f),
              void 0 !== e && (t = t.filter(function(t) {
                return e.includes(t.type);
              })),
              t.map(function(e) {
                return {
                  value: e.type,
                  selectedContent: i.a.createElement(s.a, { icon: e.icon }),
                  content: i.a.createElement(s.b, { icon: e.icon, label: e.label }),
                };
              }));
          return d && (h = Object(a.__spreadArrays)([{ readonly: !0, content: d }], h)),
            i.a.createElement(s.c, {
              disabled: u,
              className: l()(m.lineStyleSelect, o),
              hideArrowButton: !0,
              items: h,
              value: r,
              onChange: c,
              "data-name": "line-style-select",
            });
        },
        t;
    }(i.a.PureComponent);
  },
  Q40t: function(e, t, n) {
    e.exports = { titleWrap: "titleWrap-1bavobjQ" };
  },
  To8B: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\"><path fill=\"currentColor\" d=\"M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z\"/></svg>";
  },
  UXdH: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z\"/></svg>";
  },
  UXjO: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return d;
    });
    var a = n("mrSG"),
      r = n("q1tI"),
      i = n.n(r),
      o = n("TSYQ"),
      l = n.n(o),
      c = n("H172"),
      s = n("QpNh"),
      u = n("z1Uu");
    function d(e) {
      var t,
        n = e.fontSize,
        r = e.fontSizes,
        o = void 0 === r ? [] : r,
        d = e.className,
        p = e.disabled,
        m = e.fontSizeChange;
      return i.a.createElement(
        c.a,
        Object(a.__assign)({
          disabled: p,
          className: l()(d, u.defaultSelect),
          menuClassName: u.defaultSelect,
          items: (t = o,
            t.map(function(e) {
              return { value: e.value, content: e.title };
            })),
          value: n,
          onChange: m,
        }, Object(s.a)(e)),
      );
    }
  },
  ZRxn: function(e, t, n) {
    e.exports = {
      unit: "unit-b-yYYxjl",
      input: "input-124DCFwV",
      normal: "normal-3N4mfpQO",
      big: "big-1ixMJ1Cb",
      dropdown: "dropdown-NF5Htz1I",
      dropdownMenu: "dropdownMenu-36OGqXRr",
    };
  },
  "ZSM+": function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"currentColor\"><circle cx=\"9\" cy=\"14\" r=\"1\"/><circle cx=\"4\" cy=\"14\" r=\"1\"/><circle cx=\"14\" cy=\"14\" r=\"1\"/><circle cx=\"19\" cy=\"14\" r=\"1\"/><circle cx=\"24\" cy=\"14\" r=\"1\"/></svg>";
  },
  ZcEB: function(e, t, n) {
    e.exports = { dropdown: "dropdown-2xTnctYy", menu: "menu-ZFNz6yWw" };
  },
  aSdR: function(e, t, n) {
    e.exports = { coordinates: "coordinates-1KIxFYVo", input: "input-1N6PPaVy" };
  },
  aw5J: function(e, t, n) {
    e.exports = {
      container: "container-1sHZXWOS",
      active: "active-2c5C-1Pl",
      disabled: "disabled-1s7-KBqG",
      icon: "icon-2ux26WKl",
    };
  },
  bQEj: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\"><path stroke=\"currentColor\" d=\"M4 13.5h20\"/></svg>";
  },
  bvfV: function(e, t, n) {
    "use strict";
    var a = n("mrSG"),
      r = n("q1tI"),
      i = n.n(r),
      o = n("HSjo"),
      l = n("yqnI"),
      c = function(e) {
        var t = "property" in e ? e.property : void 0,
          n = "defaultValue" in e ? e.defaultValue : e.property.value(),
          a = Object(r.useState)(t ? t.value() : n),
          i = a[0],
          o = a[1];
        Object(r.useEffect)(function() {
          if (t) {
            var n = {};
            return o(t.value()),
              t.subscribe(n, function(t) {
                var n = t.value();
                e.handler && e.handler(n), o(n);
              }),
              function() {
                return t.unsubscribeAll(n);
              };
          }
          return function() {};
        }, [t]);
        return [i, function(e) {
          if (void 0 !== t) {
            var n = t.value();
            l.a.logNormal("Changing property value from \"" + n + "\" to \"" + e + "\""), t.setValue(e);
          }
        }];
      },
      s = n("Q+1u"),
      u = n("qFKp"),
      d = n("fV0y");
    function p(e) {
      var t = e.property, n = Object(a.__rest)(e, ["property"]), r = c({ property: t }), o = r[0], l = r[1];
      return i.a.createElement(
        d.Checkbox,
        Object(a.__assign)({}, n, {
          name: "toggle-enabled",
          checked: o,
          onChange: function() {
            l(!o);
          },
        }),
      );
    }
    var m = n("TSYQ"), f = n.n(m), h = n("eG6P");
    function b(e) {
      return i.a.createElement("div", { className: f()(h.wrap, e.className) }, e.children);
    }
    var v = n("vxCt");
    function g(e) {
      var t = e.property,
        n = e.disabled,
        a = e.title,
        r = e.className,
        o = e.name,
        l = i.a.createElement("span", { className: v.title }, a);
      return i.a.createElement(
        b,
        { className: r },
        t
          && i.a.createElement(p, {
            name: o,
            className: v.checkbox,
            property: t,
            disabled: n,
            label: l,
            labelAlignBaseline: !u.isIE,
          }),
        !t && l,
      );
    }
    function E(e) {
      var t = e.id, n = e.offset, a = e.disabled, r = e.checked, o = e.title, l = e.children;
      return i.a.createElement(
        s.a.Row,
        null,
        i.a.createElement(s.a.Cell, {
          placement: "first",
          verticalAlign: "adaptive",
          offset: n,
          "data-section-name": t,
          colSpan: Boolean(l) ? void 0 : 2,
          checkableTitle: !0,
        }, i.a.createElement(g, { name: "is-enabled-" + t, title: o, disabled: a, property: r })),
        Boolean(l) && i.a.createElement(s.a.Cell, { placement: "last", "data-section-name": t }, l),
      );
    }
    function y(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.checked,
        o = a.disabled,
        l = t.title,
        s = e.offset,
        u = c({ property: o, defaultValue: !1 })[0];
      return i.a.createElement(E, { id: n, offset: s, checked: r, title: l, disabled: e.disabled || u });
    }
    var _ = n("KacW");
    function w(e) {
      var t = e.property, n = c({ property: t }), r = n[0], o = n[1];
      return i.a.createElement(_.a, Object(a.__assign)({}, e, { lineStyle: r, lineStyleChange: o }));
    }
    var N = n("H172"), S = n("kJwE"), C = [1, 2, 3, 4];
    function O(e) {
      var t, n = e.value, a = e.items, r = void 0 === a ? C : a, o = e.disabled, l = e.onChange;
      return i.a.createElement(N.a, {
        disabled: o,
        hideArrowButton: !0,
        className: S.lineWidthSelect,
        items: (t = r,
          t.map(function(e) {
            return {
              value: e,
              selectedContent: c(e, !0),
              content: c(e),
            };
          })),
        value: n,
        onChange: l,
        "data-name": "line-width-select",
      });
      function c(e, t) {
        var a, r = { borderTopWidth: e };
        return i.a.createElement(
          "div",
          { className: S.item },
          i.a.createElement("div", { className: m(S.bar, (a = {}, a[S.isActive] = e === n && !t, a)), style: r }, " "),
        );
      }
    }
    function j(e) {
      var t = e.property, n = c({ property: t }), r = n[0], o = n[1];
      return i.a.createElement(O, Object(a.__assign)({}, e, { value: r, onChange: o }));
    }
    var x = n("nc0P"), k = n("Eyy1");
    function V(e, t, n) {
      var a = Object(r.useState)(e), i = a[0], o = a[1], l = Object(r.useRef)(i);
      return Object(r.useEffect)(function() {
        o(e);
      }, [e, n]),
        [i, function(e) {
          l.current = e, o(e);
        }, function() {
          t(l.current);
        }, function() {
          l.current = e, o(e);
        }];
    }
    var M = n("/3z9"), T = n("WboT"), I = n("Hr11"), z = n("zXvd");
    function A(e) {
      var t = e.property,
        n = Object(a.__rest)(e, ["property"]),
        o = Object(r.useState)(performance.now()),
        l = o[0],
        s = o[1],
        u = c({
          property: t,
          handler: function() {
            return s(performance.now());
          },
        }),
        d = V(u[0], u[1], l);
      return i.a.createElement(P, Object(a.__assign)({}, n, { valueHash: l, sharedBuffer: d }));
    }
    function P(e) {
      var t = e.sharedBuffer,
        n = e.min,
        o = e.max,
        l = e.step,
        c = Object(a.__rest)(e, ["sharedBuffer", "min", "max", "step"]),
        s = t[0],
        d = t[1],
        p = t[2],
        m = t[3],
        f = Object(r.useRef)(null),
        h = Object(r.useRef)(null),
        b = { flushed: !1 };
      return i.a.createElement(
        B,
        Object(a.__assign)({}, c, {
          ref: h,
          onValueChange: function(e, t) {
            d(e), "step" !== t || b.flushed || (p(), b.flushed = !0);
          },
          onKeyDown: function(e) {
            if (e.defaultPrevented || b.flushed) return;
            switch (Object(M.hashFromEvent)(e.nativeEvent)) {
              case 27:
                m(), b.flushed = !0;
                break;
              case 13:
                e.preventDefault();
                var t = Object(k.ensureNotNull)(h.current).getClampedValue();
                null !== t && (d(t), p(), b.flushed = !0);
            }
          },
          onBlur: function(e) {
            var t = Object(k.ensureNotNull)(f.current);
            if (!t.contains(document.activeElement) && !t.contains(e.relatedTarget)) {
              var n = Object(k.ensureNotNull)(h.current).getClampedValue();
              null === n || b.flushed || (d(n), p(), b.flushed = !0);
            }
          },
          value: s,
          roundByStep: !1,
          containerReference: function(e) {
            f.current = e;
          },
          inputMode: u.CheckMobile.iOS() ? void 0 : "numeric",
          min: n,
          max: o,
          step: l,
        }),
      );
    }
    var R = {
        mode: "float",
        min: -Number.MAX_VALUE,
        max: Number.MAX_VALUE,
        step: 1,
        precision: 0,
        inheritPrecisionFromStep: !0,
      },
      B = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          n._selection = null,
            n._restoreSelection = !1,
            n._input = null,
            n._handleSelectionChange = function() {
              n._restoreSelection || document.activeElement !== Object(k.ensureNotNull)(n._input)
                || n._saveSelection(Object(k.ensureNotNull)(n._input));
            },
            n._handleInputReference = function(e) {
              n._input = e, n.props.inputReference && n.props.inputReference(e);
            },
            n._onFocus = function(e) {
              n._saveSelection(Object(k.ensureNotNull)(n._input)),
                n.setState({ focused: !0 }),
                n.props.onFocus && n.props.onFocus(e);
            },
            n._onBlur = function(e) {
              n._selection = null,
                n.setState({ displayValue: D(n.props, n.props.value, W(n.props)), focused: !1 }),
                n.props.onBlur && n.props.onBlur(e);
            },
            n._onValueChange = function(e) {
              var t,
                a,
                r = e.currentTarget,
                i = r.value,
                o = function(e, t, n) {
                  switch (n) {
                    case "integer":
                      return F.test(t) ? t : e;
                    case "float":
                      return t = t.replace(/,/g, "."), L.test(t) ? t : e;
                  }
                }(n.state.displayValue, i, n.props.mode),
                l = U(o),
                c = n._checkValueBoundaries(l);
              n.setState({ displayValue: o }),
                o !== i
                  && (t = n.state.displayValue,
                    a = (a = o).replace(/,/g, "."),
                    (t = t.replace(/,/g, ".")).includes(".") || !a.includes("."))
                  ? (n._restoreSelection = !0, n.forceUpdate())
                  : n._saveSelection(r),
                c.value && D(n.props, l) === o && n.props.onValueChange(l, "input");
            },
            n._onValueByStepChange = function(e) {
              var t = n.props,
                a = t.roundByStep,
                r = void 0 === a || a,
                i = t.step,
                o = void 0 === i ? 1 : i,
                l = U(n.state.displayValue);
              if (!isNaN(l)) {
                var c = new x.Big(l), s = new x.Big(o), u = c.mod(s), d = c.plus(e * o);
                !u.eq(0) && r && (d = d.plus((e > 0 ? 0 : 1) * o).minus(u));
                var p = Number(d);
                n._checkValueBoundaries(p).value
                  && (n.setState({ displayValue: D(n.props, p, W(n.props)) }), n.props.onValueChange(p, "step"));
              }
            };
          var a = G(n.props.value);
          return n.state = {
            value: a,
            displayValue: D(n.props, a, W(n.props)),
            focused: !1,
            valueHash: n.props.valueHash,
          },
            n;
        }
        return Object(a.__extends)(t, e),
          t.prototype.componentDidMount = function() {
            document.addEventListener("selectionchange", this._handleSelectionChange);
          },
          t.prototype.componentWillUnmount = function() {
            document.removeEventListener("selectionchange", this._handleSelectionChange);
          },
          t.prototype.componentDidUpdate = function() {
            var e = Object(k.ensureNotNull)(this._input), t = this._selection;
            if (null !== t && this._restoreSelection && document.activeElement === e) {
              var n = t.start, a = t.end, r = t.direction;
              e.setSelectionRange(n, a, r);
            }
            this._restoreSelection = !1;
          },
          t.prototype.render = function() {
            return i.a.createElement(T.a, {
              inputMode: this.props.inputMode,
              name: this.props.name,
              borderStyle: "thick",
              fontSizeStyle: "medium",
              value: this.state.displayValue,
              className: this.props.className,
              placeholder: this.props.placeholder,
              disabled: this.props.disabled,
              onValueChange: this._onValueChange,
              onValueByStepChange: this._onValueByStepChange,
              containerReference: this.props.containerReference,
              inputReference: this._handleInputReference,
              onClick: this.props.onClick,
              onFocus: this._onFocus,
              onBlur: this._onBlur,
              onKeyDown: this.props.onKeyDown,
            });
          },
          t.prototype.getClampedValue = function() {
            var e = this.props, t = e.min, n = e.max, a = U(this.state.displayValue);
            return isNaN(a) ? null : Object(I.clamp)(a, t, n);
          },
          t.getDerivedStateFromProps = function(e, t) {
            var n = e.valueHash, a = G(e.value);
            return t.value !== a || t.valueHash !== n
              ? { value: a, valueHash: n, displayValue: D(e, a, t.focused && t.valueHash === n ? void 0 : W(e)) }
              : null;
          },
          t.prototype._saveSelection = function(e) {
            var t = e.selectionStart, n = e.selectionEnd, a = e.selectionDirection;
            null !== t && null !== n && null !== a && (this._selection = { start: t, end: n, direction: a });
          },
          t.prototype._checkValueBoundaries = function(e) {
            var t = this.props;
            return {
              value: function(e, t, n) {
                var a = e >= t, r = e <= n;
                return { passMin: a, passMax: r, pass: a && r, clamped: Object(I.clamp)(e, t, n) };
              }(e, t.min, t.max).pass,
            };
          },
          t.defaultProps = R,
          t;
      }(i.a.PureComponent),
      F = /^-?[0-9]*$/,
      L = /^(-?([0-9]+\.?[0-9]*)|(-?[0-9]*))$/;
    function D(e, t, n) {
      return null !== (t = G(t)) && void 0 !== n && (n = Math.max(H(t), n)),
        function(e, t) {
          if (null === e) return "";
          return new z.NumericFormatter(t).format(e);
        }(t, n);
    }
    function W(e) {
      var t = 0;
      return e.inheritPrecisionFromStep && e.step <= 1 && (t = H(e.step)), Math.max(e.precision, t) || void 0;
    }
    function H(e) {
      var t = Math.trunc(e).toString();
      return Object(I.clamp)(z.NumericFormatter.formatNoE(e).length - t.length - 1, 0, 15);
    }
    function U(e, t) {
      return new z.NumericFormatter(t).parse(e);
    }
    function G(e) {
      return "number" == typeof e && Number.isFinite(e) ? e : null;
    }
    var Y = n("eJTA"), q = n("7MId"), K = n("Tmoa");
    function X(e) {
      var t = e.color,
        n = e.thickness,
        r = e.thicknessItems,
        o = e.noAlpha,
        l = c({ property: t }),
        s = l[0],
        u = l[1],
        d = c(n ? { property: n } : { defaultValue: void 0 }),
        p = d[0],
        m = d[1];
      return i.a.createElement(
        q.a,
        Object(a.__assign)({}, e, {
          color: function() {
            if (!s) return null;
            return Object(Y.rgbToHexString)(Object(Y.parseRgb)(s));
          }(),
          onColorChange: function(e) {
            var t = s ? Object(K.alphaToTransparency)(Object(Y.parseRgba)(s)[3]) : 0;
            u(Object(K.generateColor)(String(e), t, !0));
          },
          thickness: p,
          thicknessItems: r,
          onThicknessChange: m,
          opacity: o ? void 0 : s ? Object(Y.parseRgba)(s)[3] : void 0,
          onOpacityChange: o ? void 0 : function(e) {
            u(Object(K.generateColor)(s, Object(K.alphaToTransparency)(e), !0));
          },
        }),
      );
    }
    var Q = n("YFKU"),
      Z = n("a7Ha"),
      J = n("CHgb"),
      $ = n("QpNh"),
      ee = n("FIOl"),
      te = n("jAqK"),
      ne = n("8XTa"),
      ae = [{ type: Z.LineEnd.Normal, icon: ee, label: window.t("Normal") }, {
        type: Z.LineEnd.Arrow,
        icon: te,
        label: window.t("Arrow"),
      }],
      re = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          return n._items = [],
            n._items = ae.map(function(e) {
              return {
                value: e.type,
                selectedContent: i.a.createElement(J.a, { icon: e.icon }),
                content: i.a.createElement(J.b, {
                  icon: e.icon,
                  iconClassName: f()(t.isRight && ne.right),
                  label: e.label,
                }),
              };
            }),
            n;
        }
        return Object(a.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props, t = e.lineEnd, n = e.className, r = e.lineEndChange, o = e.isRight, l = e.disabled;
            return i.a.createElement(
              J.c,
              Object(a.__assign)({
                disabled: l,
                className: f()(ne.lineEndSelect, o && ne.right, n),
                items: this._items,
                value: t,
                onChange: r,
                hideArrowButton: !0,
              }, Object($.a)(this.props)),
            );
          },
          t;
      }(i.a.PureComponent);
    function ie(e) {
      var t = e.property, n = c({ property: t }), r = n[0], o = n[1];
      return i.a.createElement(re, Object(a.__assign)({}, e, { lineEnd: r, lineEndChange: o }));
    }
    var oe, le = n("6w4h");
    function ce(e) {
      var t = e.children, n = e.className, a = e.breakPoint, i = void 0 === a ? "Normal" : a;
      return r.createElement(
        b,
        { className: m(le.wrap, n, le["breakpoint" + i]) },
        r.Children.map(t, function(e) {
          return r.isValidElement(e)
            ? r.createElement("span", { key: null === e.key ? void 0 : e.key, className: le.row }, e)
            : e;
        }),
      );
    }
    var se = ((oe = {})[1] = "float", oe[0] = "integer", oe), ue = n("vqb8"), de = n("eU7S");
    function pe(e) {
      var t,
        n,
        a = e.definition,
        o = a.id,
        l = a.properties,
        s = l.checked,
        u = l.disabled,
        d = l.leftEnd,
        p = l.rightEnd,
        f = l.value,
        h = l.extendLeft,
        b = l.extendRight,
        v = a.title,
        g = a.valueMin,
        y = a.valueMax,
        _ = a.valueStep,
        N = a.valueUnit,
        S = a.extendLeftTitle,
        C = a.extendRightTitle,
        O = e.offset,
        x = c({ property: s, defaultValue: !0 })[0],
        k = c({ property: u, defaultValue: !1 })[0],
        V = Object(ue.a)({ watchedValue: g, defaultValue: void 0 }),
        M = Object(ue.a)({ watchedValue: y, defaultValue: void 0 }),
        T = Object(ue.a)({ watchedValue: _, defaultValue: void 0 }),
        I = Object(ue.a)({ watchedValue: N, defaultValue: void 0 }),
        z = e.disabled || !x;
      return i.a.createElement(
        r.Fragment,
        null,
        i.a.createElement(
          E,
          { id: o, offset: O, checked: s, title: v, disabled: e.disabled || k },
          i.a.createElement(
            ce,
            { className: de.line, breakPoint: "Small" },
            i.a.createElement(
              r.Fragment,
              null,
              function() {
                var t = e.definition, n = t.properties, a = n.color, r = n.width, o = t.widthValues;
                if (a) {
                  return i.a.createElement(
                    "span",
                    { className: de.control },
                    i.a.createElement(X, { color: a, thickness: r, disabled: z, thicknessItems: o }),
                  );
                }
                return r
                  && i.a.createElement(
                    "span",
                    { className: de.control },
                    i.a.createElement(j, { items: o, property: r, disabled: z }),
                  );
              }(),
              (n = e.definition.properties.style)
                && i.a.createElement(
                  "span",
                  { className: de.control },
                  i.a.createElement(w, { property: n, disabled: z }),
                ),
            ),
            (d || p || f)
              && i.a.createElement(
                r.Fragment,
                null,
                i.a.createElement(
                  r.Fragment,
                  null,
                  d
                    && i.a.createElement(ie, {
                      className: de.control,
                      property: d,
                      disabled: z,
                      "data-name": "left-end-select",
                    }),
                  p
                    && i.a.createElement(ie, {
                      className: de.control,
                      property: p,
                      disabled: z,
                      "data-name": "right-end-select",
                      isRight: !0,
                    }),
                ),
                (t = e.definition.valueType,
                  f
                  && i.a.createElement(
                    "span",
                    { className: m(de.valueInput, de.control) },
                    i.a.createElement(A, {
                      className: de.input,
                      property: f,
                      min: V,
                      max: M,
                      step: T,
                      disabled: z,
                      mode: void 0 !== t ? se[t] : void 0,
                      name: "line-value-input",
                    }),
                    i.a.createElement("span", { className: de.valueUnit }, I),
                  )),
              ),
          ),
        ),
        h && i.a.createElement(E, { id: o + "ExtendLeft", offset: O, checked: h, title: S, disabled: e.disabled || k }),
        b
          && i.a.createElement(E, {
            id: o + "ExtendRight",
            offset: O,
            checked: b,
            title: C,
            disabled: e.disabled || k,
          }),
      );
    }
    var me = n("4vW/"), fe = n("gla1");
    function he(e) {
      var t = e.property,
        n = e.options,
        o = Object(a.__rest)(e, ["property", "options"]),
        l = c({ property: t }),
        s = l[0],
        u = l[1],
        d = Object(fe.a)();
      return Object(r.useEffect)(function() {
        var e = function() {
          return d();
        };
        return Array.isArray(n) || n.subscribe(e), function() {
          Array.isArray(n) || n.unsubscribe(e);
        };
      }, []),
        i.a.createElement(
          N.a,
          Object(a.__assign)({}, o, {
            onChange: u,
            value: s,
            items: (Array.isArray(n) ? n : n.value()).map(function(e) {
              return { content: e.title, value: e.value };
            }),
          }),
        );
    }
    var be = n("ioCK"),
      ve = [{ title: Object(Q.t)("Solid"), value: me.ColorType.Solid }, {
        title: Object(Q.t)("Gradient"),
        value: me.ColorType.Gradient,
      }];
    function ge(e) {
      var t = e.disabled,
        n = e.noAlpha,
        a = e.properties,
        r = a.color,
        o = a.gradientColor1,
        l = a.gradientColor2,
        s = a.type,
        u = c({ property: s, defaultValue: me.ColorType.Solid })[0];
      return i.a.createElement(
        ce,
        null,
        i.a.createElement(he, {
          className: be.dropdown,
          menuClassName: be.dropdownMenu,
          disabled: t,
          property: s,
          options: ve,
          "data-name": "background-type-options-dropdown",
        }),
        u === me.ColorType.Solid
          ? i.a.createElement(X, { color: r, disabled: t, noAlpha: n })
          : i.a.createElement(
            i.a.Fragment,
            null,
            i.a.createElement(X, { className: be.firstColorPicker, color: o, disabled: t, noAlpha: n }),
            i.a.createElement(X, { color: l, disabled: t, noAlpha: n }),
          ),
      );
    }
    function Ee(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = t.title,
        o = t.noAlpha,
        l = e.offset,
        s = a.color,
        u = a.checked,
        d = a.disabled,
        p = c({ property: u, defaultValue: !0 })[0],
        m = c({ property: d, defaultValue: !1 })[0],
        f = e.disabled || !p;
      return i.a.createElement(
        E,
        { id: n, offset: l, checked: u, title: r, disabled: e.disabled || m },
        i.a.createElement(
          b,
          null,
          a.hasOwnProperty("type")
            ? i.a.createElement(ge, { properties: a, disabled: f, noAlpha: o })
            : i.a.createElement(X, { color: s, disabled: f, noAlpha: o }),
        ),
      );
    }
    var ye = n("U1eG"), _e = n("HGP3"), we = n("lB1i");
    function Ne(e) {
      var t, n = e.value, a = e.disabled, r = e.onChange;
      return i.a.createElement(
        "div",
        { className: m(we.wrap, (t = {}, t[we.disabled] = a, t)) },
        i.a.createElement(ye.a, {
          hideInput: !0,
          color: _e.colorsPalette["color-tv-blue-500"],
          opacity: 1 - n / 100,
          onChange: function(e) {
            a || r(100 - 100 * e);
          },
        }),
      );
    }
    function Se(e) {
      var t = e.property, n = Object(a.__rest)(e, ["property"]), i = c({ property: t }), o = i[0], l = i[1];
      return r.createElement(Ne, Object(a.__assign)({}, n, { value: o, onChange: l }));
    }
    function Ce(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.transparency,
        o = a.checked,
        l = a.disabled,
        s = t.title,
        u = e.offset,
        d = c({ property: o, defaultValue: !0 })[0],
        p = c({ property: l, defaultValue: !1 })[0],
        m = e.disabled || !d;
      return i.a.createElement(
        E,
        { id: n, offset: u, checked: o, title: s, disabled: e.disabled || p },
        i.a.createElement(b, null, i.a.createElement(Se, { property: r, disabled: m })),
      );
    }
    var Oe = n("oWdB");
    function je(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.color1,
        o = a.color2,
        l = a.checked,
        s = a.disabled,
        u = t.title,
        d = t.noAlpha1,
        p = t.noAlpha2,
        m = e.offset,
        f = c({ property: l, defaultValue: !0 })[0],
        h = c({ property: s, defaultValue: !1 })[0],
        v = e.disabled || !f || h;
      return i.a.createElement(
        E,
        { id: n, offset: m, checked: l, title: u, disabled: e.disabled || h },
        i.a.createElement(b, { className: Oe.twoColors }, g(r, d), g(o, p)),
      );
      function g(e, t) {
        return i.a.createElement(
          "span",
          { className: Oe.colorPicker },
          i.a.createElement(X, { color: e, disabled: v, noAlpha: t }),
        );
      }
    }
    var xe = n("ybVX"), ke = n("ZRxn");
    function Ve(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        o = a.checked,
        l = a.value,
        s = a.unitOptionsValue,
        u = a.disabled,
        d = t.min,
        p = t.max,
        f = t.step,
        h = t.title,
        v = t.unit,
        g = t.unitOptions,
        y = t.type,
        _ = e.offset,
        w = c({ property: o, defaultValue: !0 })[0],
        N = c({ property: u, defaultValue: !1 })[0],
        S = Object(ue.a)({ watchedValue: d, defaultValue: void 0 }),
        C = Object(ue.a)({ watchedValue: p, defaultValue: void 0 }),
        O = Object(ue.a)({ watchedValue: f, defaultValue: void 0 }),
        j = Object(ue.a)({ watchedValue: v, defaultValue: void 0 }),
        x = Object(r.useContext)(xe.b),
        V = e.disabled || !w;
      return i.a.createElement(
        E,
        { id: n, offset: _, checked: o, title: h, disabled: e.disabled || N },
        i.a.createElement(
          b,
          null,
          i.a.createElement(
            ce,
            null,
            i.a.createElement(A, {
              className: m(ke.input, x[n] && ke[x[n]]),
              property: l,
              min: S,
              max: C,
              step: O,
              disabled: V,
              mode: se[y],
              name: "number-input",
            }),
            s
              && i.a.createElement(he, {
                className: ke.dropdown,
                menuClassName: ke.dropdownMenu,
                disabled: V,
                property: s,
                options: Object(k.ensureDefined)(g),
                "data-name": "unit-options-dropdown",
              }),
          ),
          i.a.createElement("span", { className: ke.unit }, j),
        ),
      );
    }
    function Me(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.checked,
        o = a.disabled,
        l = t.childrenDefinitions,
        s = t.title,
        u = e.offset,
        d = c({ property: r, defaultValue: !0 })[0],
        p = c({ property: o, defaultValue: !1 })[0],
        m = e.disabled || !d;
      return i.a.createElement(
        i.a.Fragment,
        null,
        i.a.createElement(E, { id: n, offset: u, checked: r, title: s, disabled: e.disabled || p }),
        l.map(function(e) {
          return i.a.createElement(wt, { key: e.id, disabled: m, definition: e, offset: !0 });
        }),
      );
    }
    var Te = n("UXjO");
    function Ie(e) {
      var t = e.property, n = c({ property: t }), r = n[0], o = n[1];
      return i.a.createElement(
        Te.a,
        Object(a.__assign)({}, e, { fontSize: r, fontSizeChange: o, "data-name": "font-size-select" }),
      );
    }
    var ze = n("Iivm"), Ae = n("aw5J");
    function Pe(e) {
      var t = e.className, n = e.checked, r = e.icon, o = e.disabled, l = e.onClick;
      return i.a.createElement(
        "div",
        Object(a.__assign)({
          className: f()(t, Ae.container, n && !o && Ae.active, o && Ae.disabled),
          onClick: o ? void 0 : l,
          "data-role": "button",
        }, Object($.a)(e)),
        i.a.createElement(ze.Icon, { className: Ae.icon, icon: r }),
      );
    }
    function Re(e) {
      var t = e.icon, n = e.className, i = e.property, o = e.disabled, l = c({ property: i }), s = l[0], u = l[1];
      return r.createElement(
        Pe,
        Object(a.__assign)({
          className: n,
          icon: t,
          checked: s,
          onClick: function() {
            u(!s);
          },
          disabled: o,
        }, Object($.a)(e)),
      );
    }
    var Be = n("Wvr1"), Fe = n("k+zC"), Le = n("jggR");
    function De(e) {
      var t = e.value,
        n = e.className,
        a = e.onChange,
        i = e.disabled,
        o = e.readonly,
        l = e.name,
        c = e.highlight,
        s = e.onFocus,
        u = e.onBlur,
        d = e.intent,
        p = e.borderStyle,
        f = void 0 === p ? "thin" : p,
        h = e.size,
        b = void 0 === h ? "medium" : h,
        v = e.removeRoundBorder,
        g = void 0 === v ? 0 : v,
        E = e.highlightRemoveRoundBorder,
        y = void 0 === E ? 0 : E,
        _ = Object(Be.a)(g),
        w = Object(Be.a)(y);
      return r.createElement(
        "span",
        {
          className: m(
            Le.container,
            n,
            Le["intent-" + d],
            Le["border-" + f],
            Le["size-" + b],
            i && Le.disabled,
            o && Le.readonly,
            _,
            c && Le.highlight,
          ),
        },
        r.createElement("textarea", {
          className: Le.textarea,
          value: t,
          onChange: function(e) {
            i || o || a(e.currentTarget.value);
          },
          onFocus: s,
          onBlur: u,
          disabled: i,
          readOnly: o,
          name: l,
        }),
        c && r.createElement("span", { className: m(Le.shadow, w) }),
      );
    }
    function We(e) {
      return e = Object(Fe.a)(e), r.createElement(De, Object(a.__assign)({}, e));
    }
    function He(e) {
      var t = e.property, n = Object(a.__rest)(e, ["property"]), i = c({ property: t }), o = i[0], l = i[1];
      return r.createElement(We, Object(a.__assign)({}, n, { value: o, onChange: l }));
    }
    var Ue = n("rRJX"),
      Ge = n("4Fxa"),
      Ye = n("CaTF"),
      qe = function(e) {
        return { content: e.title, title: e.title, value: e.value };
      },
      Ke = function(e) {
        return { content: e.title, title: e.title, value: e.value };
      };
    function Xe(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        o = a.color,
        l = a.size,
        u = a.checked,
        d = a.disabled,
        p = a.bold,
        m = a.italic,
        h = a.text,
        v = a.alignmentHorizontal,
        g = a.alignmentVertical,
        y = a.orientation,
        _ = a.backgroundVisible,
        w = a.backgroundColor,
        S = a.borderVisible,
        C = a.borderColor,
        O = a.borderWidth,
        j = a.wrap,
        x = t.title,
        k = t.sizeItems,
        V = t.alignmentTitle,
        M = t.alignmentHorizontalItems,
        T = t.alignmentVerticalItems,
        I = t.orientationTitle,
        z = t.orientationItems,
        A = t.backgroundTitle,
        P = t.borderTitle,
        R = t.borderWidthItems,
        B = t.wrapTitle,
        F = e.offset,
        L = Object(r.useContext)(xe.a),
        D = c({ property: u, defaultValue: !0 })[0],
        W = c({ property: d, defaultValue: !1 })[0],
        H = c({ property: g, defaultValue: void 0 }),
        U = H[0],
        G = H[1],
        Y = c({ property: y, defaultValue: "horizontal" }),
        q = Y[0],
        K = Y[1],
        Q = c({ property: v, defaultValue: void 0 }),
        Z = Q[0],
        J = Q[1],
        $ = c({ property: _, defaultValue: !1 })[0],
        ee = c({ property: S, defaultValue: !1 })[0],
        te = e.disabled || !D;
      return i.a.createElement(
        r.Fragment,
        null,
        function() {
          if (x) {
            return i.a.createElement(
              E,
              { id: n, offset: F, checked: u, title: x, disabled: e.disabled || W },
              i.a.createElement(ce, { breakPoint: "Small" }, ae(), re()),
            );
          }
          return i.a.createElement(
            s.a.Row,
            null,
            i.a.createElement(
              s.a.Cell,
              { placement: "first", colSpan: 2, offset: F, "data-section-name": n },
              ae(),
              re(),
            ),
          );
        }(),
        h
          && i.a.createElement(
            s.a.Row,
            null,
            i.a.createElement(
              s.a.Cell,
              { placement: "first", colSpan: 2, offset: F, "data-section-name": n },
              i.a.createElement(He, {
                className: f()(Ye.textarea, L[n] && Ye[L[n]]),
                property: h,
                disabled: te,
                onFocus: function(e) {
                  e.target.select();
                },
                name: "text-input",
              }),
            ),
          ),
        (v || g)
          && i.a.createElement(
            s.a.Row,
            null,
            i.a.createElement(s.a.Cell, {
              placement: "first",
              verticalAlign: "adaptive",
              offset: F,
              "data-section-name": n,
            }, i.a.createElement(b, null, V)),
            i.a.createElement(
              s.a.Cell,
              { placement: "last", verticalAlign: "adaptive", "data-section-name": n },
              i.a.createElement(
                ce,
                { breakPoint: "Small" },
                void 0 !== U && void 0 !== T
                  && i.a.createElement(N.a, {
                    className: Ye.dropdown,
                    menuClassName: Ye.dropdownMenu,
                    disabled: te,
                    value: U,
                    items: T.map(qe),
                    onChange: G,
                    "data-name": "alignment-vertical-select",
                  }),
                void 0 !== Z && void 0 !== M
                  && i.a.createElement(N.a, {
                    className: Ye.dropdown,
                    menuClassName: Ye.dropdownMenu,
                    disabled: te,
                    value: Z,
                    items: M.map(qe),
                    onChange: J,
                    "data-name": "alignment-horizontal-select",
                  }),
              ),
            ),
          ),
        void 0 !== y && void 0 !== z
          && i.a.createElement(
            s.a.Row,
            null,
            i.a.createElement(s.a.Cell, {
              placement: "first",
              verticalAlign: "adaptive",
              offset: F,
              "data-section-name": n,
            }, i.a.createElement(b, null, I)),
            i.a.createElement(
              s.a.Cell,
              { placement: "last", verticalAlign: "adaptive", "data-section-name": n },
              i.a.createElement(
                ce,
                { breakPoint: "Small" },
                i.a.createElement(N.a, {
                  className: Ye.dropdown,
                  menuClassName: Ye.dropdownMenu,
                  disabled: te,
                  value: q,
                  items: z.map(Ke),
                  onChange: K,
                  "data-name": "orientation-select",
                }),
              ),
            ),
          ),
        ie(A, _, w, !!_ && !$),
        ie(P, S, C, !!S && !ee, O, R),
        j && i.a.createElement(E, { id: n + "Wrap", offset: F, checked: j, title: B, disabled: e.disabled || W }),
      );
      function ne(e, t, n) {
        return e
          ? i.a.createElement(Re, { className: Ye.fontStyleButton, icon: t, property: e, disabled: te, "data-name": n })
          : null;
      }
      function ae() {
        return i.a.createElement(
          r.Fragment,
          null,
          o
            && i.a.createElement(
              "div",
              { className: Ye.colorPicker },
              i.a.createElement(X, { color: o, disabled: te }),
            ),
          l && k && i.a.createElement(Ie, { property: l, fontSizes: k, disabled: te }),
        );
      }
      function re() {
        return i.a.createElement(r.Fragment, null, ne(p, Ue, "toggle-bold"), ne(m, Ge, "toggle-italic"));
      }
      function ie(t, a, r, o, l, c) {
        return r
          ? i.a.createElement(
            E,
            { id: n + "ColorSelect", offset: F, checked: a, title: t, disabled: e.disabled || W },
            i.a.createElement(X, { color: r, thickness: l, thicknessItems: c, disabled: te || o }),
          )
          : null;
      }
    }
    var Qe = n("3G1X"),
      Ze = n("jAh7"),
      Je = n("pZll"),
      $e = n("RgaO"),
      et = n("e3/o"),
      tt = n("+EG+"),
      nt = n("K5B3"),
      at = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          return n._symbolSearch = null,
            n._input = null,
            n._popup = null,
            n._uuid = Object(et.guid)(),
            n._updateSymbolName = function() {
              var e = n.props.definition, t = e.propType, a = e.properties[t];
              n._symbolSearch && (Object(k.ensureNotNull)(n._input).value = a.value(), n._symbolSearch.acceptTypeIn());
            },
            n._onSetSymbol = function(e) {
              var t = n.props.definition, a = t.propType;
              t.properties[a].setValue(e);
            },
            n._handleOutsideClick = function(e) {
              null !== n._input && document.activeElement === n._input && e.target instanceof Node && null !== n._popup
                && !n._popup.contains(e.target) && n._input.blur();
            },
            n._refInput = function(e) {
              n._input = e;
            },
            n.state = { expanded: !1 },
            n;
        }
        return Object(a.__extends)(t, e),
          t.prototype.componentDidMount = function() {
            var e = this, t = this.props.definition;
            t.properties[t.propType].subscribe(this, this._updateSymbolName);
            var n = this.context || Object(Ze.getRootOverlapManager)();
            Object(Je.symbolSearchUIService)().bindToInput(Object(k.ensureNotNull)(this._input), {
              syncWithChartWidget: !1,
              syncOnBlur: !0,
              callback: this._onSetSymbol,
              onPopupOpen: function(t) {
                e._popup = n.ensureWindow(e._uuid), t.appendTo(e._popup), e.setState({ expanded: !0 });
              },
              onPopupClose: function() {
                e._popup = null, e.setState({ expanded: !1 }), n.removeWindow(e._uuid), e._input && e._input.focus();
              },
              keepFocus: !0,
            }).then(function(t) {
              return e._symbolSearch = t;
            });
          },
          t.prototype.componentWillUnmount = function() {
            var e = this.props.definition;
            e.properties[e.propType].unsubscribe(this, this._updateSymbolName);
          },
          t.prototype.render = function() {
            var e = this,
              t = this.props.definition,
              n = t.id,
              a = t.title,
              r = void 0 === a ? "" : a,
              o = this.state.expanded,
              l = this.props.definition,
              c = l.propType,
              s = l.properties[c].value() || "";
            return i.a.createElement(
              E,
              { id: n, title: r },
              i.a.createElement(
                b,
                null,
                i.a.createElement(
                  $e.a,
                  { mouseDown: !0, touchStart: !0, handler: this._handleOutsideClick },
                  function(t) {
                    return i.a.createElement(Qe.b, {
                      className: nt.input,
                      reference: e._refInput,
                      containerReference: t,
                      defaultValue: s,
                      "data-haspopup": !0,
                      "data-expanded": o,
                    });
                  },
                ),
              ),
            );
          },
          t.contextType = tt.b,
          t;
      }(r.PureComponent),
      rt = n("aSdR");
    function it(e) {
      var t = e.definition,
        n = t.properties,
        a = n.x,
        r = n.y,
        o = n.disabled,
        l = t.id,
        c = t.minX,
        u = t.maxX,
        d = t.stepX,
        p = t.minY,
        m = t.maxY,
        f = t.stepY,
        h = t.title,
        b = t.typeX,
        v = t.typeY,
        g = e.offset,
        E = o && o.value() || e.disabled,
        y = Object(ue.a)({ watchedValue: c, defaultValue: void 0 }),
        _ = Object(ue.a)({ watchedValue: u, defaultValue: void 0 }),
        w = Object(ue.a)({ watchedValue: d, defaultValue: void 0 }),
        N = Object(ue.a)({ watchedValue: p, defaultValue: void 0 }),
        S = Object(ue.a)({ watchedValue: m, defaultValue: void 0 }),
        C = Object(ue.a)({ watchedValue: f, defaultValue: void 0 });
      return i.a.createElement(
        s.a.Row,
        null,
        i.a.createElement(s.a.Cell, {
          verticalAlign: "adaptive",
          placement: "first",
          offset: g,
          "data-section-name": l,
        }, i.a.createElement("span", { className: rt.coordinates }, h)),
        (a || r)
          && i.a.createElement(
            s.a.Cell,
            { placement: "last", offset: g, "data-section-name": l },
            i.a.createElement(
              ce,
              { breakPoint: "Medium" },
              r
                && i.a.createElement(A, {
                  className: rt.input,
                  property: r,
                  min: N,
                  max: S,
                  step: C,
                  disabled: E,
                  name: "y-input",
                  mode: void 0 !== v ? se[v] : "integer",
                }),
              a
                && i.a.createElement(A, {
                  className: rt.input,
                  property: a,
                  min: y,
                  max: _,
                  step: w,
                  disabled: E,
                  name: "x-input",
                  mode: void 0 !== b ? se[b] : "integer",
                }),
            ),
          ),
      );
    }
    var ot = n("9gev");
    function lt(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        o = a.checked,
        l = a.option,
        s = a.disabled,
        u = t.title,
        d = t.options,
        p = e.offset,
        m = c({ property: o, defaultValue: !0 })[0],
        h = c({ property: s, defaultValue: !1 })[0],
        v = Object(r.useContext)(xe.b),
        g = e.disabled || !m;
      return i.a.createElement(
        E,
        { id: n, offset: p, checked: o, title: u, disabled: e.disabled || h },
        i.a.createElement(
          b,
          null,
          i.a.createElement(he, {
            className: f()(ot.dropdown, v[n] && ot[v[n]]),
            menuClassName: f()(ot.dropdownMenu, v[n] && ot[v[n]]),
            disabled: g,
            property: l,
            options: d,
            "data-name": "options-dropdown",
          }),
        ),
      );
    }
    var ct = n("Ialn"),
      st = n("G7lD"),
      ut = function(e) {
        function t(t) {
          var n = e.call(this, t) || this;
          return n._container = null,
            n._pointer = null,
            n._rafPosition = null,
            n._rafDragStop = null,
            n._refContainer = function(e) {
              n._container = e;
            },
            n._refPointer = function(e) {
              n._pointer = e;
            },
            n._handlePosition = function(e) {
              null !== n._rafPosition || n.props.disabled || (n._rafPosition = requestAnimationFrame(function() {
                var t = n.props,
                  a = t.from,
                  r = t.to,
                  i = t.min,
                  o = t.max,
                  l = n._getNewPosition(e),
                  c = 1 === n._detectPointerMode(e),
                  s = c ? Object(I.clamp)(l, i, r) : a,
                  u = c ? r : Object(I.clamp)(l, a, o);
                s <= u && n._handleChange(s, u), n._rafPosition = null;
              }));
            },
            n._handleDragStop = function() {
              null !== n._rafDragStop || n.props.disabled || (n._rafDragStop = requestAnimationFrame(function() {
                n.setState({ pointerDragMode: 0 }), n._rafDragStop = null, n.props.onCommit();
              }));
            },
            n._onSliderClick = function(e) {
              u.CheckMobile.any() || (n._handlePosition(e.nativeEvent), n._dragSubscribe());
            },
            n._mouseUp = function(e) {
              n._dragUnsubscribe(), n._handlePosition(e), n._handleDragStop();
            },
            n._mouseMove = function(e) {
              n._handlePosition(e);
            },
            n._onTouchStart = function(e) {
              n._handlePosition(e.nativeEvent.touches[0]);
            },
            n._handleTouch = function(e) {
              n._handlePosition(e.nativeEvent.touches[0]);
            },
            n._handleTouchEnd = function() {
              n._handleDragStop();
            },
            n.state = { pointerDragMode: 0 },
            n;
        }
        return Object(a.__extends)(t, e),
          t.prototype.componentWillUnmount = function() {
            null !== this._rafPosition && (cancelAnimationFrame(this._rafPosition), this._rafPosition = null),
              null !== this._rafDragStop && (cancelAnimationFrame(this._rafDragStop), this._rafDragStop = null),
              this._dragUnsubscribe();
          },
          t.prototype.render = function() {
            var e,
              t,
              n,
              a = this.props,
              i = a.className,
              o = a.disabled,
              l = a.from,
              c = a.to,
              s = a.min,
              u = a.max,
              d = 0 !== this.state.pointerDragMode,
              p = u - s,
              f = (l - s) / p,
              h = (c - s) / p,
              b = Object(ct.isRtl)() ? "right" : "left";
            return r.createElement(
              "div",
              { className: m(i, st.range, o && st.disabled) },
              r.createElement(
                "div",
                {
                  className: st.rangeSlider,
                  ref: this._refContainer,
                  onMouseDown: this._onSliderClick,
                  onTouchStart: this._onTouchStart,
                  onTouchMove: this._handleTouch,
                  onTouchEnd: this._handleTouchEnd,
                },
                r.createElement(
                  "div",
                  { className: st.rangeSliderMiddleWrap },
                  r.createElement("div", {
                    className: m(st.rangeSliderMiddle, d && st.dragged),
                    style: (e = {}, e[b] = 100 * f + "%", e.width = 100 * (h - f) + "%", e),
                  }),
                ),
                r.createElement(
                  "div",
                  { className: st.rangePointerWrap },
                  r.createElement("div", {
                    className: m(st.pointer, d && st.dragged),
                    style: (t = {}, t[b] = 100 * f + "%", t),
                    ref: this._refPointer,
                  }),
                ),
                r.createElement(
                  "div",
                  { className: st.rangePointerWrap },
                  r.createElement("div", {
                    className: m(st.pointer, d && st.dragged),
                    style: (n = {}, n[b] = 100 * h + "%", n),
                  }),
                ),
              ),
            );
          },
          t.prototype._dragSubscribe = function() {
            var e = Object(k.ensureNotNull)(this._container).ownerDocument;
            e && (e.addEventListener("mouseup", this._mouseUp), e.addEventListener("mousemove", this._mouseMove));
          },
          t.prototype._dragUnsubscribe = function() {
            var e = Object(k.ensureNotNull)(this._container).ownerDocument;
            e && (e.removeEventListener("mousemove", this._mouseMove), e.removeEventListener("mouseup", this._mouseUp));
          },
          t.prototype._getNewPosition = function(e) {
            var t = this.props,
              n = t.min,
              a = t.max - n,
              r = Object(k.ensureNotNull)(this._container),
              i = Object(k.ensureNotNull)(this._pointer),
              o = r.getBoundingClientRect(),
              l = i.offsetWidth,
              c = e.clientX - l / 2 - o.left;
            return Object(ct.isRtl)() && (c = o.width - c - l), Object(I.clamp)(c / (o.width - l), 0, 1) * a + n;
          },
          t.prototype._detectPointerMode = function(e) {
            var t = this.props, n = t.from, a = t.to, r = this.state.pointerDragMode;
            if (0 !== r) return r;
            var i = this._getNewPosition(e),
              o = Math.abs(n - i),
              l = Math.abs(a - i),
              c = o === l ? i < n ? 1 : 2 : o < l ? 1 : 2;
            return this.setState({ pointerDragMode: c }), c;
          },
          t.prototype._handleChange = function(e, t) {
            var n = this.props, a = n.from, r = n.to, i = n.onChange;
            e === a && t === r || i(e, t);
          },
          t;
      }(r.PureComponent),
      dt = n("/KDZ"),
      pt = n("7EmB");
    function mt(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.checked,
        o = a.disabled,
        l = a.from,
        s = a.to,
        u = t.title,
        d = t.max,
        p = t.min,
        m = e.offset,
        f = c({ property: r, defaultValue: !0 })[0],
        h = c({ property: o, defaultValue: !1 })[0],
        v = Object(ue.a)({ watchedValue: p, defaultValue: void 0 }),
        g = Object(ue.a)({ watchedValue: d, defaultValue: void 0 }),
        y = c({ property: l }),
        _ = V(y[0], y[1]),
        w = _[0],
        N = _[1],
        S = _[2],
        C = c({ property: s }),
        O = V(C[0], C[1]),
        j = O[0],
        x = O[1],
        k = O[2],
        M = e.disabled || !f,
        T = { flushed: !1 };
      return i.a.createElement(
        E,
        { id: n, offset: m, checked: r, title: u, disabled: e.disabled || h },
        i.a.createElement(
          b,
          { className: pt.range },
          function() {
            if (!v || !g) return null;
            return i.a.createElement(dt.a, { rule: "screen and (max-width: 460px)" }, function(e) {
              return i.a.createElement(
                ce,
                { breakPoint: "Medium" },
                i.a.createElement(
                  i.a.Fragment,
                  null,
                  i.a.createElement(
                    "span",
                    { className: pt.valueInput },
                    i.a.createElement(P, {
                      className: pt.input,
                      sharedBuffer: _,
                      min: v,
                      max: j,
                      step: 1,
                      disabled: M,
                      name: "from-input",
                      mode: "integer",
                    }),
                    e
                      ? i.a.createElement("span", { className: pt.rangeSlider }, "—")
                      : i.a.createElement(ut, {
                        className: pt.rangeSlider,
                        from: w,
                        to: j,
                        min: v,
                        max: g,
                        onChange: I,
                        onCommit: z,
                        disabled: M,
                      }),
                  ),
                ),
                i.a.createElement(
                  i.a.Fragment,
                  null,
                  i.a.createElement(
                    "span",
                    { className: pt.valueInput },
                    i.a.createElement(P, {
                      className: pt.input,
                      sharedBuffer: O,
                      min: w,
                      max: g,
                      step: 1,
                      disabled: M,
                      name: "to-input",
                      mode: "integer",
                    }),
                  ),
                ),
              );
            });
          }(),
        ),
      );
      function I(e, t) {
        N(Math.round(e)), x(Math.round(t));
      }
      function z() {
        T.flushed || (S(), k(), T.flushed = !0);
      }
    }
    var ft = n("Q40t");
    function ht(e) {
      return i.a.createElement(
        s.a.Row,
        null,
        i.a.createElement(s.a.Cell, {
          className: ft.titleWrap,
          placement: "first",
          verticalAlign: "adaptive",
          colSpan: 2,
          "data-section-name": e.name,
          checkableTitle: !0,
        }, i.a.createElement(g, { title: e.title, name: "is-enabled-" + e.name })),
      );
    }
    var bt = n("EJl2");
    function vt(e) {
      var t = e.definitions, n = e.name, a = e.offset;
      return i.a.createElement(
        s.a.Row,
        null,
        i.a.createElement(
          s.a.Cell,
          {
            className: f()(bt.cell, bt.fragmentCell),
            offset: a,
            placement: "first",
            verticalAlign: "adaptive",
            colSpan: 2,
            "data-section-name": n,
            checkableTitle: !0,
          },
          t.map(function(e) {
            return i.a.createElement(
              "div",
              { className: bt.item, key: e.id, "data-section-name": e.id },
              i.a.createElement(Et, { definition: e }),
            );
          }),
        ),
      );
    }
    function gt(e) {
      var t = e.definition, n = e.offset;
      return i.a.createElement(
        s.a.Row,
        null,
        i.a.createElement(s.a.Cell, {
          className: bt.cell,
          offset: n,
          placement: "first",
          verticalAlign: "adaptive",
          colSpan: 2,
          checkableTitle: !0,
        }, i.a.createElement(Et, { definition: t })),
      );
    }
    function Et(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.disabled,
        o = a.checked,
        l = a.color,
        s = a.level,
        u = a.width,
        d = a.style,
        p = t.title,
        m = t.widthValues,
        h = t.styleValues,
        b = c({ property: o, defaultValue: !0 })[0],
        v = c({ property: r, defaultValue: !1 })[0],
        E = v || !b;
      return i.a.createElement(
        i.a.Fragment,
        null,
        i.a.createElement(g, {
          name: "is-enabled-" + n,
          className: f()(p && bt.withTitle),
          title: p && i.a.createElement("span", { className: bt.title }, p),
          property: o,
          disabled: v,
        }),
        s && i.a.createElement(A, {
          className: f()(bt.input, bt.control),
          property: s,
          disabled: E,
        }),
        l && i.a.createElement(X, { className: bt.control, disabled: E, color: l, thickness: u, thicknessItems: m }),
        d && i.a.createElement(w, { className: bt.control, property: d, disabled: E, allowedLineStyles: h }),
      );
    }
    var yt = n("ZcEB");
    function _t(e) {
      var t = e.definition,
        n = t.id,
        a = t.properties,
        r = a.option1,
        o = a.option2,
        l = a.checked,
        s = a.disabled,
        u = t.title,
        d = t.optionsItems1,
        p = t.optionsItems2,
        m = e.offset,
        f = c({ property: l, defaultValue: !0 })[0],
        h = c({ property: s, defaultValue: !1 })[0],
        b = e.disabled || !f;
      return i.a.createElement(
        E,
        { id: n, offset: m, checked: l, title: u, disabled: e.disabled || h },
        i.a.createElement(
          ce,
          { className: yt.twoOptions },
          i.a.createElement(he, {
            className: yt.dropdown,
            menuClassName: yt.menu,
            property: r,
            disabled: b,
            options: d,
            "data-name": "two-options-dropdown-1",
          }),
          i.a.createElement(he, {
            className: yt.dropdown,
            menuClassName: yt.menu,
            property: o,
            disabled: b,
            options: p,
            "data-name": "two-options-dropdown-2",
          }),
        ),
      );
    }
    function wt(e) {
      var t, n = e.definition;
      if (
        function(e) {
          Object(r.useEffect)(function() {
            if (void 0 !== e) {
              var t = Object(a.__assign)({}, e.properties);
              return Object.entries(t).forEach(function(n) {
                var a = n[0], r = n[1];
                void 0 !== r && r.subscribe(t, function() {
                  return l.a.logNormal(
                    "Property \"" + a + "\" in definition \"" + e.id + "\" was updated to value \"" + r.value() + "\"",
                  );
                });
              }),
                function() {
                  Object.entries(t).forEach(function(e) {
                    var n = e[1];
                    void 0 !== n && n.unsubscribeAll(t);
                  });
                };
            }
          }, [e]);
        }(Object(o.z)(n) ? void 0 : n), Object(o.z)(n)
      ) {
        var c = n.definitions;
        return i.a.createElement(
          r.Fragment,
          null,
          n.title && i.a.createElement(ht, { title: n.title, name: n.id }),
          c && (t = c.value(),
            t.reduce(function(e, t) {
              if (Object(o.z)(t) || "leveledLine" !== t.propType) e.push(t);
              else {
                var n = e[e.length - 1];
                Array.isArray(n) ? n.push(t) : e.push([t]);
              }
              return e;
            }, [])).map(function(t) {
              return Array.isArray(t)
                ? i.a.createElement(vt, { key: t[0].id, name: n.id, definitions: t })
                : i.a.createElement(wt, Object(a.__assign)({ key: t.id }, e, { definition: t }));
            }),
          "general" === n.groupType && i.a.createElement(s.a.GroupSeparator, { size: 1 }),
        );
      }
      switch (n.propType) {
        case "line":
          return i.a.createElement(pe, Object(a.__assign)({}, e, { definition: n }));
        case "checkable":
          return i.a.createElement(y, Object(a.__assign)({}, e, { definition: n }));
        case "color":
          return i.a.createElement(Ee, Object(a.__assign)({}, e, { definition: n }));
        case "transparency":
          return i.a.createElement(Ce, Object(a.__assign)({}, e, { definition: n }));
        case "twoColors":
          return i.a.createElement(je, Object(a.__assign)({}, e, { definition: n }));
        case "number":
          return i.a.createElement(Ve, Object(a.__assign)({}, e, { definition: n }));
        case "symbol":
          return i.a.createElement(at, Object(a.__assign)({}, e, { definition: n }));
        case "text":
          return i.a.createElement(Xe, Object(a.__assign)({}, e, { definition: n }));
        case "checkableSet":
          return i.a.createElement(Me, Object(a.__assign)({}, e, { definition: n }));
        case "options":
          return i.a.createElement(lt, Object(a.__assign)({}, e, { definition: n }));
        case "range":
          return i.a.createElement(mt, Object(a.__assign)({}, e, { definition: n }));
        case "coordinates":
          return i.a.createElement(it, Object(a.__assign)({}, e, { definition: n }));
        case "twoOptions":
          return i.a.createElement(_t, Object(a.__assign)({}, e, { definition: n }));
        case "leveledLine":
          return i.a.createElement(gt, Object(a.__assign)({}, e, { definition: n }));
        default:
          return null;
      }
    }
    n.d(t, "a", function() {
      return wt;
    });
  },
  eG6P: function(e, t, n) {
    e.exports = { wrap: "wrap-3VxI_YR4" };
  },
  eU7S: function(e, t, n) {
    e.exports = {
      line: "line-Xef4M09H",
      control: "control-3967I_nS",
      valueInput: "valueInput-1ujFKjiy",
      valueUnit: "valueUnit-WuH55OtL",
      input: "input-3Sw_tvuz",
    };
  },
  gla1: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return r;
    });
    var a = n("q1tI"),
      r = function() {
        return Object(a.useReducer)(function(e, t) {
          return e + 1;
        }, 0)[1];
      };
  },
  "i/MG": function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return u;
    });
    var a = n("mrSG"),
      r = (n("YFKU"), n("q1tI")),
      i = n("TSYQ"),
      o = n("Iivm"),
      l = n("To8B"),
      c = n("kXJy"),
      s = { remove: window.t("Remove") };
    function u(e) {
      var t = e.className,
        n = e.isActive,
        u = e.onClick,
        d = e.title,
        p = e.hidden,
        m = e["data-name"],
        f = void 0 === m ? "remove-button" : m,
        h = Object(a.__rest)(e, ["className", "isActive", "onClick", "title", "hidden", "data-name"]);
      return r.createElement(
        o.Icon,
        Object(a.__assign)({}, h, {
          "data-name": f,
          className: i(c.button, "apply-common-tooltip", n && c.active, p && c.hidden, t),
          icon: l,
          onClick: u,
          title: d || s.remove,
        }),
      );
    }
  },
  ioCK: function(e, t, n) {
    e.exports = {
      dropdown: "dropdown-3GG-vYvb",
      dropdownMenu: "dropdownMenu-1j44OkOz",
      firstColorPicker: "firstColorPicker-rua-txlV",
    };
  },
  jAqK: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"28\" height=\"28\" fill=\"none\"><path stroke=\"currentColor\" d=\"M4.5 13.5H24m-19.5 0L8 17m-3.5-3.5L8 10\"/></svg>";
  },
  jggR: function(e, t, n) {
    e.exports = {
      container: "container-2IsTVQ49",
      focused: "focused-1QCDvHCH",
      readonly: "readonly-3TW7INT8",
      disabled: "disabled-Ju2phm9i",
      "size-small": "size-small-1qV-ZeVg",
      "size-medium": "size-medium-2lM1uGoO",
      "size-large": "size-large-1NHR4lnE",
      "font-size-small": "font-size-small-3eSt8Wl7",
      "font-size-medium": "font-size-medium-3lwViqM8",
      "font-size-large": "font-size-large-uTIroj7p",
      "border-none": "border-none-2LzWNqL7",
      shadow: "shadow-AN9BmmG5",
      "border-thin": "border-thin-2QjYg4o3",
      "border-thick": "border-thick-3XUkSewU",
      "intent-default": "intent-default-1A7eWGEJ",
      "intent-success": "intent-success-1Oz2EYaq",
      "intent-warning": "intent-warning-2CIKi-Sg",
      "intent-danger": "intent-danger-34bo52Yx",
      "intent-primary": "intent-primary-30cIvmgZ",
      "corner-top-left": "corner-top-left-3jqic47X",
      "corner-top-right": "corner-top-right-YZ3WAu2k",
      "corner-bottom-right": "corner-bottom-right-3_DA5L_W",
      "corner-bottom-left": "corner-bottom-left-3lFAslf6",
      textarea: "textarea-bk9MQutx",
    };
  },
  kJwE: function(e, t, n) {
    e.exports = {
      lineWidthSelect: "lineWidthSelect-3ziEuHcz",
      bar: "bar-37_AfcZG",
      isActive: "isActive-dohf9HfR",
      item: "item-2zVrXM_1",
    };
  },
  kXJy: function(e, t, n) {
    e.exports = {
      button: "button-1scLo53s",
      disabled: "disabled-2eJ5fvUz",
      active: "active-2T0ofIIp",
      hidden: "hidden-2GRQzIQ1",
    };
  },
  lB1i: function(e, t, n) {
    e.exports = { wrap: "wrap-K_N9jM1e", disabled: "disabled-2QK47L8c" };
  },
  oWdB: function(e, t, n) {
    e.exports = { twoColors: "twoColors-iyrZVlk4", colorPicker: "colorPicker-3hYQ60NL" };
  },
  rRJX: function(e, t) {
    e.exports =
      "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 28 28\" width=\"28\" height=\"28\"><path fill=\"currentColor\" d=\"M14 21h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h3c2 0 4 1 4 3 0 1 0 2-1.5 3 1.5.5 2.5 2 2.5 4 0 2.75-2.638 4-5 4zM12 9l.004 3c.39.026.82 0 1.25 0C14.908 12 16 11.743 16 10.5c0-1.1-.996-1.5-2.5-1.5-.397 0-.927-.033-1.5 0zm0 5v5h1.5c1.5 0 3.5-.5 3.5-2.5S15 14 13.5 14c-.5 0-.895-.02-1.5 0z\"/></svg>";
  },
  vqb8: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return r;
    });
    var a = n("q1tI"),
      r = function(e) {
        var t = "watchedValue" in e ? e.watchedValue : void 0,
          n = "defaultValue" in e ? e.defaultValue : e.watchedValue.value(),
          r = Object(a.useState)(t ? t.value() : n),
          i = r[0],
          o = r[1];
        return Object(a.useEffect)(function() {
          if (t) {
            o(t.value());
            var e = function(e) {
              return o(e);
            };
            return t.subscribe(e), function() {
              return t.unsubscribe(e);
            };
          }
          return function() {};
        }, [t]),
          i;
      };
  },
  vxCt: function(e, t, n) {
    e.exports = { checkbox: "checkbox-1So8p7GP", title: "title-1uAaOORo" };
  },
  ybVX: function(e, t, n) {
    "use strict";
    n.d(t, "b", function() {
      return i;
    }),
      n.d(t, "a", function() {
        return o;
      });
    var a = n("q1tI"), r = n.n(a), i = r.a.createContext({}), o = r.a.createContext({});
  },
  yqnI: function(e, t, n) {
    "use strict";
    n.d(t, "a", function() {
      return r;
    });
    var a = n("txPx"), r = Object(a.getLogger)("Platform.GUI.PropertyDefinitionTrace");
  },
  z1Uu: function(e, t, n) {
    e.exports = { defaultSelect: "defaultSelect-2RDyqwu4" };
  },
}]);
