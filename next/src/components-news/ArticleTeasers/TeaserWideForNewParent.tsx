import clsx from 'clsx'
import Link from 'next/link'
import { type FC, Fragment } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import type { TeaserWideProps } from '~/src/components-news/ArticleTeasers/TeaserWide'
import { aspectRatioCSS } from '~/src/components-news/ArticleTeasers/aspectRatioCSS'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import cs from '~/src/utils/cs'

export const TeaserWideForNewParent: FC<TeaserWideProps> = ({
  node,
  size,
  aspectRatio,
  hideCategory,
  classTitle,
}) => {
  const sizeCSS = {
    sm: 'text-[14px] leading-[142%]',
    md: 'text-[16px] leading-[130%]',
    lg: 'text-[20px] leading-[130%]',
    xl: 'text-[34px] leading-[115%]',
  }

  const imgContainerCSS = {
    sm: 'w-[120px]',
    md: 'w-[120px]',
    lg: 'w-[150px] relative',
    xl: 'w-6/12 pr-[10px]',
  }

  const bodyContainerCSS = {
    sm: 'block pl-[14px] pl-4 w-[calc(100%_-_120px)]',
    md: 'block pl-[14px] pl-4 w-[calc(100%_-_120px)]',
    lg: 'block pl-5 w-[calc(100%_-_150px)] mt-[-1px]',
    xl: 'block pl-[10px] w-6/12',
  }

  const spacerCSS = {
    sm: 'h-1',
    md: 'h-1',
    lg: 'h-2',
    xl: 'h-2',
  }

  const sumaryCSS = {
    sm: '',
    md: '',
    lg: '',
    xl: 'text-lg leading-[135%]',
  }

  const mbBlockCSS = {
    sm: 'mb-[18px]',
    md: 'mb-[40px]',
    lg: 'mb-[40px]',
    xl: 'mb-[42px]',
  }

  return (
    <div className={cs(['flex w-full', mbBlockCSS[size]])}>
      <Link className={imgContainerCSS[size]} href={node?.urlAlias ?? '/'}>
        <ImageMS
          src={
            node?.teaserImage?.detail?.default?.srcset ??
            node?.image?.detail?.default?.srcset ??
            node?.legacyThumbnailImageUrl
          }
          hasLegacyThumbnailImageUrl={!!node?.legacyThumbnailImageUrl}
          alt={`${node?.title} teaser image`}
          priority={true}
          width={304}
          height={170}
          service="icms"
          className={clsx(
            aspectRatioCSS[aspectRatio],
            'aspect-video rounded object-cover',
            'w-full',
          )}
        />
      </Link>
      <div className={bodyContainerCSS[size]}>
        {!hideCategory ? (
          <>
            <CategoryLink
              urlAlias={node?.category?.urlAlias}
              text={node?.category?.name}
            />
            <div className="h-[2px] bg-transparent" />
          </>
        ) : null}
        <Link href={node?.urlAlias || '/'}>
          <>
            <h3 className={cs([sizeCSS[size], classTitle, 'line-clamp-3'])}>
              {node?.teaserHeadline ?? node?.title}
            </h3>
            <div className={cs([spacerCSS[size]])} />
            {(size === 'lg' || size === 'xl') && (
              <Fragment>
                <div
                  className={cs(['summary', sumaryCSS[size]])}
                  dangerouslySetInnerHTML={{ __html: node?.teaserSnippet }}
                />
                <Spacer className="h-2" />
              </Fragment>
            )}
            <DateStamp stamp={node?.createdAt} />
          </>
        </Link>
      </div>
    </div>
  )
}
