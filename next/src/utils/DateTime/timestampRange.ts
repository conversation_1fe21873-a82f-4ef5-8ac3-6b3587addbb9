import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// Extend dayjs to use timezone and UTC plugins
dayjs.extend(utc)
dayjs.extend(timezone)

/**
 * Create a range of timestamps between two dates with a specified interval.
 * Aligns the start and end times to the nearest multiple of the interval.
 *
 * @param {dayjs.Dayjs} start - The start date
 * @param {dayjs.Dayjs} end - The end date
 * @param {number} interval - Interval in minutes
 * @returns {number[]} - Array of UNIX timestamps
 */
function timestampRange(
  start: dayjs.Dayjs,
  end: dayjs.Dayjs,
  interval: number,
): number[] {
  const timestamps: number[] = []

  // Round start time down to nearest multiple of interval
  const roundedStart = start
    .tz(process.env.NEXT_PUBLIC_TIMEZONE)
    .minute(Math.floor(start.minute() / interval) * interval)
    .second(0)

  // Round end time down to nearest multiple of interval
  const roundedEnd = end
    .tz(process.env.NEXT_PUBLIC_TIMEZONE)
    .minute(Math.floor(end.minute() / interval) * interval)
    .second(0)

  let current = roundedStart.tz(process.env.NEXT_PUBLIC_TIMEZONE)

  // Generate timestamps at the specified interval, including end time if necessary
  while (current.isBefore(roundedEnd) || current.isSame(roundedEnd)) {
    timestamps.push(current.unix())
    current = current.add(interval, 'minute')
  }

  return timestamps
}

export default timestampRange
