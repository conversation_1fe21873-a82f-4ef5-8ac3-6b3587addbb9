import {
  FacebookAuthProvider,
  GoogleAuthProvider,
  linkWithCredential,
  signInWithPopup,
} from '@firebase/auth'
import {
  EmailAuthProvider,
  type User,
  type UserCredential,
  updateProfile as _updateProfile,
  confirmPasswordReset,
  createUserWithEmailAndPassword,
  fetchSignInMethodsForEmail,
  signInWithCustomToken,
  signInWithEmailAndPassword,
} from 'firebase/auth'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import {
  sanitizeDisplayName,
  sanitizeEmail,
} from '~/src/features/auth/sanitize'
import { saveUserData } from '~/src/services/firebase/database'
import { subscribeUser } from '~/src/services/kitco-connect/suscribeUser'
import { auth } from './config'

/**
 * This file contains utility functions for Firebase authentication.
 *
 * The purpose of this file is to provide a set of functions that can be used
 * to interact with Firebase Auth in a more abstract way. This allows for
 * easier testing and better separation of concerns.
 *
 * Also, this file is a good place to add any additional logic that is needed
 * to interact with Firebase Auth.
 */

/**
 * Logs in the user with the given email and password.
 * Returns the user credentials if successful.
 * Throws an error if the login fails.
 *
 * @param email
 * @param password
 */
const login = async (
  email: string,
  password: string,
): Promise<UserCredential> => {
  return signInWithEmailAndPassword(auth, email, password)
}

/**
 * Sends an email verification link to the given user.
 * Throws an error if the email fails to send.
 *
 * @param user : User
 * @param url
 */
const sendEmailVerification = async (
  user: User,
  url: string = null,
): Promise<void> => {
  // Send the verification email using API
  const response = await fetch('/api/auth/sendVerificationEmail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: user.email,
      url: url ?? process.env.NEXT_PUBLIC_URL,
    }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || 'Failed to send verification email')
  }
}

/**
 * Updates the profile of the given user with the given data.
 * Throws an error if the update fails.
 *
 * @param user
 * @param profileData
 */
const updateProfile = async (
  user: User,
  profileData: {
    displayName?: string | null
    photoURL?: string | null
  },
): Promise<void> => {
  const cleanedDisplayName = sanitizeDisplayName(profileData.displayName)

  return _updateProfile(user, {
    ...profileData,
    displayName: cleanedDisplayName ? cleanedDisplayName : user.displayName,
  })
}

/**
 * Sends a password reset email to the given email address.
 *
 * @param email
 * @param url
 */
const resetPassword = async (
  email: string,
  url: string = null,
): Promise<void> => {
  // Send the password reset email using API
  const response = await fetch('/api/auth/sendResetPasswordEmail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email,
      url: url ? url : process.env.NEXT_PUBLIC_URL,
    }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.message || 'Failed to send password reset email')
  }
}

/**
 * Logs out the user
 */
async function logout() {
  // Then sign out the user
  await auth.signOut()
}

/**
 * Registers a new user with the given email and password.
 * Throws an error if the registration fails.
 * This function also sends an email verification to the user.
 * This function also saves additional user data to the database.
 * This function also logs out the user after registration.
 *
 * @param email
 * @param password
 * @param userData
 * @param redirectToForum
 */
const register = async (
  email: string,
  password: string,
  userData: UserData,
  redirectToForum = false,
): Promise<void> => {
  let user: User

  try {
    // Try to register the user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password,
    )

    // Get the user from the userCredential
    user = userCredential.user
  } catch (error) {
    // If the email is already in use, link the credential to the existing user
    if (error.code === 'auth/email-already-in-use') {
      // Fetch the existing sign-in methods for the email
      const signInMethods = await fetchSignInMethodsForEmail(auth, email)

      // If the email is already in use with password, throw an error
      if (signInMethods.includes('password')) {
        throw new Error('email/already-in-use')
      }

      // Create the email/password credential
      const credential = EmailAuthProvider.credential(email, password)

      // Sign in with the existing provider (e.g., Google/Facebook) and link the credential
      const userCredential = await signInWithPopup(
        auth,
        getProviderForProviderId(signInMethods[0]),
      )

      // Get the user from the userCredential
      user = userCredential.user

      // Link the email/password credential to the signed-in user
      await linkWithCredential(user, credential)
    } else {
      throw error
    }
  }

  // Update the user profile with the display name
  await updateProfile(user, userData)

  // Check if we have to create a Discourse Redirect
  let verifyURL = process.env.NEXT_PUBLIC_URL
  if (redirectToForum) {
    verifyURL = process.env.NEXT_PUBLIC_DISCOURSE_URL

    /* this need more work
      await generateDiscourseSSOURL(user, userData)
    console.log('verifyURL:', verifyURL)

    if (!verifyURL) {
      throw new Error('Failed to generate SSO URL. Please try again.')
    }
    */
  }

  // Send the email verification
  await sendEmailVerification(user, verifyURL)

  // Save additional user data to the database
  await saveUserData(user, userData)

  // Subscribe the user to Kitco Connect if they opted in
  if (userData?.newsletter === true) {
    await subscribeUser(email)
  }

  // Close the session (We need to verify the email first)
  await logout()
}

/**
 * Gets the appropriate provider based on the provider ID.
 * @param providerId
 */
const getProviderForProviderId = (providerId: string) => {
  switch (providerId) {
    case 'google.com':
      return new GoogleAuthProvider()
    case 'facebook.com':
      return new FacebookAuthProvider()
    // Add other providers as needed
    default:
      throw new Error(`Unsupported provider: ${providerId}`)
  }
}

/**
 * Checks if the given email exists in the Firebase Auth user database.
 *
 * @param {string} email The email address to check.
 * @param {string} method The sign-in method to check. (Password, Google, etc.)
 * @returns {Promise<boolean>} True if the email exists, false otherwise.
 */
export const userEmailExist = async (
  email: string,
  method = '',
): Promise<boolean> => {
  try {
    const signInMethods = await fetchSignInMethodsForEmail(
      auth,
      sanitizeEmail(email),
    )

    // If no method is provided, check if the email exists
    if (method === '') {
      return signInMethods.length > 0
    }

    // Check if the email exists with the given method
    return signInMethods.includes(method)
  } catch (error) {
    console.error('Error checking email existence:', error)
    throw error
  }
}

/**
 * Checks if the given email has a username in the database.
 *
 * @param {string} email - The email address to check.
 * @param {boolean} returnUsername - If true, return the username if it exists.
 */
export const hasUsername = async (
  email: string,
  returnUsername = false,
): Promise<boolean | UserData> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_URL}/api/auth/hasUsername`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email: sanitizeEmail(email) }),
    },
  )

  if (response.ok) {
    const data = await response.json()

    if (data.username && data.username.length > 0) {
      if (returnUsername) {
        return {
          username: data.username,
          displayUsername: data?.displayUsername ?? data.username,
        }
      }

      return true
    }
  }

  if (returnUsername) {
    return false
  }

  return false
}

/**
 * Fetches the username for a specific user from Firebase Realtime Database
 * using Firebase Admin.
 *
 * @param email
 */
export const getUsername = async (email: string): Promise<string | null> => {
  const userData = await hasUsername(email, true)

  // If the response is a boolean, return null
  if (typeof userData === 'boolean') {
    return null
  }

  // Return the display username if it exists, otherwise return the username
  return userData?.displayUsername ?? userData?.username
}

const loginWithToken = async (token: string): Promise<UserCredential> => {
  return signInWithCustomToken(auth, token)
}

const doConfirmPasswordReset = async (
  code: string,
  newPassword: string,
): Promise<void> => {
  return confirmPasswordReset(auth, code, newPassword)
}

export {
  doConfirmPasswordReset,
  login,
  loginWithToken,
  logout,
  register,
  resetPassword,
  sendEmailVerification,
  updateProfile,
}
