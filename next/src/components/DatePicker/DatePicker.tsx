// src/components/DatePicker/DatePicker.tsx
'use client'

import type { ChangeEvent, FC } from 'react'

interface DatePickerProps {
  value: string // YYYY-MM-DD
  onChange: (v: string) => void
  className?: string
}

const DatePicker: FC<DatePickerProps> = ({ value, onChange, className }) => {
  const handle = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    // Basic validation: ensure it's a valid date format
    if (newValue && !/^\d{4}-\d{2}-\d{2}$/.test(newValue)) {
      return // Don't update if invalid format
    }
    onChange(newValue)
  }

  return (
    <input
      type="date"
      value={value}
      onChange={handle}
      className={
        className ||
        `
        w-32 h-10
        border border-gray-300 rounded-md
        px-2
        text-sm text-gray-900
        cursor-pointer
        appearance-none
      `
      }
    />
  )
}

export default DatePicker
