import clsx from 'clsx'
import type React from 'react'
import { IoClose } from 'react-icons/io5'

/**
 * The Modal component props
 */
interface ModalProps {
  // The Modal is opened or closed
  opened: boolean
  // The event emitter when the user close the Modal
  onClose: () => void
  // The content of the Modal
  children?: React.ReactNode
  // The class name of the Modal
  className?: string
}

/**
 * The Modal component
 * Is a container that can be opened or closed.
 * It can be resized and is responsive.
 *
 * @param opened
 * @param title
 * @param onClose
 * @param children
 * @constructor
 */
const LoginModal = ({ opened, onClose, children, className }: ModalProps) => {
  /**
   * The event emitter when the user click on the overlay to close the Modal
   *
   * @param e
   */
  const handleCloseClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <>
      {/* The Modal */}
      <div
        className={clsx(
          'relative inset-0 z-40',
          'transform-all duration-500 ease-in-out',
          opened ? 'opacity-100' : 'pointer-events-none opacity-0',
        )}
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
      >
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        <div className="fixed inset-0 z-50 w-screen overflow-y-auto">
          <div
            className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
            onClick={handleCloseClick}
            onKeyDown={handleCloseClick}
          >
            <div
              className={clsx(
                'relative transform overflow-hidden rounded-lg bg-white',
                'text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg',
                className ? className : '',
              )}
            >
              <button
                onClick={onClose}
                type="button"
                className={clsx(
                  'absolute right-0 top-0 z-50 p-2 text-gray-400 hover:text-gray-600',
                  'rounded-none border-none hover:text-gray-800',
                  'hover:no-underline focus:text-neutral-800 focus:opacity-100 focus:shadow-none focus:outline-none',
                )}
                aria-label="Close"
              >
                <IoClose className="h-6 w-6" />
              </button>

              <div className="p-4">{children}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default LoginModal
