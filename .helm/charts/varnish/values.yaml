backend:
  host: nginx
  port: 80
configMapName: default-varnish
image:
  repository: "favish/varnish"
  tag: "2.0.0"
malloc: 100M
replicas: 2
resources:
  requests:
    cpu: 10m
    memory: 120M
secret: "cloudcontroltomajordom"
# Used to set heavier caching on these filetypes
staticFormats: "7z|avi|bmp|bz2|css|csv|doc|docx|eot|flac|flv|gif|ico|jpeg|jpg|js|less|mka|mkv|mov|mp3|mp4|mpeg|mpg|odt|otf|ogg|ogm|opus|pdf|png|ppt|pptx|rar|rtf|svg|svgz|swf|tar|tbz|tgz|ttf|txt|txz|wav|webm|webp|woff|woff2|xls|xlsx|xml|xz|zip"
