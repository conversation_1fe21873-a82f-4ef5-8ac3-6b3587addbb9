import type { UseQueryOptions } from '@tanstack/react-query'
import type { GoldIndexTableQuery, GoldIndexWidgetQuery } from '~/src/generated'
import { GoldIndex } from '~/src/lib/GoldIndex/Queries'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import convertMetalData from '~/src/utils/convertMetalData'
import safeParseJSON from '~/src/utils/safeParseJSON'
import * as timestamps from '~/src/utils/timestamps'
import { refetchInterval } from '~/src/utils/timestamps'

/**
 * Get the gold index data
 *
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 *
 * @returns CommodityData[]
 */
const useGIMetalsData = (onlyPreciousMetals?: boolean): CommodityData[] => {
  const { data } = kitcoQuery(getMetalsDataQuery(onlyPreciousMetals))

  return processMetalsData(data)
}

/**
 * Fetch the metal data from the API
 *
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 */
function getMetalsDataQuery(
  onlyPreciousMetals?: boolean,
): UseQueryOptions<GoldIndexTableQuery | GoldIndexWidgetQuery> {
  const args = {
    variables: {
      timestamp: timestamps.current(),
      currency: 'USD',
    },
    options: {
      refetchInterval,
    },
  }

  // Fetch the metal data from the API
  if (onlyPreciousMetals) {
    return GoldIndex.goldIndexWidget(args)
  }

  return GoldIndex.goldIndexTable(args)
}

/**
 * Parse the extra data from the API
 *
 * @param {GoldIndexTableQuery} originalData - The original data from the API
 */
function parseExtraData(
  originalData: GoldIndexTableQuery | GoldIndexWidgetQuery,
) {
  if (!originalData) return {}

  const data = JSON.parse(JSON.stringify(originalData))

  for (const metal in data) {
    if (
      Object.prototype.hasOwnProperty.call(data, metal) &&
      data[metal]?.results
    ) {
      for (let i = 0; i < data[metal]?.results.length; i++) {
        const parsedExtra = safeParseJSON(data[metal].results[i].extra)

        if (parsedExtra !== null) {
          // Add the extra parsed data to the results
          data[metal].results[i] = {
            ...data[metal].results[i],
            extra: {},
            ...parsedExtra,
          }
        } else {
          // Default values if extra is not a valid JSON
          data[metal].results[i] = {
            ...data[metal].results[i],
            extra: {},
            ChangePercentTrade: 0,
            ChangePercentUSD: 0,
            ChangeTrade: 0,
            ChangeUSD: 0,
          }
        }
      }
    }
  }

  return data
}

/**
 * Process the metal data from the GraphQL API
 *
 * @param data
 */
function processMetalsData(
  data: GoldIndexTableQuery | GoldIndexWidgetQuery,
): CommodityData[] {
  // Process the extra data
  const parsedData = parseExtraData(data)

  // Convert the data to the selected currency
  const conversionRate = 1 // USD to USD

  const convertedData = convertMetalData(parsedData, conversionRate)

  return Object.entries(convertedData).map(([name, values]) => {
    const result = values?.results?.[0] ?? {}

    return {
      commodity: name,
      lastBid: {
        bid: result?.bid ? result?.bid.toString() : '0',
        bidVal: result?.bid ?? 0,
        currency: result?.currency ?? '',
        originalTime: result?.originalTime ?? '',
      },
      changeDueToUSD: {
        change: result?.ChangeUSD ? result?.ChangeUSD.toString() : '0',
        changeVal: result?.ChangeUSD ?? 0,
        percentage: result?.ChangePercentUSD
          ? result?.ChangePercentUSD.toString()
          : '0',
        percentageVal: result?.ChangePercentUSD ?? 0,
      },
      changeDueToTrade: {
        change: result?.ChangeTrade ? result?.ChangeTrade.toString() : '0',
        changeVal: result?.ChangeTrade ?? 0,
        percentage: result?.ChangePercentTrade
          ? result?.ChangePercentTrade.toString()
          : '0',
        percentageVal: result?.ChangePercentTrade ?? 0,
      },
      totalChange: {
        change: result?.change ? result?.change.toString() : '0',
        changeVal: result?.change ?? 0,
        percentage: result?.changePercentage
          ? result?.changePercentage.toString()
          : '0',
        percentageVal: result?.changePercentage ?? 0,
      },
    }
  })
}

export { getMetalsDataQuery, processMetalsData, useGIMetalsData }
