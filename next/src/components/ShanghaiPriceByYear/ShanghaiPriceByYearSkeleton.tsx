import type { FC } from 'react'
import { ColumnTitles } from '~/src/components/ShanghaiPriceByYear/ColumnTitles'
import { Values } from '~/src/components/ShanghaiPriceByYear/Values'
import Table from '~/src/components/Table/Table'
import { DynamicWeightSelect } from '~/src/components/WeightSelect'
import useWeight from '~/src/hooks/Weight/useWeight'
import WeightType from '~/src/types/WeightSelect/WeightType'
import cs from '~/src/utils/cs'
import { CurrencySelectCNY } from '../CurrencySelect'
import { TimeSelect } from '../year-select/year-select.component'
import styles from './ShanghaiPriceByYear.module.scss'

interface Props {
  isFetching: boolean
}

const ShanghaiPriceByYearSkeleton: FC<Props> = ({ isFetching }) => {
  const weight = useWeight(WeightType.PreciousMetals, 'GRAM', 'shanghai')
  return (
    <>
      <div className="mb-2 flex items-center justify-end">
        <CurrencySelectCNY classNamesListbox={styles.listbox} />
        <div className={cs(['pl-4'])}>
          <TimeSelect styleSelect={styles.selectStyle} />
        </div>
      </div>
      <Table title="Shanghai Fix Latest Price">
        <ColumnTitles />
        <Values
          timestamp={undefined}
          am={undefined}
          pm={undefined}
          idx={0}
          isLoading={isFetching}
          weight={weight}
        />
      </Table>
      <div className="mt-10" />
      <Table title="Shanghai Fix Historical Prices">
        <div className="no-scrollbar overflow-hidden overflow-x-scroll">
          <ColumnTitles />
          <div className="h-[350px] overflow-x-hidden">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((_, idx) => (
              <Values
                timestamp={undefined}
                am={undefined}
                pm={undefined}
                idx={undefined}
                key={weight.label + idx}
                isLoading={isFetching}
                weight={weight}
              />
            ))}
          </div>
        </div>
      </Table>
    </>
  )
}

export default ShanghaiPriceByYearSkeleton

export const ShanghaiPriceByYearNoData = () => {
  return (
    <>
      <div className="mb-2 flex items-center justify-end">
        {/* <CurrencySelectCNY classNamesListbox={styles.listbox} /> */}
        <div className={cs(['pl-4'])}>
          <DynamicWeightSelect
            type={WeightType.PreciousMetals}
            defaultWeight="GRAM"
            id="shanghai"
          />
        </div>
        <div className={cs(['pl-4'])}>
          <TimeSelect styleSelect={styles.selectStyle} />
        </div>
      </div>
      <Table title="Shanghai Fix Latest Price">
        <ColumnTitles />
        <div
          className={cs([
            'grid grid-cols-1 items-center border-b border-gray-200 px-2 py-1',
          ])}
        >
          <h4 className="text-center text-sm text-gray-600">Loading...</h4>
        </div>
      </Table>
      <div className="mt-10" />
      <Table title="Shanghai Fix Historical Prices">
        <div className="no-scrollbar overflow-hidden overflow-x-scroll">
          <ColumnTitles />
          <div className="grid grid-cols-1 items-center border-b border-gray-200 px-2 py-1">
            <h4 className="text-center text-sm text-gray-600">Loading...</h4>
          </div>
        </div>
      </Table>
    </>
  )
}
