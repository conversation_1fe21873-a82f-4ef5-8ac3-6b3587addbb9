import { type FC, Fragment } from 'react'
import cs from '~/src/utils/cs'
import { teaserTimestamp } from '~/src/utils/teaser-timestamp'

export const DateStamp: FC<{ stamp: string; classNames?: string }> = ({
  stamp,
  classNames,
}) => (
  <Fragment>
    <time
      className={cs([classNames, 'text-xs font-medium text-ktc-date-gray'])}
    >
      {stamp && teaserTimestamp(stamp)}
    </time>
  </Fragment>
)
