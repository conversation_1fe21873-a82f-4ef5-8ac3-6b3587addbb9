import Link from 'next/link'
import * as Navigation from './../Composables'
import ChartsMenu from './ChartsMenu'

const ChartsItem = ({ value, onNodeUpdate }) => {
  return (
    <Navigation.Item value={value}>
      <Navigation.Trigger value={value} onNodeUpdate={onNodeUpdate}>
        <Link
          href="/price/crypto"
          className="whitespace-nowrap text-sm font-bold leading-5 text-white"
        >
          <span className="group-hover:underline">{value}</span>
        </Link>
      </Navigation.Trigger>
      <Navigation.Content>
        <ChartsMenu />
      </Navigation.Content>
    </Navigation.Item>
  )
}

export default ChartsItem
