import admin from 'firebase-admin'
import type { UserImportRecord } from 'firebase-admin/auth'

/**
 * Sleep for a specified number of milliseconds
 * @param ms - Milliseconds to sleep
 * @returns A promise that resolves after the specified time
 */
function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * Export users from a given Firebase app
 *
 * @param app - The Firebase app instance
 * @returns A promise that resolves to an array of user records
 */
async function exportUsers(
  app: admin.app.App,
): Promise<admin.auth.UserRecord[]> {
  const maxResults = 1000 // Maximum number of users per page
  let users: admin.auth.UserRecord[] = []
  let nextPageToken: string | undefined

  do {
    const listUsersResult = await admin
      .auth(app)
      .listUsers(maxResults, nextPageToken)
    users = users.concat(listUsersResult.users)
    nextPageToken = listUsersResult.pageToken
  } while (nextPageToken)

  return users
}

/**
 * Import users into the development environment with passwords
 *
 * @param devApp - The Firebase app instance for the development environment
 * @param prodUsers - List of users from the production environment
 */
async function importUsers(
  devApp: admin.app.App,
  prodUsers: admin.auth.UserRecord[],
): Promise<void> {
  const usersToImport: UserImportRecord[] = prodUsers.map((user) => ({
    uid: user.uid,
    email: user.email,
    emailVerified: user.emailVerified,
    displayName: user.displayName,
    photoURL: user.photoURL,
    phoneNumber: user.phoneNumber,
    disabled: user.disabled,
    passwordHash: user.passwordHash
      ? Buffer.from(user.passwordHash, 'base64')
      : undefined,
    passwordSalt: user.passwordSalt
      ? Buffer.from(user.passwordSalt, 'base64')
      : undefined,
    customClaims: user.customClaims,
    tenantId: user.tenantId ? user.tenantId : undefined,
  }))

  try {
    const result = await admin.auth(devApp).importUsers(usersToImport, {
      hash: {
        algorithm: 'SCRYPT',
        key: Buffer.from(
          '2AgW1czrQiqzdEZKvtY0X9XkzVIkYyZbCIVDCi6NYm8VuOh53+zcBPEHKpsTp54bil/ya5H+GyzaRTnDFhXe/g==',
          'base64',
        ),
        saltSeparator: Buffer.from('Bw==', 'base64'),
        rounds: 8,
        memoryCost: 14,
      },
    })

    console.log(`Successfully imported users: ${result.successCount}`)
    if (result.errors.length > 0) {
      console.error('Errors:', result.errors)
    }
  } catch (error: any) {
    console.error('Error importing users:', error.message)
  }
}

/**
 * Delete users from the development environment that do not exist in production
 *
 * @param prodUsers - List of users from the production environment
 * @param devUsers - List of users from the development environment
 * @param devApp - The Firebase app instance for the development environment
 */
async function deleteUsersNotInProd(
  prodUsers: admin.auth.UserRecord[],
  devUsers: admin.auth.UserRecord[],
  devApp: admin.app.App,
): Promise<void> {
  const prodUserUIDs = new Set(prodUsers.map((user) => user.uid))
  const maxDeletesPerSecond = 10 // Maximum number of deletions per second
  let deleteCount = 0

  for (const devUser of devUsers) {
    if (!prodUserUIDs.has(devUser.uid)) {
      try {
        await admin.auth(devApp).deleteUser(devUser.uid)
        console.log(`Deleted user: ${devUser.uid}`)
        deleteCount++

        // Check if we've reached the rate limit and need to pause
        if (deleteCount >= maxDeletesPerSecond) {
          // Sleep for 1 second to respect the rate limit
          await sleep(1000)
          deleteCount = 0
        }
      } catch (error: any) {
        console.error(`Error deleting user ${devUser.uid}:`, error.message)
      }
    }
  }
}

/**
 * Main function to synchronize users from production to development
 */
export async function syncAuth(
  devApp: admin.app.App,
  prodApp: admin.app.App,
): Promise<void> {
  try {
    console.log('Exporting users from production...')
    const prodUsers = await exportUsers(prodApp)
    console.log(`Exported ${prodUsers.length} users from production.`)

    console.log('Exporting users from development...')
    const devUsers = await exportUsers(devApp)
    console.log(`Exported ${devUsers.length} users from development.`)

    console.log('Importing users with passwords...')
    await importUsers(devApp, prodUsers)

    console.log('Deleting users not present in production from development...')
    await deleteUsersNotInProd(prodUsers, devUsers, devApp)

    console.log('Synchronization process completed.')
  } catch (error: any) {
    console.error('Error in the process:', error.message)
  }
}
