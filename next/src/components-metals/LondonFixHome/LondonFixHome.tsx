import Link from 'next/link'
import type { FC } from 'react'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import type { LondonFixQuery } from '~/src/generated'
import dates from '~/src/utils/dates'
import priceFormatter from '~/src/utils/priceFormatter'

function classAltenator(idx: number): string {
  if (idx % 2) {
    return 'grid grid-cols-3 p-2 bg-[#f5f5f5]'
  }
  return 'grid grid-cols-3 p-2'
}

const LondonFixHome: FC<{ data: LondonFixQuery }> = ({ data }) => {
  return (
    <BlockShell title="London Fix Gold" href="/price/fixes/london-fix">
      <div className="grid grid-cols-3 justify-items-center py-2 desktop:justify-items-stretch">
        <small
          className="ml-1 self-center whitespace-nowrap text-[10px] font-bold"
          suppressHydrationWarning
        >
          {dates.fmtUnix(data?.londonFixUSD?.results[0]?.timestamp - 1)}
        </small>
        <div className="text-right text-xs">AM</div>
        <div className="mr-1 text-right text-xs">PM</div>
      </div>
      {!data && <Loaders />}
      {data &&
        Object.entries(data).map((x: any, idx) => (
          <div
            key={idx}
            className={
              'grid grid-cols-3 justify-items-center p-1 text-xs desktop:justify-items-end'
            }
          >
            <div className="justify-self-center">
              {(idx === 0 && 'USD') ||
                (idx === 1 && 'EUR') ||
                (idx === 2 && 'GBP')}
            </div>
            <div className="text-right text-xs font-semibold">
              {priceFormatter(x?.[1]?.results[0]?.goldAM) || '-'}
            </div>
            <div className="text-right text-xs font-semibold">
              {priceFormatter(x?.[1]?.results[0]?.goldPM) || '-'}
            </div>
          </div>
        ))}

      <div className="hidden grid-cols-3 bg-[#f5f5f5] py-2 desktop:grid">
        <small
          className="ml-1 self-center whitespace-nowrap text-[10px] font-bold"
          suppressHydrationWarning
        >
          {dates.fmtUnix(data?.londonFixUSD?.results[1]?.timestamp - 1)}
        </small>
        <div className="text-right text-xs">AM</div>
        <div className="mr-1 text-right text-xs">PM</div>
      </div>
      {!data && <Loaders />}
      {data &&
        Object.entries(data).map((x: any, idx) => (
          <div
            key={idx}
            className={
              'hidden grid-cols-3 bg-[#f5f5f5] p-1 text-xs desktop:grid'
            }
          >
            <div className="hidden justify-self-center desktop:block">
              {(idx === 0 && 'USD') ||
                (idx === 1 && 'EUR') ||
                (idx === 2 && 'GBP')}
            </div>
            <div className="hidden text-right text-xs font-semibold desktop:block">
              {priceFormatter(x?.[1]?.results?.[1]?.goldAM) || '-'}
            </div>
            <div className="hidden text-right text-xs font-semibold desktop:block">
              {priceFormatter(x?.[1]?.results?.[1]?.goldPM) || '-'}
            </div>
          </div>
        ))}

      <div className="mx-2 mb-2 flex justify-center border-t border-gray-300 pt-2">
        <Link
          className="mx-auto text-center font-semibold"
          href="/price/fixes/london-fix"
        >
          Historical London Fix
        </Link>
      </div>
    </BlockShell>
  )
}

export default LondonFixHome

const howManyLoaders = [1, 2, 3]
const Loaders = () => (
  <>
    {howManyLoaders.map((x: number) => (
      <div className={classAltenator(x)} key={x}>
        <div>
          {(x === 0 && 'USD') || (x === 1 && 'EUR') || (x === 2 && 'GBP')}
        </div>
        <div className="text-right font-semibold">-</div>
        <div className="text-right font-semibold">-</div>
      </div>
    ))}
  </>
)
