import clsx from 'clsx'
import Link from 'next/link'
import type React from 'react'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import type { SilverPgmQuery } from '~/src/generated'
import { isUp } from '~/src/utils/Prices/isUp'
import { styleUpOrDown } from '~/src/utils/Prices/styleUpOrDown'
import capitalizeFirstLetter from '~/src/utils/capitalizeFirstLetter'
import dates from '~/src/utils/dates'
import priceFormatter from '~/src/utils/priceFormatter'
import styles from './SilverPricePGMBlock.module.scss'

interface Props {
  data: SilverPgmQuery
}

interface ValuesRowProps {
  title: string
  href?: string
  valueLeft: number
  valueRight: number
  altBg: boolean
}

const SilverPricePGMBlock: React.FC<Props> = ({ data }) => {
  const silver = data?.silver?.results[0]
  const platinum = data?.platinum?.results[0]
  const palladium = data?.palladium?.results[0]
  const rhodium = data?.rhodium?.results[0]

  const ValuesRow = ({
    title,
    href,
    valueLeft,
    valueRight,
    altBg,
  }: ValuesRowProps) => (
    <div className={`border-t border-gray-200 px-2 ${altBg && 'bg-gray-100'}`}>
      <div className={'flex py-1 desktop:py-[2px]'}>
        {href ? (
          <Link
            href={href}
            className={
              'text-sm capitalize text-[#003871] underline desktop:text-[11.5px]'
            }
          >
            {capitalizeFirstLetter(title)}
          </Link>
        ) : (
          <div className={'text-sm capitalize desktop:text-[11.5px]'}>
            {capitalizeFirstLetter(title)}
          </div>
        )}

        <span
          className={clsx(
            'ml-auto text-sm font-semibold desktop:text-[11.5px]',
          )}
        >
          {priceFormatter(valueLeft) || '-'}
        </span>
        <span
          className={clsx(
            styleUpOrDown(valueRight, styles),
            'flex min-w-[140px] justify-end text-sm font-semibold tablet:min-w-[155px] desktop:min-w-[55px] desktop:text-[11.5px]',
          )}
        >
          {isUp(valueRight) ? '+' : ''}
          {priceFormatter(valueRight) || '-'}
        </span>
      </div>
    </div>
  )

  return (
    <BlockShell title="Silver Price & PGMs" href="/charts/silver">
      <div className={styles.intro}>
        <div>
          <small className="font-bold" suppressHydrationWarning>
            {dates.dayTime()} NY Time
          </small>
        </div>
        <Link className={styles.link} href="/price/fixes/kitco-fix">
          Kitco 10AM Silver Fix
        </Link>
      </div>
      <div className={styles.table}>
        <ValuesRow
          title={'Silver'}
          href={'/charts/silver'}
          valueLeft={silver?.bid}
          valueRight={silver?.change}
          altBg={true}
        />
        <ValuesRow
          title={'Platinum'}
          href={'/charts/platinum'}
          valueLeft={platinum?.bid}
          valueRight={platinum?.change}
          altBg={false}
        />
        <ValuesRow
          title={'Palladium'}
          href={'/charts/palladium'}
          valueLeft={palladium?.bid}
          valueRight={palladium?.change}
          altBg={false}
        />
        <ValuesRow
          title={'Rhodium'}
          valueLeft={rhodium?.bid}
          valueRight={rhodium?.change}
          altBg={false}
        />
      </div>
      <footer className={clsx(styles.footer, '!hidden desktop:!block')}>
        <small className="">
          Click on the metal names to see the associated charts
        </small>
      </footer>
      <footer
        className={clsx(
          styles.footer,
          '!flex border-t border-gray-200 desktop:!hidden',
        )}
      >
        <Link
          className="mx-auto text-center font-semibold"
          href="/price/precious-metals"
        >
          + More Spot Prices
        </Link>
      </footer>
    </BlockShell>
  )
}

export default SilverPricePGMBlock
