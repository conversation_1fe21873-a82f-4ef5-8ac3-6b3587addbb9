import type { User, UserCredential } from 'firebase/auth'
import type React from 'react'
import { type FormEvent, useState } from 'react'
import { useRecaptchaVerification } from '~/src/features/auth/recapcha'
import SignIn from '~/src/features/auth/signIn'
import Validator from '~/src/features/auth/validator'
import firebaseError from '~/src/services/firebase/errors'
import {
  getUsername,
  hasUsername,
  logout,
  userEmailExist,
} from '~/src/services/firebase/service'

interface UseLoginWithEmailProps {
  onError: (error?: string) => void
  onLoginProcessStarted?: () => void
  onNeedUsername?: (user: User) => void
  onNeedVerification?: (user: User) => void
  onSuccess: () => void
  onUserNotFound: (email: string) => void
}

export const useLoginWithEmail = ({
  onError,
  onLoginProcessStarted,
  onNeedUsername,
  onNeedVerification,
  onSuccess,
  onUserNotFound,
}: UseLoginWithEmailProps) => {
  // State for the email and password input fields.
  const [email, setEmail] = useState<string>('')
  const [password, setPassword] = useState<string>('')

  // State for any error messages.
  const [emailError, setEmailError] = useState<string>()
  const [passwordError, setPasswordError] = useState<string>()

  // State for the loading indicator.
  const [isLoading, setIsLoading] = useState<boolean>(false)

  // State for the alert message.
  const [showAlert, setShowAlert] = useState<boolean>(false)

  // Custom hook to verify the user with reCAPTCHA
  const verifyUserRecaptcha = useRecaptchaVerification()

  // Step 1 for email, Step 2 for password
  const [step, setStep] = useState<number>(1)

  /**
   * Handle the email input change.
   * Validate the email address.
   * Show an error message if the email is invalid.
   * Clear the error message if the email is valid.
   * Set the email state.
   *
   * @param e
   */
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Set step to 1 if email is changed
    setStep(1)

    // Clear any previous errors.
    setEmailError(null)
    onError()
    setShowAlert(false)

    // Convert the email to lowercase and remove any whitespace.
    const newEmail = e.target.value.toString()
      ? e.target.value.toString().toLowerCase().trim()
      : ''

    // Set the email state.
    setEmail(newEmail)

    // If the email is invalid, show an error message.
    if (!Validator.validateEmail(newEmail)) {
      setEmailError('Please enter a valid email.')
    }
  }

  /**
   * Handle the password input change.
   * Validate the password.
   * Show an error message if the password is invalid.
   * Clear the error message if the password is valid.
   * Set the password state.
   *
   * @param e
   */
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Convert the password to a string and remove any whitespace.
    const newPassword = e.target.value.toString().trim()

    // Set the password state.
    setPassword(newPassword)

    // Validate the password.
    const passwordErrors = Validator.validatePassword(newPassword)

    // Show an error message if the password is invalid.
    if (passwordErrors.errors.length > 0) {
      // Only show the first error message.
      setPasswordError(passwordErrors.errors[0])
    } else {
      setPasswordError(null)
    }
  }

  /**
   * Handle the email submit, check if the email exists in the Firebase Auth user database.
   * If the email exists, proceed to the password step.
   * If the email does not exist, call onUserNotFound.
   *
   */
  const handleEmailSubmit = async () => {
    // Show a loading indicator.
    setIsLoading(true)

    // Clear any previous errors.
    onError()

    try {
      // Check if the email exists in the Firebase Auth user database
      // And if the user has a password sign-in method
      if (await userEmailExist(email, 'password')) {
        // If the user does not have a username, show an alert message
        // (it's an old user without username)
        if (!(await hasUsername(email))) {
          setShowAlert(true)
        }

        setStep(2) // Proceed to password step
      } else {
        onUserNotFound(email) // Call onUserNotFound if email is not registered
      }
    } catch (error) {
      console.error('Error trying to sign in with email:', error)

      // Handle Firebase error by showing an appropriate error message
      const errorMessage = firebaseError(
        error?.code,
        'Failed to login. Please check your credentials and try again.',
      )

      // Show the error message to the user
      onError(errorMessage)
    } finally {
      // Hide loading indicator
      setIsLoading(false)
    }
  }

  /**
   * Handle the password submit, sign in the user with email and password.
   * Verify the user with reCAPTCHA.
   * If the sign-in is successful, call onSuccess.
   * If the email is not verified, call onNeedVerification.
   * If the user does not have a username, call onNeedUsername.
   * If there is an error, call onError with the error message.
   *
   */
  const handlePasswordSubmit = async () => {
    // Show a loading indicator.
    setIsLoading(true)

    // Clear any previous errors.
    onError()

    try {
      // Verify the user with reCAPTCHA
      const recaptchaVerified = await verifyUserRecaptcha()

      // If the reCAPTCHA verification fails, show an error message and return.
      if (!recaptchaVerified) {
        console.error('The reCAPTCHA verification failed. Please try again.')
        onError('The reCAPTCHA verification failed. Please try again.')
        return
      }

      // Sign in with the email and password.
      const userCredential: UserCredential = await SignIn.signInWithEmail(
        email,
        password,
      )

      const user: User = userCredential.user

      // Check if the user's email is verified
      if (!user.emailVerified) {
        // If the email is not verified, sign out immediately and show a message
        await logout()

        // Show a message to the user to verify their email
        onNeedVerification(user)

        return
      }

      const username = await getUsername(user.email)

      // Check if user have a username
      if (username === null) {
        // If the user does not have a username, show a message to the user to set a username
        onNeedUsername(user)

        return
      }

      // Call the onSuccess callback if all checks pass
      onSuccess()
    } catch (error) {
      console.error('Error trying to sign in with email:', error)

      // Handle Firebase error by showing an appropriate error message
      const errorMessage = firebaseError(
        error.code,
        'Failed to login. Please check your credentials and try again.',
      )

      // Show the error message to the user
      onError(errorMessage)
    } finally {
      setIsLoading(false) // Hide loading indicator
    }
  }

  /**
   * Handle the form submission.
   *
   * @param e
   */
  const handleLogin = (e?: FormEvent<HTMLFormElement>) => {
    // Prevent the form from submitting.
    if (e) e.preventDefault()

    // Tell the parent component that the login process has started.
    onLoginProcessStarted()

    step === 1 ? handleEmailSubmit() : handlePasswordSubmit()
  }

  return {
    email,
    password,
    emailError,
    passwordError,
    isLoading,
    showAlert,
    step,
    handleEmailChange,
    handlePasswordChange,
    handleLogin,
  }
}
