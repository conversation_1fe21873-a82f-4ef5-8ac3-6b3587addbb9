import { parseStringPromise } from 'xml2js'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import { refetchInterval } from '~/src/utils/timestamps'

const apiUrl =
  'https://kds2.kitco.com/getValue?apikey=9bnteWVi2kT13528d100c608fn0TlbC6&ver=2.0&symbol=CL&df=2&tf=2&type=xml&kgx=yes'

/**
 * Get the gold index data
 */
const useGIEnergyData = () => {
  const { data } = kitcoQuery(getEnergyDataQuery())

  return data ? processEnergyData(data) : null
}

/**
 * Fetch the oil data from the API
 */
async function fetchEnergyData() {
  const response = await fetch(apiUrl)
  const textData = await response.text()
  const result = await parseStringPromise(textData)
  return result?.Values?.Value[0]
}

/**
 * Get the oil data
 */
const getEnergyDataQuery = () => {
  return {
    queryKey: ['GoldIndexEnergyData'],
    queryFn: fetchEnergyData,
    refetchInterval,
  }
}

/**
 * Process the energy data from the API
 *
 * @param data
 */
const processEnergyData = (data: any): CommodityData => {
  return {
    commodity: 'Oil',
    lastBid: {
      bid: data?.Price[0] ? data?.Price[0].toString() : '0',
      bidVal: data?.Price[0] ? Number.parseFloat(data?.Price[0]) : 0,
      currency: 'USD', // We are assuming that the currency is always USD
      originalTime: data?.Timestamp[0] ?? '',
    },
    changeDueToUSD: {
      change: data?.ChangeUSD[0] ? data?.ChangeUSD[0].toString() : '0',
      changeVal: data?.ChangeUSD[0] ? Number.parseFloat(data?.ChangeUSD[0]) : 0,
      percentage: data?.ChangePercentUSD[0]
        ? data?.ChangePercentUSD[0].toString()
        : '0',
      percentageVal: data?.ChangePercentUSD[0]
        ? Number.parseFloat(data?.ChangePercentUSD[0])
        : 0,
    },
    changeDueToTrade: {
      change: data?.ChangeTrade[0] ? data?.ChangeTrade[0].toString() : '0',
      changeVal: data?.ChangeTrade[0]
        ? Number.parseFloat(data?.ChangeTrade[0])
        : 0,
      percentage: data?.ChangePercentTrade[0]
        ? data?.ChangePercentTrade[0].toString()
        : '0',
      percentageVal: data?.ChangePercentTrade[0]
        ? Number.parseFloat(data?.ChangePercentTrade[0])
        : 0,
    },
    totalChange: {
      change: data?.Change[0] ? data?.Change[0].toString() : '0',
      changeVal: data?.Change[0] ? Number.parseFloat(data?.Change[0]) : 0,
      percentage: data?.ChangePercentage[0]
        ? data?.ChangePercentage[0].toString()
        : '0',
      percentageVal: data?.ChangePercentage[0]
        ? Number.parseFloat(data.ChangePercentage[0])
        : 0,
    },
  }
}

export { getEnergyDataQuery, processEnergyData, useGIEnergyData }
