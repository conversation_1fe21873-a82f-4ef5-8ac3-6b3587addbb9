import type { ReactNode } from 'react'

const KitcoReach = (): ReactNode => {
  return (
    <section id="kitco-reach" className="d-flex align-items-center">
      <div className="container">
        <div className="row">
          <div className="col-12 col-lg-6  text-center">
            <div className="headline-block">
              <h2 className="col-12">
                <strong>the future</strong> IS digital
              </h2>
            </div>
          </div>
          <div className="col-12 col-lg-5 content-block">
            <h2 className="align-center-xs">
              <strong>KITCO</strong> REACH
            </h2>
            <p className="align-center-xs">
              <strong>
                Let our team help you reach your marketing goals! <br />
                We provide fully managed services.
              </strong>
            </p>
            <h3>SOCIAL MEDIA</h3>
            <ul>
              <li>Account creation, branding, posting &amp; engagement</li>
              <li>Campaign setup and daily monitoring</li>
              <li>Competitor analysis and monthly reports</li>
              <li>Branding, leads and growth campaigns</li>
              <li>Pixel installation and custom audience build up</li>
              <li>Conversion tracking and re-marketing</li>
              <li>Content and creatives</li>
              <li>Demographic targeting, A/B split testing &amp; refining</li>
              <li>Unbranded campaigns</li>
            </ul>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-lg-6 content-block">
            <h3>SEARCH AND DISPLAY</h3>
            <ul>
              <li>Account creation and analytics setup</li>
              <li>Full campaign setup and daily monitoring</li>
              <li>Keyword and budget optimization</li>
              <li>Negative keywords setup</li>
              <li>Conversion tracking and re-marketing</li>
              <li>Competitor analysis and monthly reports</li>
              <li>Branding, leads and growth campaigns</li>
              <li>Content and creatives</li>
              <li>Demographic targeting. A/B split testing and refining</li>
              <li>Unbranded campaigns</li>
            </ul>
          </div>
          <div className="col-12 col-lg-6 content-block">
            <h3>Graphic Design</h3>
            <div className="row">
              <div className="h3 col-12">WEB</div>
              <ul className="col-md-4">
                <li>Banners</li>
                <li>Social Media Visuals</li>
                <li>Ad Campaigns</li>
                <li>Websites</li>
                <li>Micro sites</li>
              </ul>
              <ul className="col-md-7">
                <li>Landing Pages</li>
                <li>Logo and Branding</li>
                <li>Corporate Brochure and Report</li>
                <li>Product and Service Brochure</li>
                <li>Company Magazines and Newsletters</li>
              </ul>
            </div>
            <div className="row">
              <div className="h3 col-12">PRINT</div>
              <ul className="col-md-4">
                <li>Printed Banners</li>
                <li>Pamphlets and Flyers</li>
                <li>Business Cards</li>
              </ul>
              <ul className="col-md-7">
                <li>Signs</li>
                <li>Posters</li>
                <li>Decals</li>
              </ul>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-lg-6 content-block">
            <h3>WEB DEVELOPMENT</h3>
            <ul>
              <li>Fully responsive website coding</li>
              <li>Landing page coding</li>
              <li>
                Analytics installation including goals, events and re-marketing
              </li>
              <li>SEO best practices integration</li>
              <li>E-mail, domain and hosting management</li>
              <li>Pixel installation and configuration</li>
              <li>E-mail template integration</li>
            </ul>
          </div>
          <div className="col-12 col-lg-6 content-block">
            <h3>SEO AND SEM</h3>
            <ul>
              <li>Strategy and execution</li>
              <li>Detailed monthly progress reports</li>
              <li>Competitor analysis</li>
              <li>Website audit</li>
              <li>Marketing campaign management</li>
              <li>Content strategy</li>
              <li>Local SEO audit</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  )
}
export default KitcoReach
