// import type { FC } from 'react'
import { FC } from 'react'
import FilterItem from '~/src/components/DataTable/Filter/FilterItem'

/**
 * Filter component for the data table
 *
 * @param {(filter: string) => void} onFilterChange - Function to call when a filter is selected
 * @param {string} activeFilter - The currently active filter
 * @param {string[]} filters - The list of filters to display
 * @param {boolean} [uppercase=true] - Whether to display the filter names in uppercase
 * @param {string} [className] - Additional class names for the container
 * @param {boolean} [disabled=false] - Whether the filter is disabled
 */
interface DataTableFilterProps {
  onFilterChange: (filter: string) => void
  activeFilter: string
  filters: string[]
  uppercase?: boolean
  className?: string
  disabled?: boolean
}

/**
 * Filter component for the data table
 *
 * @param onFilterChange
 * @param activeFilter
 * @param filters
 * @param uppercase
 * @constructor
 */
const DataTableFilter: FC<DataTableFilterProps> = ({
  onFilterChange,
  activeFilter,
  filters,
  uppercase = true,
  className,
  disabled = false,
}: DataTableFilterProps) => {
  return (
    <div className="inline-flex w-full items-end justify-start self-stretch border-b border-slate-200 px-2 sm:px-0">
      <div className="select-none flex overflow-x-auto flex-nowrap p-0 scrollbar-hide">
        <FilterItem
          className="px-2"
          name="ALL"
          onSelect={() => onFilterChange('ALL')}
          selected={activeFilter === 'ALL'}
        />
        {filters.map((filter) => (
          <FilterItem
            className="px-2"
            key={filter}
            name={uppercase ? filter.toUpperCase() : filter}
            onSelect={() => onFilterChange(filter)}
            selected={activeFilter.toUpperCase() === filter.toUpperCase()}
          />
        ))}
      </div>
    </div>
  )
}

export default DataTableFilter
