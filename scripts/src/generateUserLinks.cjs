const fs = require('fs')
const csv = require('csv-parser')
const { parse } = require('json2csv')
const path = require('path')

// Define the input and output file paths
const inputFile = path.join(__dirname, 'users.csv')
const outputFile = path.join(__dirname, 'users_with_links.csv')

// Function to generate base64 encoded string from user data
const generateBase64 = (userData) => {
  const jsonString = JSON.stringify(userData)
  return Buffer.from(jsonString).toString('base64')
}

// Function to generate user links and add them to the CSV
const generateUserLinks = (inputFile, outputFile) => {
  const users = []
  const baseUrl = `${process.env.NEXT_PUBLIC_URL}/discourse/recover/`

  // Read the CSV file
  fs.createReadStream(inputFile)
    .pipe(csv())
    .on('data', (row) => {
      const { username, name, email } = row

      // Check if the required fields are present
      if (username && name && email) {
        // Create user data object
        const userData = { username, name, email }
        // Generate base64 encoded string
        const base64Data = generateBase64(userData)
        // Create the user link
        // Add the link to the current row
        row.link = `${baseUrl}${base64Data}`
      } else {
        // Add an empty link if any required field is missing
        row.link = ''
      }
      // Add the row to the users array
      users.push(row)
    })
    .on('end', () => {
      // Get CSV fields
      const csvFields = Object.keys(users[0])
      // Convert users array to CSV string
      const csvString = parse(users, { fields: csvFields })

      // Write the updated CSV string to the output file
      fs.writeFile(outputFile, csvString, (err) => {
        if (err) {
          console.error('Error writing to output file:', err)
        } else {
          console.log('User links have been generated and saved to', outputFile)
        }
      })
    })
}

// Call the function to generate user links
generateUserLinks(inputFile, outputFile)
