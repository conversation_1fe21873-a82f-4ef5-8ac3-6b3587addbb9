import { type FC, useCallback } from 'react'
import type { ReportersQuery } from '~/src/generated'
import { news } from '~/src/lib/news-factory.lib'
import kitcoQuery from '~/src/services/database/kitcoQuery'
import { ReporterOrContributorItem } from '../ReporterOrContributorItem/ReporterOrContributorItem'
import styles from './ContributorsCell.module.scss'

const ReportersCell: FC = () => {
  const { data } = kitcoQuery(
    news.reporters({
      options: {
        enabled: true,
        select: useCallback((data: ReportersQuery) => {
          return {
            ...data,
            reporters: [...data.reporters.filter((x) => x.hidden === false)],
          }
        }, []),
      },
    }),
  )

  return (
    <div className={styles.contributorsSidebar}>
      <div>
        <h2>Reporters</h2>
        <ul>
          {data?.reporters?.map((reporter) => (
            <ReporterOrContributorItem item={reporter} key={reporter.id} />
          ))}
        </ul>
      </div>
    </div>
  )
}

export default ReportersCell
