// src/hooks/useMorningFix.ts
import { XMLParser } from 'fast-xml-parser'
import { useEffect, useState } from 'react'

import { useHubTimeInfo } from '~/src/hooks/MorningFix/useHubTimeInfo'

// map Kitco symbols → your Hub.prices keys
const SYMBOL_TO_METAL: Record<string, keyof Hub['prices']> = {
  AU: 'gold',
  AG: 'silver',
  PT: 'platinum',
  PD: 'palladium',
}

// map City IDs → your Hub metadata (API logical mapping)
const CITY_META: Record<string, { key: string; label: string; tz: string }> = {
  '0': { key: 'ny', label: 'NEW YORK', tz: 'America/New_York' },
  '2': { key: 'london', label: 'LONDON', tz: 'Europe/London' },
  '7': { key: 'hk', label: 'HONG KONG', tz: 'Asia/Hong_Kong' },
  '12': { key: 'mumbai', label: '<PERSON>UMBA<PERSON>', tz: 'Asia/Kolkata' },
}

export interface Hub {
  key: string
  label: string
  timestamp: string
  prices: {
    gold: number
    silver: number
    platinum: number
    palladium: number
  }
}

/**
 * Fetches KITCO Morning Fix XML and returns an array of Hubs with gold/silver/platinum/palladium prices.
 */
export function useMorningFix(
  date: string,
  currency: 'USD' | 'EUR',
  latest = 1,
) {
  const [hubs, setHubs] = useState<Hub[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const abbrMap = useHubTimeInfo()

  useEffect(() => {
    setLoading(true)
    setError(null)

    // ← renamed env-vars here:
    const baseUrl = process.env.NEXT_PUBLIC_KMFIX_BASE_URL!
    const apiKey = process.env.NEXT_PUBLIC_KMFIX_API_KEY!

    // build the full URL
    const url = new URL(baseUrl)
    url.searchParams.set('symbol', 'AU,AG,PT,PD')
    url.searchParams.set('date', date)
    url.searchParams.set('currency', currency)
    url.searchParams.set('type', 'xml')
    url.searchParams.set('latest', String(latest))
    url.searchParams.set('apikey', apiKey)

    // console.log('Fetching Kitco feed from:', url.toString())

    // Reset error state before new fetch
    setError(null)

    fetch(url.toString())
      .then((r) => {
        if (!r.ok) throw new Error(`HTTP ${r.status}`)
        return r.text()
      })
      .then((xmlStr) => {
        const parser = new XMLParser({ ignoreAttributes: false })
        const doc = parser.parse(xmlStr)

        // 1) guard against missing <KFValues> or root <KFValue>
        let entries: any[] = []
        if (doc.KFValues?.KFValue) {
          entries = Array.isArray(doc.KFValues.KFValue)
            ? doc.KFValues.KFValue
            : [doc.KFValues.KFValue]
        } else if (doc.KFValue) {
          entries = Array.isArray(doc.KFValue) ? doc.KFValue : [doc.KFValue]
        } else {
          throw new Error('Unexpected XML format: no <KFValues> or <KFValue>')
        }

        // 2) group by City ID
        const grouped: Record<string, { ts: string; prices: any }> = {}
        entries.forEach((e) => {
          const cityId = e.City
          if (!grouped[cityId])
            grouped[cityId] = { ts: e.Timestamp, prices: {} }
          const metal = SYMBOL_TO_METAL[e.Symbol]
          grouped[cityId].prices[metal] = parseFloat(e.Value)
          grouped[cityId].ts = e.Timestamp
        })

        // 3) map into Hub[]
        const out: Hub[] = Object.entries(grouped).map(
          ([cityId, { ts, prices }]) => {
            const meta = CITY_META[cityId]!
            // format local time then append the hub’s fetched abbreviation
            const baseTime = new Date(ts).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              timeZone: meta.tz,
            })
            const abbr = abbrMap[meta.key] || ''
            const time = abbr ? `${baseTime} ${abbr}` : baseTime
            return {
              key: meta.key,
              label: meta.label,
              timestamp: time,
              prices,
            }
          },
        )

        setHubs(out)
      })
      .catch((e) => {
        console.error(e)
        setError((e as Error).message)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [date, currency, latest])

  return { hubs, loading, error }
}
