import dotenv from 'dotenv'
import admin from 'firebase-admin'
import { createRequire } from 'node:module'

/**
 * This script updates a specific user's email in Firebase.
 * 
 * Usage:
 * `yarn update-users-email --uid=<user-id> --email=<new-email> [--env=<environment>]`
 * 
 * Parameters:
 * - uid: The Firebase user ID
 * - email: The new email address
 * - env: The environment (prod or dev, defaults to dev)
 * 
 * Prerequisites:
 * - Firebase Admin SDK must be installed
 * - Service account credentials must be available
 * - The service account must have permissions to update users
 */

// Parse command line arguments
const args = process.argv.slice(2)
const params: { [key: string]: string } = {}

args.forEach(arg => {
  const [key, value] = arg.replace(/^--/, '').split('=')
  if (key && value) {
    params[key] = value
  }
})

// Validate required parameters
const uid = params.uid
const newEmail = params.email
const env = params.env || 'dev'

if (!uid) {
  console.error('Error: User ID (--uid) is required')
  process.exit(1)
}

if (!newEmail) {
  console.error('Error: New email address (--email) is required')
  process.exit(1)
}

// Validate environment
if (env !== 'dev' && env !== 'prod') {
  console.error('Error: Environment (--env) must be either "dev" or "prod"')
  process.exit(1)
}

// Base path for environment configuration files and service account JSON files
const envBasePath = '../.circleci/environments'
const basePath = '../../.circleci/environments'

// Set the debug flag to true to enable logging in the dotenv module
const debug = false

// Load appropriate environment variables
dotenv.config({
  path: `${envBasePath}/${env}/.env.${env === 'prod' ? 'production' : 'development'}`,
  debug: debug,
  override: true,
})

const databaseURL = process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL

// Initialize Firebase Admin SDK
const require = createRequire(import.meta.url)
const serviceAccount = require(`${basePath}/${env}/${env === 'prod' ? 'firebase-admin.json' : 'firebase-admin.json'}`)

// Initialize the Firebase app
try {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: databaseURL,
  })
} catch (error) {
  // App might already be initialized in some cases
  console.log('Firebase app initialization:', error instanceof Error ? error.message : 'Unknown error')
}

/**
 * Updates a user's email in Firebase
 * @param uid - The Firebase user ID
 * @param newEmail - The new email address
 */
async function updateUserEmail(uid: string, newEmail: string): Promise<void> {
  try {
    await admin.auth().updateUser(uid, {
      email: newEmail,
      emailVerified: false, // Reset email verification status
    })
    
    console.log(`✅ Successfully updated user email`)
    console.log(`User ID: ${uid}`)
    console.log(`New email: ${newEmail}`)
    console.log(`Environment: ${env}`)
    console.log(`Note: The user will need to verify their new email address`)
  } catch (error) {
    console.error('❌ Error updating user email:')
    console.error(error instanceof Error ? error.message : 'Unknown error')
    throw error
  }
}

// Execute the update
console.log(`Updating user email in Firebase (${env} environment)...`)
console.log(`User ID: ${uid}`)
console.log(`New email: ${newEmail}`)
console.log('')

updateUserEmail(uid, newEmail)
  .then(() => {
    console.log('')
    console.log('Operation completed successfully.')
  })
  .catch(() => {
    console.log('')
    console.error('Operation failed. See error details above.')
    process.exit(1)
  })