/**
 * Record an event in Google Tag Manager
 *
 * @param data
 */
function doAction(data): void {
  // If the code is not running on the client side, return
  // @ts-ignore - window.dataLayer is defined in the Google Tag Manager script
  if (typeof window === 'undefined' || !window.dataLayer) {
    return
  }

  try {
    // Record the page view in Google Tag Manager
    // @ts-ignore - window.dataLayer is defined in the Google Tag Manager script
    window.dataLayer.push(data)
  } catch (err) {
    console.error('GTM - Failed to send data:', err)
  }
}

/**
 * Record an event in Google Tag Manager
 *
 * @param pathName - The path of the page, excluding the domain (e.g., /about)
 * @param title - The title of the page
 * @param location - The full URL of the page (e.g., https://www.kitco.com/about)
 * @param author- the author of the content (if an article)
 * @param event
 */
export default function gtmEvent(
  pathName: string,
  title: string,
  location: string,
  author: string,
  event: string,
): void {
  // Send the event to Google Tag Manager
  doAction({
    event: event,
    page_path: pathName ?? window?.location?.pathname,
    page_title: title ?? document?.title,
    page_location: location ?? window?.location?.href,
    author: author ?? 'Unknown',
  })
}

/**
 * Record an A/B test event in Google Tag Manager
 */
export function recordABTest(
  event_name: string,
  experiment_name: string,
  variant: string,
  variant_description: string = null,
  pathName: string = null,
  title: string = null,
  location: string = null,
): void {
  doAction({
    event: event_name,
    experiment_name: experiment_name,
    page_path: pathName ?? window?.location?.pathname,
    page_title: title ?? document?.title,
    page_location: location ?? window?.location?.href,
    variant,
    variant_description,
  })
}
