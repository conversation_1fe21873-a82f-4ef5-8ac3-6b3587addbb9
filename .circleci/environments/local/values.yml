nextjs:
  image:
    repository: "node"
    tag: "current-alpine"
  nodeEnv: "development"
  ingress:
    enabled: true
    hostname: "frontend.local.favish.com"
    redirect_www: true
    annotations:
      nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
    service:
      name: varnish
      port: 80
    tls:
      enabled: false
  livenessProbe:
    enabled: false
  volume:
    enabled: true
    hostPath:
    command:
      - /bin/sh
      - -c
    commandArgs:
      - yarn install && yarn dev
    autoscaling:
      enabled: true
      minReplicas: 1
      maxReplicas: 10
      targets:
        cpuUtilizationPercentage: 70
        memoryUtilizationPercentage: 70

varnish:
  replicas: 1
  image:
    repository: us.gcr.io/kitco-224816/favish/varnish-frontend
    tag: 1.0.1
  backend:
    host: kitco-frontend-local-nextjs
  resources:
    requests:
      cpu: "50m"
      memory: "500M"
  # Extra Varnish Configuration for templating
  extra:
    # Drupal Host
    drupalHost: "kitco-cms-drupal.local.favish.com"

    # Enable/Disable Basic Auth
    enableBasicAuth: false

    # Main Hostname
    hostname: "www.frontend.local.favish.com"

    # Enable/Disable Redirect to www
    redirect_www: true
    # Domain without www for comparison
    redirect_www_hostname: "frontend.local.favish.com"

    # Sitemap Bucket for Google Storage
    sitemapBucket: "kitco-cms-local.storage.googleapis.com"

rss:
  resources:
    requests:
      cpu: "0.25"
      memory: "0.5G"
  ingressHost: "kitco-news.local.favish.com"
  ingressAnnotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    certmanager.k8s.io/cluster-issuer: "letsencrypt-prod"
  targetHost: "cms.prod.kitco.com"
  tls:
    enabled: false
    host: "kitco-news.local.favish.com"
