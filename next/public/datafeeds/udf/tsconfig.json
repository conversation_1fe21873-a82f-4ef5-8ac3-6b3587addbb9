{"compilerOptions": {"allowSyntheticDefaultImports": true, "importHelpers": true, "lib": ["dom", "es2015.promise", "es2015.iterable", "es2015.symbol.wellknown", "es5"], "module": "es6", "moduleResolution": "node", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": true, "outDir": "./lib/", "rootDir": "src", "sourceMap": false, "strict": true, "target": "es5", "types": []}, "include": ["./src/**/*.ts"]}