import clsx from 'clsx'
import Link from 'next/link'
import type { FC } from 'react'
import { CategoryLink } from '~/src/components-news/ArticleTeasers/CategoryLink'
import { DateStamp } from '~/src/components-news/ArticleTeasers/DateStamp'
import { ImageMS } from '~/src/components/ImageMS/ImageMS.component'
import { Spacer } from '~/src/components/spacer/spacer.component'
import type { UTMParams } from '~/src/hooks/Global/UTMGenerator'
import type { ArticlesUnion } from '~/src/types/types'
import cs from '~/src/utils/cs'

/**
 * Teaser Card Props
 */
export interface TeaserCardProps {
  node: ArticlesUnion
  size: 'sm' | 'md' | 'lg' | 'xl' | 'xxl'
  hideCategory?: boolean
  hideSummary?: boolean
  sizeImg?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl'
  lineClampTitle?: string
  classTitle?: string
  classWrapper?: string
  utmParams?: UTMParams
}

/**
 * Teaser Card Component
 *
 * @param node
 * @param size
 * @param hideCategory
 * @param hideSummary
 * @param sizeImg
 * @param lineClampTitle
 * @param classWrapper
 * @constructor
 */
export const TeaserCard: FC<TeaserCardProps> = ({
  node,
  size,
  hideCategory = false,
  hideSummary = false,
  sizeImg = 'md',
  lineClampTitle = '',
  classWrapper = '',
}) => {
  const sizeCSS = {
    sm: 'text-[16px] leading-[130%]',
    md: 'text-[20px] leading-[130%]',
    lg: 'text-[24px] leading-[130%]',
    xl: 'text-[24px] leading-[130%]',
  }

  const summaryCSS = {
    sm: '',
    md: '',
    lg: 'text-[16px] leading-[135%]',
    xl: '',
  }

  const sizeImgCSS = {
    sm: '',
    md: 'max-w-[200px] max-h-[130px] object-cover mb-2.5',
    lg: '',
    xl: '',
  }

  const imgDimensions = {
    sm: { width: 304, height: 170 },
    md: { width: 304, height: 170 },
    lg: { width: 304, height: 170 },
    xl: { width: 1203, height: 676 },
  }

  return (
    <div className={cs(['w-full', classWrapper])}>
      <Link className="aspect-video" href={node?.urlAlias ?? '/'}>
        <ImageMS
          src={
            node?.teaserImage?.detail?.default?.srcset ??
            node?.image?.detail?.default?.srcset ??
            node?.legacyThumbnailImageUrl
          }
          hasLegacyThumbnailImageUrl={!!node?.legacyThumbnailImageUrl}
          alt={`${node?.title} teaser image`}
          priority={true}
          width={imgDimensions[size].width}
          height={imgDimensions[size].height}
          service="icms"
          className={clsx(
            sizeImgCSS[sizeImg],
            'aspect-video rounded-lg object-cover',
            'w-full',
          )}
        />
      </Link>
      <div className="h-2" />
      {!hideCategory ? (
        <>
          <CategoryLink
            urlAlias={node?.category?.urlAlias}
            text={node?.category?.name}
          />
          <div className="h-2 bg-transparent" />
        </>
      ) : (
        <Spacer className="h-2" />
      )}
      <Link href={node?.urlAlias ?? '/'}>
        <>
          <h3 className={cs([sizeCSS[size], lineClampTitle])}>
            {node?.teaserHeadline ?? node?.title}
          </h3>
          {hideSummary ? null : (
            <div className="pt-2">
              <div
                className={cs(['summary', summaryCSS[size]])}
                dangerouslySetInnerHTML={{
                  __html:
                    node?.teaserSnippet ?? node?.bodyWithEmbeddedMedia?.value,
                }}
              />
            </div>
          )}
          <Spacer className="h-2" />
          <DateStamp stamp={node?.createdAt} />
        </>
      </Link>
    </div>
  )
}
