import { List, Root, Viewport } from '@radix-ui/react-navigation-menu'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import AboutItem from './AboutItem/AboutItem'
import BuySellItem from './BuySellItem/BuySellItem'
import ChartsItem from './ChartsItem/ChartsItem'
import JewelerItem from './JewelerItem/JewelerItem'
import MarketsItem from './MarketsItem/MarketsItem'
import MiningItem from './MiningItem/MiningItem'
import { NavLogo } from './NavLogo'
import styles from './NavVer2.module.scss'

import React, { useEffect, useRef, useState } from 'react'
import { IoCloseOutline, IoMenu } from 'react-icons/io5'
import LoginMenuIcon from '~/src/components/Auth/Menu/LoginMenuIcon'

import Link from 'next/link'
import BaseMetalsItem from '~/src/components/NavVer2/BaseMetalsItem/BaseMetalsItem'
import useScreenSize from '~/src/utils/useScreenSize'
import { FavoritesBar } from '../FavoritesBar/FavoritesBar'
import NewsItem from './NewsItem/NewsItem'
import QuotesItem from './QuotesItem/QuotesItem'
import Search from './Search/Search'

/**
 * Nav component props
 *
 * @interface NavProps
 * @property {any} headerRef - reference to the header
 * @property {function} onMenuMobile - function to show/hide the mobile nav
 * @property {function} onShowHide - function to show/hide the nav
 */
interface NavProps {
  headerRef?: any
  onMenuMobile?: (val: boolean) => void
  onShowHide?: (val: boolean) => void
}

const Nav: React.FC<NavProps> = ({
  headerRef,
  onMenuMobile,
  onShowHide,
}: NavProps) => {
  const router = useRouter()
  const [mobileNavActivate, setMobileNavActivate] = useState(false)
  const { isDesktopForNavBar } = useScreenSize()

  const [offset, setOffset] = React.useState<any>()
  const [list, setList] = React.useState<any>()
  const [value, setValue] = React.useState<any>()
  let lastScrollTop = 0
  const buttonRef = useRef(null)
  const isScrollRef = useRef(false)
  const [itemClick, setItemClick] = useState(false)
  const heightHeader = Number(headerRef?.heightHeader)
  const heightAds = Number(headerRef?.heightAds)

  useEffect(() => {
    const handleButtonClick = () => {
      setItemClick(true)
    }
    window.addEventListener('buttonClicked', handleButtonClick)
    return () => {
      window.removeEventListener('buttonClicked', handleButtonClick)
    }
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      if (
        lastScrollTop > scrollTop &&
        lastScrollTop <= heightAds &&
        scrollTop <= heightHeader
      ) {
        isScrollRef.current = false
        lastScrollTop = scrollTop <= 0 ? 0 : scrollTop
        if (typeof onShowHide === 'function') {
          onShowHide(null)
        }
        return
      }
      if (
        (!isDesktopForNavBar &&
          buttonRef.current === document.activeElement &&
          mobileNavActivate) ||
        itemClick
      ) {
        setItemClick(false)
        return
      }

      if (scrollTop > lastScrollTop) {
        isScrollRef.current = true
        if (typeof onShowHide === 'function') {
          onShowHide(true)
        }
      } else {
        isScrollRef.current = false
        if (typeof onShowHide === 'function') {
          onShowHide(false)
        }
      }

      lastScrollTop = scrollTop <= 0 ? 0 : scrollTop
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [lastScrollTop, itemClick, mobileNavActivate, heightHeader])

  useEffect(() => {
    if (mobileNavActivate) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }

    // Check if the onMenuMobile function is a function before calling it
    if (typeof onMenuMobile === 'function') {
      onMenuMobile(mobileNavActivate)
    }
  }, [mobileNavActivate])

  const onNodeUpdate = (trigger, itemValue) => {
    if (isScrollRef.current) {
      setOffset(undefined)
      return
    }
    if (trigger && list && value === itemValue) {
      const listWidth = list?.offsetWidth
      const listCenter = listWidth / 2

      const triggerOffsetRight =
        listWidth - trigger.offsetLeft - trigger.offsetWidth

      setOffset(Math.round(listCenter - triggerOffsetRight))
    } else if (value === '') {
      setOffset(undefined)
    }
    return trigger
  }

  const menuTitle = [
    'BUY/SELL GOLD & SILVER',
    'Precious Metals',
    'Cryptos',
    'Base Metals',
    'Markets',
    'Mining',
    'News',
    'About',
  ]

  const menuItems = [
    BuySellItem,
    QuotesItem,
    ChartsItem,
    BaseMetalsItem,
    MarketsItem,
    MiningItem,
    NewsItem,
    AboutItem,
  ]

  return (
    <>
      <Root
        id="nav2"
        value={value}
        onValueChange={setValue}
        orientation={!isDesktopForNavBar ? 'vertical' : 'horizontal'}
        className={clsx(
          'relative w-full',
          'px-2',
          mobileNavActivate ? 'h-auto' : 'h-14',
          router.pathname.includes('/news/video')
            ? 'bg-[#0F181D]'
            : 'bg-ktc-black',
        )}
      >
        <div
          className={clsx(
            'mx-auto h-full max-w-[1240px] px-2',
            'relative flex flex-wrap overflow-hidden',
            'desktop:flex-nowrap desktop:justify-between',
          )}
        >
          <NavLogo />
          <div
            className={clsx(
              mobileNavActivate ? 'overflow-x-hidden mb-5 order-last' : '',
              'flex items-start justify-start desktop:items-center desktop:justify-between left-0 mx-auto',
            )}
          >
            <List
              ref={setList}
              className={clsx(
                'left-0 mx-auto w-full',
                'flex-col',
                'gap-2 desktop:order-2 desktop:w-auto desktop:flex-row desktop:gap-0',
                'mb-5 desktop:mb-0',
                mobileNavActivate ? 'flex' : 'hidden desktop:flex',
              )}
            >
              {menuTitle.map((item, index) => {
                const MenuItem = menuItems[index]
                return (
                  <MenuItem
                    key={index}
                    value={item}
                    onNodeUpdate={onNodeUpdate}
                  />
                )
              })}

              <div className="block md:hidden border-t border-gray-100/50 pt-4 mt-3">
                <div className="text-white pb-3">
                  <Link
                    href="https://forum.kitco.com/categories?utm_source=kitco_website&utm_medium=navbar&utm_campaign=forum_navigation"
                    target="_blank"
                    className="whitespace-nowrap text-sm leading-5 text-white hover:underline font-bold"
                  >
                    {' '}
                    Kitco Forum{' '}
                    <span className="inline-block rounded-md bg-ktc-blue ml-2 px-1 py-0.5 text-xs font-medium text-white hover:bg-kitco-black">
                      NEW
                    </span>
                  </Link>
                </div>
                <JewelerItem
                  onNodeUpdate={onNodeUpdate}
                  key="jeweler-08"
                  value="Jeweler Resources"
                />
              </div>
            </List>
          </div>
          <List
            className={clsx(
              'h-14 max-h-full w-full',
              'z-[2147483647] flex justify-end',
              'order-2 self-start',
              'desktop:order-last',
            )}
          >
            <Search
              mobileNavActivate={mobileNavActivate}
              toggleMobileNav={() => setMobileNavActivate(!mobileNavActivate)}
            />
            <li className={clsx(styles.hamburgerContainer)}>
              <button
                type="button"
                ref={buttonRef}
                onClick={() => setMobileNavActivate(!mobileNavActivate)}
              >
                {!mobileNavActivate ? (
                  <IoMenu size="28px" color="white" />
                ) : (
                  <IoCloseOutline size="28px" color="white" />
                )}
              </button>
            </li>
            {process.env.NEXT_PUBLIC_LOGIN_MENU === 'true' && (
              <li className="flex items-center bg-transparent px-4">
                <LoginMenuIcon />
              </li>
            )}
          </List>
        </div>
        {isDesktopForNavBar && (
          <div
            className={clsx(
              'perspective-2000 absolute left-0 z-[20000000] flex w-full justify-center',
              'mx-auto',
            )}
          >
            <Viewport
              style={{
                paddingTop: '4px',
                // Avoid transitioning from initial position when first opening
                display: !offset ? 'none' : undefined,
                transform: `translateX(${offset}px)`,
                top: '100%',
                transition: 'all 0.5s ease',
              }}
            />
          </div>
        )}
      </Root>
      {/* don't show the favorites bar on video news paths */}
      {!router.pathname.includes('/video') ? (
        <div id="nav2-favorites" className="hidden md:block">
          <FavoritesBar />{' '}
        </div>
      ) : null}
    </>
  )
}

export default Nav
