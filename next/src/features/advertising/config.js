import { banner1 } from '~/src/features/advertising/units/banner1'
import { banner2 } from '~/src/features/advertising/units/banner2'
import { banner3 } from '~/src/features/advertising/units/banner3'
import { banner4 } from '~/src/features/advertising/units/banner4'
import { chartFooter } from '~/src/features/advertising/units/chartFooter'
import { footer } from '~/src/features/advertising/units/footer'
import { inContent1 } from '~/src/features/advertising/units/inContent1'
import { inContent2 } from '~/src/features/advertising/units/inContent2'
import { inContent3 } from '~/src/features/advertising/units/inContent3'
import { inContent4 } from '~/src/features/advertising/units/inContent4'
import { leaderboard } from '~/src/features/advertising/units/leaderboard'
import { leftRail1 } from '~/src/features/advertising/units/leftRail1'
import { leftRail1News } from '~/src/features/advertising/units/leftRail1News'
import { leftRail2 } from '~/src/features/advertising/units/leftRail2'
import { leftRail2News } from '~/src/features/advertising/units/leftRail2News'
import { leftRail3News } from '~/src/features/advertising/units/leftRail3News'
import { leftRail4News } from '~/src/features/advertising/units/leftRail4News'
import { leftRail5News } from '~/src/features/advertising/units/leftRail5News'
import { oop } from '~/src/features/advertising/units/oop'
import { rightRail1 } from '~/src/features/advertising/units/rightRail1'
import { rightRail1AMQ } from '~/src/features/advertising/units/rightRail1AMQ'
import { rightRail1HP } from '~/src/features/advertising/units/rightRail1HP'
import { rightRail2 } from '~/src/features/advertising/units/rightRail2'
import { rightRail2HP } from '~/src/features/advertising/units/rightRail2HP'
import { rightRail3 } from '~/src/features/advertising/units/rightRail3'
import { rightRail3HP } from '~/src/features/advertising/units/rightRail3HP'
import { rightRail4 } from '~/src/features/advertising/units/rightRail4'
import { rightRailArticle2_1 } from '~/src/features/advertising/units/rightRailArticle2_1'
import { rightRailArticle2_2 } from '~/src/features/advertising/units/rightRailArticle2_2'
import { rightRailArticle3_1 } from '~/src/features/advertising/units/rightRailArticle3_1'
import { rightRailArticle3_2 } from '~/src/features/advertising/units/rightRailArticle3_2'
import { rightRailArticle4_1 } from '~/src/features/advertising/units/rightRailArticle4_1'
import { rightRailArticle4_2 } from '~/src/features/advertising/units/rightRailArticle4_2'
import { rightRailArticle5_1 } from '~/src/features/advertising/units/rightRailArticle5_1'
import { rightRailArticle5_2 } from '~/src/features/advertising/units/rightRailArticle5_2'
import { square1 } from '~/src/features/advertising/units/square1'
import { videoBanner } from '~/src/features/advertising/units/videoBanner'
import {
  checkCryptoPage,
  checkDevSite,
  checkNewsArticlePage,
  checkNewsPage,
  checkOpinionsPage,
  checkVideoNewsPage,
  getPageUrl,
  grabUrl,
} from '~/src/features/advertising/utils/targetting'
import { sizeDefaults } from './units/demandConfig'

const viewports = {
  mobile: [0, 0],
  tablet: [768, 60],
  large: [1024, 250],
  desktop: [1270, 250],
  desktopLarge: [1320, 250],
}
let pdKeys = {}

if (typeof window !== 'undefined') {
  pdKeys = {
    8: encodeURIComponent(window.location.href),
    9: encodeURIComponent(window.location.host),
    12: encodeURIComponent(window.navigator.userAgent),
  }
}

// Currently do not include interstitial on mobile devices below tablet.
function includeInterstitial() {
  if (typeof window !== 'undefined') {
    if (window.innerWidth < 768) {
      return {}
    } else {
      return {
        path: '/21841313772,22554256/kitco/interstitial',
      }
    }
  } else {
    return {}
  }
}

const pdRaw = Object.keys(pdKeys)
  .map((key) => key + '=' + pdKeys[key])
  .join('&')
const pdString = btoa(pdRaw)

export const adConfig = {
  usePrebid: true,
  useAPS: true,
  prebid: {
    bidderTimeout: 1000,
    bidderSequence: 'random',
    enableSendAllBids: false, // This is to prevent issues with how many bidders we have. Only so much can be sent to GAM. Only send winning bid.
    enableTIDs: true,
    consentManagement: {
      gpp: {
        cmpApi: 'iab',
        timeout: 8000,
      },
      gdpr: {
        cmpApi: 'iab',
        timeout: 8000,
        defaultGdprScope: true,
      },
      usp: {
        cmpApi: 'iab',
        timeout: 100,
      },
    },
    allowActivities: {
      accessDevice: {
        default: true,
      },
    },
    bidderSettings: {
      standard: {
        storageAllowed: true,
        userSync: {
          filterSettings: {
            iframe: {
              bidders: '*',
              filter: 'include',
            },
          },
        },
      },
      eplanning: {
        storageAllowed: true,
      },
    },
    schain: {
      validation: 'strict',
      config: {
        ver: '1.0',
        complete: 1,
        nodes: [
          {
            asi: 'favish.com',
            sid: '2',
            hp: 1,
          },
        ],
      },
    },
    userSync: {
      userIds: [
        {
          name: 'sharedId',
          storage: {
            type: 'cookie',
            name: '_pubcid',
            expires: 365,
          },
        },
        {
          name: 'unifiedId',
          params: {
            partner: 'cin08sv',
          },
          storage: {
            type: 'cookie',
            name: '_unifiedId',
            expires: 60,
          },
        },
        {
          name: 'id5Id',
          params: {
            partner: 1076,
            pd: pdString,
            externalModuleUrl:
              'https://cdn.id5-sync.com/api/1.0/id5PrebidModule.js',
          },
          storage: {
            type: 'html5',
            name: 'id5id',
            expires: 90,
            refreshInSeconds: 8 * 3600,
          },
        },
      ],
      auctionDelay: 50,
    },
  },
  aps: {
    adServer: 'googletag',
    simplerGPT: true,
    pubID: '940db4ee-28cf-4307-9b65-76d8ba12e927',
    bidTimeout: 1000,
    schain: {
      complete: 1,
      ver: '1.0',
      nodes: [
        {
          asi: 'favish.com',
          sid: '2',
          hp: 1,
        },
      ],
    },
  },
  enableLazyLoad: {
    marginPercent: 25,
    mobileScaling: 1.5,
  },
  sizeMappings: {
    leaderboard: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [320, 50],
          [320, 100],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[728, 90]],
      },
      {
        viewPortSize: viewports.large,
        sizes: [
          [970, 250],
          [728, 90],
        ],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [
          [970, 250],
          [728, 90],
        ],
      },
    ],
    largeBanner: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [336, 280],
          [300, 250],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[728, 90]],
      },
      {
        viewPortSize: viewports.large,
        sizes: [
          [970, 250],
          [728, 90],
        ],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [
          [970, 250],
          [728, 90],
        ],
      },
    ],
    leftRail: [
      {
        viewPortSize: viewports.mobile,
        sizes: [],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[160, 600]],
      },
    ],
    inContent: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [300, 250],
          [336, 280],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [
          [300, 250],
          [336, 280],
        ],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [],
      },
    ],
    leftRailNews: [
      {
        viewPortSize: viewports.mobile,
        sizes: [],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[160, 600]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[160, 600]],
      },
    ],
    rightRail: [
      {
        viewPortSize: viewports.mobile,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[300, 250]],
      },
    ],
    rightRailHP: [
      {
        viewPortSize: viewports.mobile,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [
          [300, 250],
          [336, 280],
        ],
      },
    ],
    rightRailMd: [
      {
        viewPortSize: viewports.mobile,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [
          [300, 250],
          [300, 600],
        ],
      },
    ],
    rightRailMdHP: [
      {
        viewPortSize: viewports.mobile,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [
          [300, 250],
          [300, 600],
        ],
      },
      {
        viewPortSize: viewports.desktopLarge,
        sizes: [[300, 250], [336, 280], [300, 600], 'fluid'],
      },
    ],
    banner: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [336, 280],
          [300, 250],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[728, 90]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[728, 90]],
      },
    ],
    footer: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [320, 100],
          [320, 50],
          [300, 50],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[728, 90]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [],
      },
    ],
    chartFooter: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [320, 100],
          [320, 50],
          [300, 50],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [],
      },
    ],
    square: [
      {
        viewPortSize: viewports.mobile,
        sizes: [],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [
          [125, 125],
          [180, 150],
        ],
      },
    ],
    interstitial: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [300, 250],
          [320, 480],
          [336, 280],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [
          [300, 250],
          [320, 480],
          [336, 280],
        ],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [],
      },
    ],
    billboard: [
      {
        viewPortSize: viewports.mobile,
        sizes: ['fluid', [300, 250], [300, 600]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: ['fluid', [300, 250]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: ['fluid', [728, 90]],
      },
    ],
    pressRelease: [
      {
        viewPortSize: viewports.mobile,
        sizes: ['fluid', [320, 100], [320, 50]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: ['fluid', [728, 90]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: ['fluid', [728, 90]],
      },
    ],
    pressReleaseMin: [
      {
        viewPortSize: viewports.mobile,
        sizes: ['fluid', [320, 100], [320, 50]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: ['fluid', [320, 100], [320, 50]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: ['fluid', [320, 100], [320, 50]],
      },
    ],
    video: [
      {
        viewPortSize: viewports.mobile,
        sizes: [
          [1, 1],
          [300, 250],
          [336, 280],
        ],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [
          [1, 1],
          [300, 250],
          [336, 280],
        ],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[1, 1]],
      },
    ],
    vidBanner: [
      {
        viewPortSize: viewports.mobile,
        sizes: [[320, 100]],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[320, 100]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[320, 100]],
      },
    ],
    rightRailAMQ: [
      {
        viewPortSize: viewports.mobile,
        sizes: [],
      },
      {
        viewPortSize: viewports.tablet,
        sizes: [[300, 250]],
      },
      {
        viewPortSize: viewports.desktop,
        sizes: [[300, 250], [300, 600], 'fluid'],
      },
      {
        viewPortSize: viewports.desktopLarge,
        sizes: [[300, 250], [336, 280], [300, 600], 'fluid'],
      },
    ],
  },
  slots: [
    leaderboard,
    rightRail1,
    rightRail2,
    rightRail3,
    rightRail4,
    leftRail1,
    leftRail2,
    leftRail1News,
    leftRail2News,
    leftRail3News,
    leftRail4News,
    leftRail5News,
    banner1,
    banner2,
    banner3,
    banner4,
    square1,
    footer,
    chartFooter,
    videoBanner,
    rightRail1AMQ,
    rightRailArticle2_1,
    rightRailArticle2_2,
    rightRailArticle3_1,
    rightRailArticle3_2,
    rightRailArticle4_1,
    rightRailArticle4_2,
    rightRailArticle5_1,
    rightRailArticle5_2,
    rightRail1HP,
    rightRail2HP,
    rightRail3HP,
    inContent1,
    inContent2,
    inContent3,
    inContent4,
    oop,
    {
      id: 'news-native',
      path: '/21841313772,22554256/kitco/native_news_1',
      sizes: sizeDefaults.native,
      prebid: [],
    },
    {
      id: 'mining-content-billboard',
      path: '/21841313772,22554256/kitco/mining_content_billboard',
      sizes: sizeDefaults.native,
      sizeMappingName: 'billboard',
      prebid: [],
    },
    {
      id: 'press-release',
      path: '/21841313772,22554256/kitco/press_release',
      sizes: sizeDefaults.pressRelease,
      sizeMappingName: 'pressRelease',
      prebid: [],
    },
    {
      id: 'press-release-min',
      path: '/21841313772,22554256/kitco/press_release',
      sizes: sizeDefaults.pressReleaseMin,
      sizeMappingName: 'pressReleaseMin',
      prebid: [],
    },
  ],
  targeting: {
    kitco_short_url: grabUrl(),
    news_page: checkNewsPage(),
    news_article_page: checkNewsArticlePage(),
    video_news_page: checkVideoNewsPage(),
    kitco_page_path: getPageUrl(),
    crypto_page: checkCryptoPage(),
    kitco_opinions_page: checkOpinionsPage(),
    dev_server: checkDevSite(),
  },
  observers: [],
  interstitialSlot: includeInterstitial(),
}
