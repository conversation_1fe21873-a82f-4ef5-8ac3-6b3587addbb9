(window.webpackJsonp = window.webpackJsonp || []).push([[53], {
  "5VK0": function(e, t, r) {
    e.exports = {
      scrollWrap: "scrollWrap-hMorJ3oa",
      tabsWrap: "tabsWrap-DW4tJckL",
      tabs: "tabs-2ZEU3acm",
      tab: "tab-1l4dFt6c",
      withHover: "withHover-1_-qVdZP",
      headerBottomSeparator: "headerBottomSeparator-1v9qLLAy",
    };
  },
  "5o6O": function(e, t, r) {
    e.exports = {
      tabs: "tabs-1LGqoVz6",
      tab: "tab-1Yr0rq0J",
      noBorder: "noBorder-oc3HwerO",
      disabled: "disabled-s8cEYElA",
      active: "active-37sipdzm",
      defaultCursor: "defaultCursor-Np9BHjTg",
      slider: "slider-1-X4lOmE",
      content: "content-2asssfGq",
    };
  },
  K3s3: function(e, t, r) {
    "use strict";
    r.d(t, "a", function() {
      return i;
    }),
      r.d(t, "b", function() {
        return l;
      }),
      r.d(t, "c", function() {
        return u;
      });
    var n = r("mrSG"), a = r("q1tI"), o = r("TSYQ"), s = r("Eyy1"), c = r("5o6O"), i = c;
    function l(e) {
      var t,
        r = o(
          e.className,
          c.tab,
          ((t = {})[c.active] = e.isActive,
            t[c.disabled] = e.isDisabled,
            t[c.defaultCursor] = !!e.shouldUseDefaultCursor,
            t[c.noBorder] = !!e.noBorder,
            t),
        );
      return a.createElement("div", { className: r, onClick: e.onClick, ref: e.reference }, e.children);
    }
    function u(e) {
      return function(t) {
        function r() {
          var e = null !== t && t.apply(this, arguments) || this;
          return e.activeTab = { current: null }, e;
        }
        return Object(n.__extends)(r, t),
          r.prototype.componentDidUpdate = function() {
            Object(s.ensureNotNull)(this._slider).style.transition = "transform 350ms", this._componentDidUpdate();
          },
          r.prototype.componentDidMount = function() {
            this._componentDidUpdate();
          },
          r.prototype.render = function() {
            var t = this, r = this.props.className, n = this._generateTabs();
            return a.createElement(
              "div",
              { className: o(r, c.tabs), "data-name": this.props["data-name"] },
              n,
              a.createElement(e, {
                reference: function(e) {
                  t._slider = e;
                },
              }),
            );
          },
          r.prototype._generateTabs = function() {
            var e = this;
            return this.activeTab.current = null,
              a.Children.map(this.props.children, function(t) {
                var r = t,
                  n = Boolean(r.props.isActive),
                  o = {
                    reference: function(t) {
                      n && (e.activeTab.current = t), r.props.reference && r.props.reference(t);
                    },
                  };
                return a.cloneElement(r, o);
              });
          },
          r.prototype._componentDidUpdate = function() {
            var e = Object(s.ensureNotNull)(this._slider).style;
            if (this.activeTab.current) {
              var t = this.activeTab.current.offsetWidth, r = this.activeTab.current.offsetLeft;
              e.transform = "translateX(" + r + "px)", e.width = t + "px", e.opacity = "1";
            } else e.opacity = "0";
          },
          r;
      }(a.PureComponent);
    }
    u(function(e) {
      return a.createElement("div", { className: c.slider, ref: e.reference });
    });
  },
  aDg1: function(e, t, r) {
    "use strict";
    var n = r("mrSG"),
      a = (r("EsMY"), r("q1tI")),
      o = r("TSYQ"),
      s = r("K3s3"),
      c = r("nPPD"),
      i = r("dMmr"),
      l = Object(c.a)(s.a, i);
    var u = r("4Cm8"), d = r("5VK0");
    r.d(t, "a", function() {
      return f;
    });
    var p = Object(s.c)(function(e) {
        return a.createElement(
          "div",
          { className: l.slider, ref: e.reference },
          a.createElement("div", { className: l.inner }),
        );
      }),
      f = function(e) {
        function t() {
          var t = null !== e && e.apply(this, arguments) || this;
          return t._createClickHandler = function(e) {
            return function() {
              t.props.onSelect(e);
            };
          },
            t;
        }
        return Object(n.__extends)(t, e),
          t.prototype.render = function() {
            var e = this._generateDialogTabs();
            return a.createElement(
              "div",
              { className: d.scrollWrap },
              a.createElement("div", { className: d.headerBottomSeparator }),
              a.createElement(u.a, {
                isVisibleFade: Modernizr.mobiletouch,
                isVisibleButtons: !Modernizr.mobiletouch,
                isVisibleScrollbar: !1,
              }, a.createElement("div", { className: d.tabsWrap }, a.createElement(p, { className: d.tabs }, e))),
            );
          },
          t.prototype._generateDialogTabs = function() {
            var e = this, t = this.props, r = t.activeTabId, n = t.tabs;
            return n.allIds.map(function(t) {
              var c = r === t;
              return a.createElement(s.b, {
                key: t,
                className: o(d.tab, !c && d.withHover),
                isActive: c,
                onClick: e._createClickHandler(t),
              }, n.byId[t].title);
            });
          },
          t;
      }(a.PureComponent);
  },
  dMmr: function(e, t, r) {
    e.exports = { slider: "slider-2TOmsMP8", inner: "inner-21p4mP7K" };
  },
  nPPD: function(e, t, r) {
    "use strict";
    function n(e, t, r) {
      void 0 === r && (r = {});
      for (var n = Object.assign({}, t), a = 0, o = Object.keys(t); a < o.length; a++) {
        var s = o[a], c = r[s] || s;
        c in e && (n[s] = [e[c], t[s]].join(" "));
      }
      return n;
    }
    function a(e, t, r) {
      return void 0 === r && (r = {}), Object.assign({}, e, n(e, t, r));
    }
    r.d(t, "b", function() {
      return n;
    }),
      r.d(t, "a", function() {
        return a;
      });
  },
  vHME: function(e, t, r) {
    "use strict";
    r.d(t, "a", function() {
      return f;
    });
    var n = r("mrSG"),
      a = r("q1tI"),
      o = r("TSYQ"),
      s = r.n(o),
      c = (r("YFKU"), r("Iivm")),
      i = r("H172"),
      l = r("N5tr"),
      u = r("HWhk"),
      d = r("wt3x"),
      p = {
        reset: window.t("Reset Settings"),
        saveAsDefault: window.t("Save As Default"),
        defaults: window.t("Defaults"),
      },
      f = function(e) {
        function t(t) {
          var r = e.call(this, t) || this;
          return r._getItems = function() {
            var e = r.props.mode;
            return [r._getPlaceHolderItem("compact" === e)].concat(r._propertyItems);
          },
            r._handleResetToDefaults = function() {
              r.props.model.restorePropertiesForSource(r.props.source);
            },
            r._handleSaveAsDefaults = function() {
              r.props.source.properties().saveDefaults();
            },
            r._propertyItems = [{
              value: "reset",
              readonly: !0,
              content: a.createElement(l.b, {
                className: d.defaultsButtonItem,
                isActive: !1,
                label: p.reset,
                onClick: r._handleResetToDefaults,
              }),
            }, {
              value: "save",
              readonly: !0,
              content: a.createElement(l.b, {
                className: d.defaultsButtonItem,
                isActive: !1,
                label: p.saveAsDefault,
                onClick: r._handleSaveAsDefaults,
              }),
            }],
            r;
        }
        return Object(n.__extends)(t, e),
          t.prototype.render = function() {
            var e = this.props.mode;
            return a.createElement(i.a, {
              className: s()("normal" === e && d.defaultsButtonText),
              items: this._getItems(),
              hideArrowButton: "compact" === e,
              value: "defaults",
            });
          },
          t.prototype._getPlaceHolderItem = function(e) {
            return {
              value: "defaults",
              readonly: !0,
              content: "",
              selectedContent: e ? a.createElement(c.Icon, { className: d.defaultsButtonIcon, icon: u }) : p.defaults,
            };
          },
          t;
      }(a.PureComponent);
  },
  wt3x: function(e, t, r) {
    e.exports = {
      defaultsButtonText: "defaultsButtonText-1ycd-VvR",
      defaultsButtonItem: "defaultsButtonItem-3rYXMHYc",
      defaultsButtonIcon: "defaultsButtonIcon-1L3YZ47K",
    };
  },
}]);
