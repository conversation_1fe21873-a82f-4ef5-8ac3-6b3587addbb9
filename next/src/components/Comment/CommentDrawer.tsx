import clsx from 'clsx'
import Script from 'next/script'
import type React from 'react'
import { useEffect, useState } from 'react'
import { FaComments } from 'react-icons/fa6'
import CommentBox, {
  type CommentBoxProps,
} from '~/src/components/Comment/CommentBox'
import { getStoryID } from '~/src/components/Comment/CommentUtils'
import Drawer from '~/src/components/Drawer/Drawer'

/**
 * Create a CommentDrawer component
 * This component is a Drawer that contains a Comment Box
 * It is used to display the comments of a story
 * Also, it contains a button to open the Drawer
 *
 * @param storyID
 * @param category
 * @param className
 * @param elementID
 * @constructor
 */
const CommentDrawer: React.FC<CommentBoxProps> = ({
  storyID,
  category,
  className,
  elementID,
}: CommentBoxProps) => {
  // The state to show or hide the Drawer
  const [showDrawer, setShowDrawer] = useState<boolean>(false)

  useEffect(() => {
    // Check if the commentsOpened is saved in local storage
    const commentsOpened = localStorage.getItem('commentsOpenedRedirect')

    if (commentsOpened) {
      // Remove the Drawer state from local storage
      localStorage.removeItem('commentsOpenedRedirect')
      // Open the Drawer
      setShowDrawer(true)
    }
  })

  useEffect(() => {
    // Check if the Drawer is opened
    if (showDrawer) {
      // Save the Drawer state to local storage
      localStorage.setItem('commentsOpened', 'true')
    } else {
      // Remove the Drawer state from local storage
      localStorage.removeItem('commentsOpened')
    }
  }, [showDrawer])

  if (process.env.NEXT_PUBLIC_CORALTALK_ENABLED !== 'true') {
    return null
  }

  return (
    <>
      <button
        type={'button'}
        onClick={() => setShowDrawer(true)}
        className="relative inline-block"
      >
        <FaComments
          size={'24px'}
          className={clsx(
            className ? className : 'text-gray-400 hover:text-gray-700',
          )}
        />
        <span
          className="coral-count absolute right-0 top-0 inline-flex h-5 w-5 
                    -translate-y-1/2 translate-x-1/2 transform items-center
                    justify-center rounded-full bg-red-600 text-xs
                    font-bold leading-none text-white"
          data-coral-id={getStoryID(category, storyID)}
          data-coral-notext="true"
        >
          0
        </span>
      </button>

      <Script
        className="coral-script"
        src={`${process.env.NEXT_PUBLIC_CORALTALK_URL}/assets/js/count.js`}
        defer={true}
      />

      <Drawer
        title="Comments"
        opened={showDrawer}
        onClose={() => setShowDrawer(false)}
      >
        <CommentBox
          className={className}
          category={category}
          storyID={storyID}
          elementID={elementID}
        />
      </Drawer>
    </>
  )
}

export default CommentDrawer
