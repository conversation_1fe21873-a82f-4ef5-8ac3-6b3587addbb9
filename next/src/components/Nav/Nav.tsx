// import MoreItem from "./MoreItem/MoreItem";
import { List, Root, Viewport } from '@radix-ui/react-navigation-menu'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { IoMenu } from 'react-icons/io5'
import useScreenSize from '~/src/utils/useScreenSize'
import { FavoritesBar } from '../FavoritesBar/FavoritesBar'
import Account from './Account/Account'
import BuySellItem from './BuySellItem/BuySellItem'
import ChartsItem from './ChartsItem/ChartsItem'
import MarketsItem from './MarketsItem/MarketsItem'
import MiningItem from './MiningItem/MiningItem'
import MobileFooter from './MobileFooter/MobileFooter'
import styles from './Nav.module.scss'
import { NavLogo } from './NavLogo'
import NewsItem from './NewsItem/NewsItem'
import QuotesItem from './QuotesItem/QuotesItem'
import Search from './Search/Search'

const Nav = () => {
  const r = useRouter()
  const [mobileNavActivate, setMobileNavActivate] = useState(false)
  const { isDesktop } = useScreenSize()

  return (
    <>
      <Root
        orientation={!isDesktop ? 'vertical' : 'horizontal'}
        className={clsx(
          'relative w-full',
          'px-2',
          mobileNavActivate ? 'h-auto' : 'h-14',
          r.pathname.includes('/news/video') ? 'bg-[#0F181D]' : 'bg-ktc-black',
        )}
      >
        <div
          className={clsx(
            'mx-auto h-full w-full max-w-[1240px] px-2',
            'relative flex flex-wrap',
            'lg:flex-nowrap lg:justify-between',
          )}
        >
          <NavLogo />
          <List
            className={clsx(
              'left-0 mx-auto mt-20 h-full w-full',
              'order-last flex-col',
              'lg:order-2 lg:mt-0 lg:w-auto lg:flex-row',
              mobileNavActivate ? 'flex' : 'hidden lg:flex',
              // radix wraps this component in an unreachable parent div.. lol
              styles.radixList,
            )}
          >
            <BuySellItem />
            <QuotesItem />
            <ChartsItem />
            <MarketsItem />
            <NewsItem />
            <MiningItem />
            {/* <MoreItem /> */}
          </List>
          <MobileFooter />
          <List
            className={clsx(
              'h-14 max-h-full w-full',
              'z-[**********] flex justify-end',
              'order-2 self-start',
              'lg:order-last',
              // radix wraps this component in an unreachable parent div.. lol
              styles.iconsParentDiv,
            )}
          >
            <Search
              mobileNavActivate={mobileNavActivate}
              toggleMobileNav={() => setMobileNavActivate(!mobileNavActivate)}
            />
            <Account />
            <li className={clsx(styles.hamburgerContainer)}>
              <button
                type="button"
                onClick={() => setMobileNavActivate(!mobileNavActivate)}
              >
                <IoMenu size="28px" color="white" />
              </button>
            </li>
          </List>
        </div>
        {isDesktop && (
          <div className={clsx('relative z-[********] mt-1 w-full', 'mx-auto')}>
            <Viewport />
          </div>
        )}
      </Root>
      {/* dont show the favorites bar on video news paths */}
      {!r.pathname.includes('/video') ? <FavoritesBar /> : null}
    </>
  )
}

export default Nav
