import clsx from 'clsx'
import Link from 'next/link'
import React, { useEffect, useState } from 'react'
import type { Label } from '~/src/generated'
import { OpinionType } from '~/src/types/index'
import hrefMatcher from '~/src/utils/hrefMatcher'
import { teaserTimestamp } from '~/src/utils/teaser-timestamp'
import ArticleTeaserListItemTag from '../../ArticleTeaserListItemTag/ArticleTeaserListItemTag'
import styles from './ListItemOneLine.module.scss'

interface Props {
  title: string
  teaserHeadline: string
  source: string
  date: string
  tag?: Label
  isOdd: boolean
  isBold: boolean
  url: string
  opinionType?: string
  target?: '_blank' | '_parent' | '_self' | '_top'
}

const ListItemOneLine: React.FC<Props> = ({
  date,
  title,
  teaserHeadline,
  source,
  tag,
  isOdd,
  isBold,
  url,
  opinionType,
  target,
}) => {
  const containerStyle = [styles.container, isOdd && styles.odd]
    .filter((e) => e)
    .join(' ')

  const titleStyle = [styles.title, isBold && styles.bold]
    .filter((e) => e)
    .join(' ')

  const [isClient, setIsClient] = useState(false)

  if (!url || !title) {
    return null
  }

  useEffect(() => {
    setIsClient(true)
  }, [])

  return (
    <div className={containerStyle}>
      <div className="flex items-center">
        <ArticleTeaserListItemTag tag={tag} />{' '}
        {!hrefMatcher(url) ? (
          <a href={url} className={titleStyle}>
            <h3>{teaserHeadline ?? title}</h3>
          </a>
        ) : (
          <Link
            target={target}
            className={clsx(
              titleStyle,
              `${
                opinionType?.toLowerCase() ===
                OpinionType[OpinionType.KITCO].toLowerCase()
                  ? 'font-bold'
                  : ''
              }`,
            )}
            href={url}
          >
            <h3>{teaserHeadline ?? title}</h3>
          </Link>
        )}
      </div>
      <div>
        <span className={styles.source}>{source}</span>{' '}
        {isClient ? teaserTimestamp(date, 'MMM DD') : 'Loading...'}
      </div>
    </div>
  )
}

export default ListItemOneLine
