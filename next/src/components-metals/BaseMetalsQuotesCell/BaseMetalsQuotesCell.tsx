import { type FC, Suspense } from 'react'
import RelatedMetals from '~/src/components-metals/RelatedMetals/RelatedMetals'
import BidAskGrid from '~/src/components/BidAskGrid/BidAskGrid'
import BidAskGridMobile from '~/src/components/BidAskGrid/BidAskGridMobile'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import KitcoTable from '~/src/components/KitcoTable/KitcoTable'
import { Query } from '~/src/components/Query/Query'
import type { BaseMetalsQuery } from '~/src/generated'
import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import { metals } from '~/src/lib/metals-factory.lib'
import { useGetMarketStatus } from '~/src/utils/market-status.util'
import * as timestamps from '~/src/utils/timestamps'
import useScreenSize from '~/src/utils/useScreenSize'

const BaseMetalsQuotesCell: FC<{
  title?: string
  componentAlias?: 'bidAskGrid' | 'relatedMetals' | 'kitcoTable'
}> = ({ title, componentAlias }) => {
  // Check if we're in a mobile or tablet view
  const { isTablet, isMobile } = useScreenSize()

  // Get the currency symbol
  const { symbol } = useCurrency()

  // Get the market status
  const { data: dataStatus } = useGetMarketStatus()

  // Fetcher for the base metals data
  const fetcher = metals.baseMetals({
    variables: {
      currency: symbol ?? 'USD',
      timestamp: timestamps.current(),
    },
  })

  return (
    <ErrBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Query<BaseMetalsQuery> fetcher={fetcher}>
          {({ data, isFetching }) => {
            const dataAsArray = [
              data?.CopperPrice,
              data?.NickelPrice,
              data?.AluminumPrice,
              data?.ZincPrice,
              data?.LeadPrice,
            ]

            if (componentAlias === 'relatedMetals') {
              return <RelatedMetals data={data} />
            }

            if (componentAlias === 'kitcoTable') {
              return (
                <KitcoTable data={dataAsArray} title="Kitco Precious Metals" />
              )
            }
            return (
              <>
                {isTablet || isMobile ? (
                  <BidAskGridMobile
                    title={title}
                    values={!dataAsArray[0] === undefined ? [] : dataAsArray}
                    isBaseMetals={true}
                    isStatus={dataStatus?.GetMarketStatus?.status === 'OPEN'}
                  />
                ) : (
                  <BidAskGrid
                    isLoading={isFetching}
                    title={title}
                    data={!dataAsArray[0] === undefined ? [] : dataAsArray}
                    isBaseMetals={true}
                    isStatus={dataStatus?.GetMarketStatus?.status === 'OPEN'}
                  />
                )}
              </>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default BaseMetalsQuotesCell
