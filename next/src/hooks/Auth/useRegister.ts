import type React from 'react'
import { useEffect, useState } from 'react'
import { useDebounce, useDebouncedCallback } from 'use-debounce'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { useRecaptchaVerification } from '~/src/features/auth/recapcha'
import {
  sanitizeDisplayName,
  sanitizeEmail,
  sanitizeUsername,
} from '~/src/features/auth/sanitize'
import Validator from '~/src/features/auth/validator'
import { getUserByEmail } from '~/src/services/discourse/api'
import firebaseError from '~/src/services/firebase/errors'
import { register } from '~/src/services/firebase/service'

/**
 * Errors for the RegisterForm component.
 */
interface RegisterErrors {
  email?: string | undefined
  display_name?: string | undefined
  username?: string | undefined
  newsletter?: string | undefined
}

/**
 * Custom hook for the register logic.
 */
const useRegisterForm = (
  onCustomTitle: (title: string) => void,
  onError: (error: string) => void,
  onIsRegistering: (isRegistering: boolean) => void,
  redirectToForum: boolean,
  userData: UserData | undefined,
) => {
  const debounceInterval = 300

  // Form fields
  const [email, setEmail] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [username, setUsername] = useState('')
  const [displayUsername, setDisplayUsername] = useState('')
  const [password, setPassword] = useState('')
  const [newsletter, setNewsletter] = useState(false)

  // Debounced fields
  const [debouncedEmail] = useDebounce(email, debounceInterval)
  const [debouncedDisplayName] = useDebounce(displayName, debounceInterval)
  const [debouncedUsername] = useDebounce(username, debounceInterval)
  const [debouncedPassword] = useDebounce(password, debounceInterval)

  // Using an object to track errors for each field
  const [errors, setErrors] = useState<RegisterErrors>({})
  const [passwordError, setPasswordError] = useState<boolean>(false)

  // Check if the form is valid
  const [formIsValid, setFormIsValid] = useState(false)

  // Show the verification message
  const [showVerificationMessage, setShowVerificationMessage] = useState(false)

  // State for the user is registering
  const [isRegistering, setIsRegistering] = useState(false)

  // State for forum data: display name and username
  const [isForumDisplayName, setIsForumDisplayName] = useState(false)
  const [isForumUsername, setIsForumUsername] = useState(false)

  // Custom hook to verify the user with reCAPTCHA
  const verifyUserRecaptcha = useRecaptchaVerification()

  /**
   * Set the user data if it exists
   */
  useEffect(() => {
    if (userData) {
      setEmail(userData?.email ? userData.email : '')
      setDisplayName(userData?.name ? userData.name : '')
      setDisplayUsername(userData?.username ? userData.username : '')
    }
  }, [userData])

  /**
   * Set the user is registering state
   */
  useEffect(() => {
    onIsRegistering(isRegistering)
  }, [isRegistering])

  /**
   * Debounced function to check if the form is valid
   */
  const debouncedCheckFormIsValid = useDebouncedCallback(() => {
    checkFormIsValid()
  }, debounceInterval)

  /**
   * Check if the form is valid when errors change
   * wrapped in a useEffect to avoid infinite loops
   */
  useEffect(() => {
    debouncedCheckFormIsValid()
  }, [errors, passwordError, displayName, username, email])

  /**
   * Check if we have a forum user with the provided email and try to
   * get the user data
   */
  useEffect(() => {
    checkForumData()
  }, [debouncedEmail])

  /**
   * Clean the forum data
   * This function is used to clean the forum data when the email is changed
   *
   * @param showErrors
   */
  const cleanForumData = (showErrors = false) => {
    if (isForumUsername) {
      updateField('username', '', showErrors)
      setIsForumUsername(false)
    }

    if (isForumDisplayName) {
      updateField('display_name', '', showErrors)
      setIsForumDisplayName(false)
    }
  }

  /**
   * Set the forum data to the form fields if it exists
   *
   * @param userData
   */
  const setForumData = (userData: UserData) => {
    if (userData?.name) {
      setDisplayName(userData.name)
      setIsForumDisplayName(true)
    }

    if (userData?.username) {
      setUsername(userData.username)
      setDisplayUsername(userData.displayUsername ?? userData.username)
      setIsForumUsername(true)
    }

    // If we have forum data, set the custom title
    if (userData?.username && userData?.name) {
      onCustomTitle('Create a new password')
    }
  }

  /**
   * Check if we have forum data
   */
  const checkForumData = async () => {
    // If the email is not valid, clean the forum data
    if (!email || !(await validateField('email', email, false))) {
      cleanForumData()
      return
    }

    // Get the user data from the forum
    const userData = await getUserByEmail(email)

    // First clean the old forum data (if exists)
    cleanForumData()

    // Set the new forum data to the form fields
    setForumData(userData)
  }

  /**
   * Check if the form is valid, show errors if needed
   *
   * This is used to check if the form is valid when the form is submitted
   * Also used to check if the form is valid when the form is changed
   *
   * @param showErrors
   */
  const checkFormIsValid = async (showErrors = false) => {
    const formIsValid =
      Object.values(errors).every((error) => error === undefined) &&
      (await validateField('email', email, showErrors)) &&
      (await validateField('display_name', displayName, showErrors)) &&
      (await validateField('username', username, showErrors)) &&
      password !== '' &&
      passwordError === false

    setFormIsValid(formIsValid)

    return formIsValid
  }

  /**
   * Validate a field based on the field name and value
   * This function is used to validate each field in the form
   * It sets the error message for the field if any
   * It also returns a boolean value to indicate if the field is valid
   * This function is called on field change
   *
   * @param fieldName
   * @param value
   * @param setError
   */
  const validateField = async (
    fieldName: string,
    value: string,
    setError = true,
  ) => {
    // Initialize the error message
    let errorMessage: string | undefined

    switch (fieldName) {
      case 'email':
        errorMessage = !Validator.validateEmail(value)
          ? 'Please enter a valid email.'
          : undefined
        break
      case 'display_name':
        errorMessage = !Validator.validateDisplayName(value)
          ? 'Display name cannot be empty and must be between 3 and 60 characters.'
          : undefined

        if (errorMessage) {
          setIsForumDisplayName(false)
        }
        break
      case 'username':
        errorMessage = !Validator.validateUsername(value)
          ? 'Username name cannot be empty, must be between 3 and 60 characters, and can only contain letters, numbers, dots and underscores.'
          : (await Validator.usernameExists(value, email))
            ? 'Username already exists. Please choose another username.'
            : undefined

        if (errorMessage) {
          setIsForumUsername(false)
        }
        break
      default:
        errorMessage = undefined
    }

    // If setError is false, return the validation result
    if (!setError) {
      return errorMessage === undefined
    }

    // Set the error message for the field
    setErrors((prevErrors) => ({
      ...prevErrors,
      [fieldName]: errorMessage,
    }))
  }

  /**
   * Update the field value and validate the field
   *
   * @param {string} fieldName
   * @param {string} value
   * @param {boolean} showErrors
   * @param {boolean} validate
   */
  const updateField = (
    fieldName: string,
    value: string | boolean,
    showErrors = true,
    validate = true,
  ) => {
    let sanitizedValue = undefined

    // Update the field state
    switch (fieldName) {
      case 'email':
        sanitizedValue = sanitizeEmail(value as string)
        setEmail(sanitizedValue)
        break
      case 'display_name':
        sanitizedValue = sanitizeDisplayName(value as string)
        setDisplayName(sanitizedValue)
        break
      case 'username': {
        const { original, lowercase } = sanitizeUsername(value as string)
        sanitizedValue = lowercase
        setUsername(lowercase)
        setDisplayUsername(original)
        break
      }
      case 'password':
        setPassword(value as string)
        break
      case 'newsletter':
        setNewsletter(value as boolean)
    }

    if (validate) {
      // Validate the field
      validateField(
        fieldName,
        sanitizedValue ? sanitizedValue : value,
        showErrors,
      )
    }
  }

  /**
   * Debounced function to validate the email field
   */
  useEffect(() => {
    if (!debouncedEmail) return
    // Validate the field
    validateField('email', debouncedEmail, true)
  }, [debouncedEmail])

  /**
   * Debounced function to validate the username field
   */
  useEffect(() => {
    if (!debouncedUsername) return
    // Validate the field
    validateField('username', debouncedUsername, true)
  }, [debouncedUsername])

  /**
   * Debounced function to validate the display name field
   */
  useEffect(() => {
    if (!debouncedDisplayName) return
    // Validate the field
    validateField('display_name', debouncedDisplayName, true)
  }, [debouncedDisplayName])

  /**
   * Debounced function to validate the password field
   */
  useEffect(() => {
    if (!debouncedPassword) return
    // Validate the field
    validateField('password', debouncedPassword, true)
  }, [debouncedPassword])

  /**
   * Handle change event for input fields
   * This function is called when the value of an input field changes
   * It updates the state of the corresponding field
   * It also calls the validateField function to validate the field
   *
   * @param e
   */
  const handleChange = (e) => {
    // Get the field name and value
    const fieldName = e.target.id
    const value =
      e.target.type === 'checkbox'
        ? e.target.checked
        : e.target.value.toString()

    updateField(fieldName, value, true, false)
  }

  /**
   * Handle register event
   * This function is called when the form is submitted
   * It checks if the form is valid and then proceeds with the registration process
   *
   * @param e
   */
  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    // Prevent the default form submission
    e.preventDefault()

    // Set the user is registering state
    setIsRegistering(true)

    // Clear any previous errors
    onError('')

    try {
      // Validate the form
      if ((await checkFormIsValid(true)) === false) {
        onError('Form is not valid. Please check the fields.')
        return
      }

      // Verify the user with reCAPTCHA
      const recaptchaVerified = await verifyUserRecaptcha()

      // If the reCAPTCHA verification fails, show an error message and return.
      if (!recaptchaVerified) {
        onError('The reCAPTCHA verification failed. Please try again.')
        return
      }

      // Register the user
      await register(
        email,
        password,
        {
          displayName,
          displayUsername,
          username,
          newsletter,
        },
        redirectToForum,
      )

      // Show the verification message
      setShowVerificationMessage(true)
    } catch (error) {
      console.error('Error trying to register:', error)

      // Handle Firebase error by showing an appropriate error message
      const errorMessage = firebaseError(
        error.code,
        'Failed to register. Please check your credentials and try again.',
      )

      // Show the error message to the user
      onError(
        error.message === 'email/already-in-use'
          ? 'Email already in use. Please login or use a different email'
          : errorMessage,
      )
    } finally {
      setIsRegistering(false)
    }
  }

  return {
    displayName,
    displayUsername,
    email,
    errors,
    formIsValid,
    handleChange,
    handleRegister,
    isForumDisplayName,
    isForumUsername,
    isRegistering,
    newsletter,
    password,
    passwordError,
    setPasswordError,
    showVerificationMessage,
    username,
  }
}

export default useRegisterForm
