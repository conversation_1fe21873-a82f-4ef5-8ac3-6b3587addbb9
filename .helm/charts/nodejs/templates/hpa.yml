{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ template "templ.fullname" . }}
  labels:
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ template "templ.fullname" . }}

  # Configure the minimum number of replicas
  minReplicas: {{ .Values.autoscaling.minReplicas | default 2 }}

  # Configure the maximum number of replicas
  maxReplicas: {{ .Values.autoscaling.maxReplicas | default 10 }}

  metrics:
    # CPU Utilization target in percentage
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targets.cpuUtilizationPercentage | default 80 }}

    # Memory Utilization target in percentage
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targets.memoryUtilizationPercentage | default 80 }}

  # Configure scale-up and scale-down behavior for the HPA
  behavior:
    scaleUp:
      stabilizationWindowSeconds: {{ .Values.autoscaling.scaleUpStabilizationWindowSeconds | default 0 }}
      policies:
      - type: Percent
        value: {{ .Values.autoscaling.scaleUpPercent | default 100 }}
        periodSeconds: {{ .Values.autoscaling.scaleUpPeriodSeconds | default 15 }}
    scaleDown:
      stabilizationWindowSeconds: {{ .Values.autoscaling.scaleDownStabilizationWindowSeconds | default 300 }}
      policies:
      - type: Percent
        value: {{ .Values.autoscaling.scaleDownPercent | default 50 }}
        periodSeconds: {{ .Values.autoscaling.scaleDownPeriodSeconds | default 60 }}

  # Optionally, include additional metrics for scaling (e.g., custom metrics)
  # Uncomment and modify the below section if needed
  # metrics:
  #   - type: Pods
  #     pods:
  #       metricName: http_requests_per_second
  #       target:
  #         type: AverageValue
  #         averageValue: 100m
{{- end }}