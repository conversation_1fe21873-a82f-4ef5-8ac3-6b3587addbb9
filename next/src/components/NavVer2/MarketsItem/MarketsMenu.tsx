import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const markets: SectionItems[] = [
  {
    name: 'DOW',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$DOWI',
  },
  {
    name: 'NASDAQ',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$NASX',
  },
  {
    name: 'S&P 500',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$SPX',
  },
  {
    name: 'NYSE',
    href: '/markets/indices/[symbol]',
    as: '/markets/indices/$NYA',
  },
]

export const mining: SectionItems[] = [
  { name: 'Gold', href: '/markets/mining/-MIGL' },
  { name: 'Silver', href: '/markets/mining/-MISI' },
  { name: 'Iron', href: '/markets/mining/-MIIR' },
  { name: 'Base Metals', href: '/markets/mining/-MEPF' },
  { name: 'Non-Ferrous', href: '/markets/mining/-MINF' },
]

export const metalsMarket: SectionItems[] = [
  { name: 'Kitco Global Index', href: '/markets/kitco-gold-index' },
]

export const cryptos: SectionItems[] = [
  {
    name: 'Bitcoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/bitcoin',
  },
  {
    name: 'Ethereum',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ethereum',
  },
  {
    name: 'Litecoin',
    href: '/price/crypto/[name]',
    as: '/price/crypto/litecoin',
  },
  {
    name: 'Monero',
    href: '/price/crypto/[name]',
    as: '/price/crypto/monero',
  },
  {
    name: 'Ripple',
    href: '/price/crypto/[name]',
    as: '/price/crypto/ripple',
  },
  {
    name: 'Dash',
    href: '/price/crypto/[name]',
    as: '/price/crypto/dash',
  },
]

export const more: SectionItems[] = [
  { name: 'Metals', href: '/markets/metals' },
  { name: 'Stocks Gainers and Losers', href: '/markets/stocks' },
]

function MarketsMenu() {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Markets Overview"
          titleUrl="/markets"
          items={markets}
        />
        <SectionList title="Forex" titleUrl="/price/forex" />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList title="Mining" titleUrl="/markets/mining" items={mining} />
      </Navigation.SubMenuColumn>
      <Navigation.SubMenuColumn>
        <SectionList
          title="Metals Market"
          titleUrl="/markets/metals"
          items={metalsMarket}
        />
        <SectionList
          title="Active Stocks, Gainers & Losers"
          titleUrl="/markets/stocks"
        />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default MarketsMenu
