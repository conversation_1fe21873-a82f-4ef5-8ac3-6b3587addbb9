import commodityCategories from '~/src/data/GoldIndex/CommodityCategories'
import { useGIEnergyData } from '~/src/hooks/GoldIndex/useGIEnergyData'
import { useGIMetalsData } from '~/src/hooks/GoldIndex/useGIMetalsData'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import { formatPercentage } from '~/src/utils/Prices/formatPercentage'
import { formatPrice } from '~/src/utils/Prices/formatPrice'

/**
 * Get the gold index data
 *
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 */
const useGoldIndexData = (onlyPreciousMetals?: boolean) => {
  // Get the metal and energy data
  const metalData = useGIMetalsData(onlyPreciousMetals)
  const energyData = onlyPreciousMetals ? null : useGIEnergyData()

  return createTableData(metalData, energyData, onlyPreciousMetals)
}

/**
 * Create the table data
 *
 * @param metalData
 * @param energyData
 * @param onlyPreciousMetals
 */
function createTableData(
  metalData: CommodityData[],
  energyData: CommodityData,
  onlyPreciousMetals: boolean,
): CommodityData[] {
  const tableData = generateTableData(metalData, energyData, onlyPreciousMetals)

  return formatData(tableData)
}

/**
 * Formats an array of CommodityData objects by categorizing each commodity
 * and formatting specific nested properties.
 *
 * @param {CommodityData[]} data - The array of CommodityData objects to format.
 * @returns {CommodityData[]} - A new array of CommodityData objects with formatted values.
 */
function formatData(data: CommodityData[]): CommodityData[] {
  // Return an empty array if there is no data
  if (!data || data.length <= 0) return []

  return data.map((item: CommodityData) => {
    const category = categorizeCommodity(item.commodity)
    return {
      ...item,
      lastBid: {
        ...item.lastBid,
        bid: formatPrice({ value: item.lastBid.bidVal, category }),
      },
      changeDueToUSD: {
        ...item.changeDueToUSD,
        change: formatPrice({ value: item.changeDueToUSD.changeVal, category }),
        percentage: formatPercentage({
          value: item.changeDueToUSD.percentageVal,
        }),
      },
      changeDueToTrade: {
        ...item.changeDueToTrade,
        change: formatPrice({
          value: item.changeDueToTrade.changeVal,
          category,
        }),
        percentage: formatPercentage({
          value: item.changeDueToTrade.percentageVal,
        }),
      },
      totalChange: {
        ...item.totalChange,
        change: formatPrice({ value: item.totalChange.changeVal, category }),
        percentage: formatPercentage({ value: item.totalChange.percentageVal }),
      },
    }
  })
}

/**
 * Categorize the commodity based on the category
 *
 * @param {string} commodity - The commodity to categorize
 *
 * @returns {string} The category of the commodity
 */
function categorizeCommodity(commodity: string): string {
  for (const category in commodityCategories) {
    if (commodityCategories[category].includes(commodity)) {
      return category
    }
  }
  return null
}

/**
 * Generate the table data from the API data
 *
 * @param {CommodityData[]} metalData - The metal data
 * @param {CommodityData} energyData - The energy data
 * @param {boolean} onlyPreciousMetals - Whether to get only precious metals
 *
 * @returns {CommodityData[]} The table data
 */
const generateTableData = (
  metalData: CommodityData[],
  energyData: CommodityData,
  onlyPreciousMetals?: boolean,
): CommodityData[] => {
  // No need to process the data if there is only precious metals
  if (onlyPreciousMetals) {
    return metalData
  }

  // Return an empty array if there is no data
  if (!metalData || metalData.length <= 0) {
    return []
  }

  // Todo: This maybe can be removed in the future when the API is fixed
  // Todo: And we can fetch the data in the correct order using GraphQL

  // Find the index of copper entry
  const copperIndex = metalData.findIndex(
    (entry) => entry.commodity === 'Copper',
  )

  // Insert the energy entry before the copper entry
  if (copperIndex !== -1) {
    metalData.splice(copperIndex, 0, energyData)
  } else {
    metalData.push(energyData)
  }

  return metalData
}

export { createTableData, useGoldIndexData }
