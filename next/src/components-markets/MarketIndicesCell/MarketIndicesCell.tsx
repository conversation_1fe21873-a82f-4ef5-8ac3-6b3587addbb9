import { Suspense } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import MarketIndices from '~/src/components/MarketIndices/MarketIndices'
import { Query } from '~/src/components/Query/Query'
import { markets } from '~/src/lib/markets-factory.lib'
import * as timestamps from '~/src/utils/timestamps'

const IndicesCell = () => {
  const fetcher = markets.regionIndices({
    variables: {
      timestamp: timestamps.current(),
    },
  })

  return (
    <ErrBoundary errorTitle="Market Indices">
      <Suspense fallback={<div>Loading...</div>}>
        <Query fetcher={fetcher}>
          {(res) => {
            return (
              <div>
                <MarketIndices data={res.data} />
              </div>
            )
          }}
        </Query>
      </Suspense>
    </ErrBoundary>
  )
}

export default IndicesCell
