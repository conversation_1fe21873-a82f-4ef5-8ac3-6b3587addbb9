sub vcl_init {
    dict.load("/etc/varnish/redirects.dict");
    
    new www_resolver = dynamic.resolver();
    # when we specified .share property to `HOST`
    # then host header is as same as the host that specified when calling the backend() function later on
    # IF you specified .share = `DIRECTOR` which is default value, you have to add in the .host_header as well
    # @see https://raw.githubusercontent.com/nigoroll/libvmod-dynamic/6.0/src/vmod_dynamic.vcc for more details
    new www_dir = dynamic.director(
        port = "443",
        share = HOST,
        resolver = www_resolver.use(),
        whitelist = ipv4_only,
        probe = www_probe,
        ttl = 5m
    );

    www_dir.debug(true);
}