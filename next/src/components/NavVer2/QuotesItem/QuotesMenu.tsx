import Link from 'next/link'
import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const fixes: SectionItems[] = [
  { name: 'Kitco Fixes', href: '/price/fixes/kitco-fix' },
  { name: 'London Fix', href: '/price/fixes/london-fix' },
  { name: 'Shanghai Gold Benchmark', href: '/price/fixes/shanghai-benchmark' },
]

export const liveCharts: SectionItems[] = [
  {
    name: 'Gold',
    href: '/charts/[commodity]',
    as: '/charts/gold',
  },
  {
    name: 'Silver',
    href: '/charts/[commodity]',
    as: '/charts/silver',
  },
  {
    name: 'Platinum',
    href: '/charts/[commodity]',
    as: '/charts/platinum',
  },
  {
    name: 'Palladium',
    href: '/charts/[commodity]',
    as: '/charts/palladium',
  },
  {
    name: 'Rhodium',
    href: '/charts/[commodity]',
    as: '/charts/rhodium',
  },
]

const QuotesMenu = () => {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <Link href="/price/precious-metals">
          <h4 className="font-mulish mb-1 pt-1 text-sm font-normal leading-5 text-white hover:underline">
            All Metal Quotes
          </h4>
        </Link>
        <SectionList title="Classic Live Charts" items={liveCharts} />
        <SectionList title="Fix Prices" items={fixes} />
        <SectionList
          title="Text Quotes"
          titleUrl={'/price/precious-metals/text-quotes'}
        />
        <SectionList
          title="Free Web Quote Banners"
          titleUrl={'https://www.kitconet.com/price/'}
          isExternalLink={true}
        />
        <SectionList
          title="Free Charts on Your Website"
          titleUrl={'https://www.kitconet.com/'}
          isExternalLink={true}
        />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default QuotesMenu
