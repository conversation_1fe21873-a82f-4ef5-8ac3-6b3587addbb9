import { getDatabase as _getDatabase } from '@firebase/database'
import { type FirebaseApp, getApp, getApps, initializeApp } from 'firebase/app'
import { type Auth, getAuth } from 'firebase/auth'
import * as process from 'process'

// Todo: We should move this to server side for security reasons
const config = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
}

// Initialize Firebase
let firebaseApp: FirebaseApp

// Check if Firebase app is already initialized
if (!getApps().length) {
  firebaseApp = initializeApp(config)
} else {
  firebaseApp = getApp()
}

// Get the Auth service for the default app
const auth: Auth = getAuth(firebaseApp)

/**
 * Get the Firestore service for the default app
 */
const getDatabase = () => {
  // Get a reference to the Firestore service using the default app
  return _getDatabase(firebaseApp)
}

export { auth, firebaseApp, getDatabase }
