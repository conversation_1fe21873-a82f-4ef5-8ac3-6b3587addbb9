apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rss-ingress
  {{- if .Values.rss.ingressAnnotations }}
  annotations:
{{ toYaml .Values.rss.ingressAnnotations | indent 4 }}
  {{- end }}
spec:
  ingressClassName: nginx
  {{- if .Values.rss.tls.enabled }}
  tls:
    - hosts:
      - {{ .Values.rss.tls.host }}
      secretName: rss-tls
  {{- end }}
  rules:
    - host: {{ .Values.rss.ingressHost }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nginx-rss-svc
                port:
                  number: 80
