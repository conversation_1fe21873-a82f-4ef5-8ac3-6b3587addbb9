import clsx from 'clsx'
import dayjs from 'dayjs'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import { AdvertisingSlot } from 'react-advertising'
import MarketIndicesCell from '~/src/components-markets/MarketIndicesCell/MarketIndicesCell'
import OtherIndicesCell from '~/src/components-markets/OtherIndicesCell/OtherIndicesCell'
import AllMetalQuotesCell from '~/src/components-metals/AllMetalQuotesCell/AllMetalQuotesCell'
import GoldRatiosCell from '~/src/components-metals/GoldRatiosCell/GoldRatiosCell'
import LondonFixCell from '~/src/components-metals/LondonFixCell/LondonFixCell'
import ShanghaiGold from '~/src/components-metals/ShanghaiGold/ShanghaiGold'
import LatestNewsCell from '~/src/components-news/LatestNewsCell/LatestNewsCell'
import BuySellButton from '~/src/components/BuySellButton/BuySellButton'
import Divider from '~/src/components/Divider/Divider'
import KGX from '~/src/components/KGX/KGX'
import Layout from '~/src/components/Layout/LayoutLarger'
import { GoldIndicators } from '~/src/components/gold-indicators/gold-indicators.component'
import GoldSilverNews from '~/src/components/gold-silver-news/gold-silver-news.component'
import { InvestmentTrends } from '~/src/components/investment-trends/investment-trends.component'
import { NewsMiningTrendsCell } from '~/src/components/news-mining-trends/news-mining-trends.component'
import { GoldIndex } from '~/src/lib/GoldIndex/Queries'
import { markets } from '~/src/lib/markets-factory.lib'
import { metals } from '~/src/lib/metals-factory.lib'
import { news } from '~/src/lib/news-factory.lib'
import gridAreas from '~/src/styles/gridAreas.module.scss'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import * as timestamps from '~/src/utils/timestamps'
import styles from './all-metal-quotes.module.scss'

export const getServerSideProps: GetServerSideProps = async (c) => {
  const { dehydratedState } = await ssrQueries({
    ctxRes: c.res,
    queries: [
      metals.allMetalsQuote({
        variables: {
          timestamp: timestamps.current(),
          currency: 'USD',
        },
      }),
      markets.regionIndices({
        variables: {
          timestamp: timestamps.current(),
        },
      }),
      metals.londonFix({
        variables: {
          year: dayjs().year().toString(),
        },
        options: {},
      }),
      news.nodeListQueue({
        variables: {
          limit: 10,
          offset: 0,
          queueId: 'latest_news',
        },
      }),
      metals.shanghaiFix({
        variables: {
          timestamp: timestamps.current(),
          symbol: 'SHAU',
          currency: 'CNY',
        },
      }),
      GoldIndex.goldIndexWidget({
        variables: {
          timestamp: timestamps.current(),
          currency: 'USD',
        },
      }),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const AllMetalQuotes: NextPage = () => {
  return (
    <Layout title="Gold Spot Prices | Silver Prices | Platinum & Palladium | KITCO">
      <Head>
        <meta
          name="description"
          content="Live Spot Prices for Gold, Silver, Platinum, Palladium and Rhodium in ounces, grams, kilos and tolas in all major currencies."
        />
      </Head>
      <h1
        className="pl-2 text-[32px] uppercase text-kitco-black md:text-[48px]"
        style={{ fontFamily: 'Bebas Neue' }}
      >
        Precious Metals
      </h1>
      <div className={clsx('px-2', styles.tabletGridOrder)}>
        <div
          className={clsx(
            'hidden desktop:flex desktop:w-[200px] desktop:flex-col desktop:gap-5',
          )}
        >
          <MarketIndicesCell />
          <AdvertisingSlot
            id={'left-rail-1'}
            className="mx-auto h-[600px] w-[160px] no-print"
          />
          <ShanghaiGold />
          <GoldIndicators />
          <AdvertisingSlot
            id={'left-rail-2'}
            className="mx-auto h-[600px] w-[160px] no-print"
          />
          <OtherIndicesCell />
        </div>
        <div
          className={clsx(
            'contents desktop:flex desktop:flex-col desktop:gap-5',
          )}
        >
          <KGX />
          <AllMetalQuotesCell title="World Spot Price" />
          <BuySellButton
            width={240}
            containerClassName="flex justify-center pt-5"
            buttonClassName="text-[14px]"
          />
          <LondonFixCell />
          <div className={clsx('block desktop:hidden')}>
            <AdvertisingSlot
              id={'banner-2'}
              className="mx-auto flex justify-center items-center mb-8 h-[280px] w-[336px]
              tablet:h-[90px] tablet:w-[728px] no-print"
            />
          </div>
          <div className={clsx('hidden desktop:block')}>
            <AdvertisingSlot
              id={'oop'}
              className="mx-auto flex min-h-[439px] min-w-[100%] justify-center no-print"
            />
          </div>
          <AllMetalQuotesCell title="New York Spot Price" />
          <BuySellButton
            width={240}
            containerClassName="flex justify-center pt-5"
            buttonClassName="text-[14px]"
          />
          <GoldRatiosCell />
          <AdvertisingSlot
            id={'banner-3'}
            className="mx-auto flex justify-center items-center mb-8 h-[280px] w-[336px]
            tablet:h-[90px] tablet:w-[728px] no-print"
          />
          <Divider />
          <div
            className={clsx(
              'flex grid-cols-7 flex-col gap-10 pb-5 desktop:grid',
              gridAreas.ll,
            )}
          >
            <div className="col-span-4">
              <NewsMiningTrendsCell />
            </div>
            <div className="col-span-3">
              <InvestmentTrends />
            </div>
          </div>
          <Divider className="hidden desktop:block" />
          <GoldSilverNews />
        </div>
        <div
          className={clsx('hidden desktop:flex desktop:flex-col desktop:gap-5')}
        >
          <AdvertisingSlot
            id={'right-rail-amq'}
            className="mx-auto h-[250px] w-[300px]
            desktop:h-[600px]
            amqdAds:w-[336px] amqdAds:flex amqdAds:justify-center no-print"
          />
          <LatestNewsCell />
          <AdvertisingSlot
            id={'right-rail-2-hp'}
            className="sticky top-[100px] mx-auto h-[600px] w-[300px]
            amqdAds:w-[336px] amqdAds:flex amqdAds:justify-center no-print"
          />
        </div>
      </div>
      <AdvertisingSlot
        id={'footer'}
        className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
      />
    </Layout>
  )
}

export default AllMetalQuotes
