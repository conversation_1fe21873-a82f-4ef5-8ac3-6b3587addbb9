/* 0. Header Bar - Mobile style */
.headerMobile {
  @apply relative flex items-center justify-between text-white;
  background-color: #373737;
  height: 37px;
  padding: 15px 10px;
  margin: 1rem 1rem 0 1rem;
  border-radius: 0;
}

.headerMobileIconContainer {
  position: relative;
}

.headerMobileTitle {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #ffffff;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.headerMobileIcon {
  width: 21px;
  height: 21px;
  color: #ffffff;
}

/* 1. Wrapper */
.wrapperMobile {
  @apply mx-auto my-6 max-w-md overflow-hidden rounded-md bg-white; /* md = ~ 480px */
  border: 1px solid #e5e7eb;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.12); /* Same shadow as desktop */
}

/* 1a. Wrapper for Landing Page - Sharp corners, no shadow */
.wrapperMobileLanding {
  @apply mx-auto my-6 max-w-md overflow-hidden bg-white; /* md = ~ 480px */
  border: 1px solid #e5e7eb;
  border-radius: 0; /* Sharp corners for landing page */
  /* No shadow for landing page variant */
}

/* 2. Controls - Mobile Layout */
.controlsMobile {
  @apply flex px-4;
  padding-top: 8px;
  padding-bottom: 8px;
  gap: 15px;
}

.leftColumnMobile {
  @apply flex flex-col;
  gap: 8px;
  flex: 1;
}

.rightColumnMobile {
  @apply flex items-center justify-center text-center;
  width: 40%;
  align-self: stretch;
}

.topRowMobile {
  @apply flex items-center;
  padding: 8px 0;
}

.bottomRowMobile {
  @apply flex justify-start space-x-4;
}

.mobileInfoLabel {
  font-family: 'Mulish', sans-serif;
  font-weight: 300;
  font-size: 11px;
  line-height: 14px;
  color: #000000;
  text-align: right;
  flex: 1;
  margin-left: 15px;

  strong {
    font-weight: 600;
  }
}

.mobileDatePicker {
  width: 200px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
}

.mobileCurrencySelect {
  width: 125px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
}

.mobileUomSelect {
  width: 60px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
}

/* 3. Card list */
.cardsMobile {
  @apply divide-y divide-gray-200; /* horizontal separators */
  margin-top: 0; /* No top padding/gap for cards */
  padding-top: 0;
}

/* 4. Single Card */
.cardMobile {
  @apply bg-white px-4 pb-4;
  padding-top: 0;
  border: none;
}

/* 4a. Card Header */
.cardHeader {
  @apply flex items-center bg-gray-50 p-2;
  border-top: 1px solid #e5e7eb;
  border-radius: 0;
}

.cardTitle {
  @apply flex flex-1 items-center justify-end;
}
.cityName {
  @apply text-base font-semibold;
}
.cityTimeContainer {
  @apply ml-3 text-right;
}
.timestamp {
  @apply text-sm text-gray-500;
}

/* 4b. Commodity list */
.commodityList {
  @apply mt-3 space-y-2;
}
.commodityItem {
  @apply flex justify-between;
}
.commodityLabel {
  @apply text-sm font-medium uppercase text-gray-900;
}
.commodityValue {
  @apply text-sm text-gray-900;
}
.commodityValue.disabled {
  @apply text-gray-400;
}
/* Additional disabled state for NY hub */
.disabled {
  @apply text-gray-400;
}

.countdown {
  font-family: 'Lato', sans-serif;
  font-weight: 300;
  font-size: 12px;
  line-height: 16px;
  color: #4f81bd;
}

.blueText {
  color: #4f81bd;
}

.disabledOverlay {
  background-color: #f1f5fa;
  border-radius: 8px;
  padding: 0.5rem;
}

/* Tool Drawer */
.toolDrawer {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 225px;
  height: 125px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 18px 18px 14px 18px;
}

.toolDrawerTitle {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #000000;
  text-align: center;
  margin-bottom: 12px;
}

.toolDrawerIcons {
  @apply flex items-center justify-center space-x-3;
}

.toolDrawerIcon {
  width: 21px;
  height: 21px;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.toolDrawerIconLarge {
  width: 23px;
  height: 23px;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.toolDrawerSeparator {
  width: 0.7px;
  height: 21px;
  background-color: #d9d9d9;
}

/* Removed overlay - no screen fade */
